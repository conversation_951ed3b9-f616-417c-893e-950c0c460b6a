/**
 * Test script to verify enhanced room_info_updated event handling
 * This simulates the enhanced event structure with gameSpecificData
 */

const io = require('socket.io-client');

// Connect to the socket server
const socket = io('http://localhost:3001', {
  auth: {
    token: 'test-token' // You'll need a valid token
  }
});

socket.on('connect', () => {
  console.log('Connected to socket server');
  
  // Simulate enhanced room_info_updated event with gameSpecificData
  setTimeout(() => {
    console.log('Simulating enhanced room_info_updated event...');
    
    const enhancedRoomData = {
      room: {
        id: 'test-room-123',
        name: 'Test Prize Wheel Room',
        gameType: 'prizewheel',
        status: 'waiting',
        playerCount: 3,
        maxPlayers: 8,
        betAmount: 100, // 100 cents = $1.00
        prizePool: 300,
        isPrivate: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      roomState: {
        playerCount: 3,
        readyCount: 1,
        canStartGame: false,
        prizePool: 300,
        gameInProgress: false,
        countdown: null,
      },
      players: [
        {
          userId: 'user-1',
          username: '<PERSON>',
          betAmount: 100,
          isReady: true,
          joinedAt: new Date().toISOString(),
          position: 0,
          balance: 1000,
          insufficientBalance: false,
          colorId: 'red',
          colorHex: '#FF0000',
        },
        {
          userId: 'user-2',
          username: 'Bob',
          betAmount: 100,
          isReady: false,
          joinedAt: new Date().toISOString(),
          position: 1,
          balance: 500,
          insufficientBalance: false,
          colorId: 'blue',
          colorHex: '#0000FF',
        },
        {
          userId: 'user-3',
          username: 'Charlie',
          betAmount: 100,
          isReady: false,
          joinedAt: new Date().toISOString(),
          position: 2,
          balance: 200,
          insufficientBalance: false,
          colorId: null,
          colorHex: null,
        },
      ],
      gameConfig: {
        betAmount: 100,
        gameType: 'prizewheel',
        maxPlayers: 8,
        minPlayers: 2,
        settings: {},
      },
      // Enhanced game-specific data for Prize Wheel
      gameSpecificData: {
        gameType: 'prizewheel',
        colorSelections: {
          'user-1': 'red',
          'user-2': 'blue',
        },
        availableColors: ['green', 'yellow', 'purple', 'orange', 'pink', 'teal'],
        playerColorMappings: {
          'user-1': {
            colorId: 'red',
            selectedAt: new Date(Date.now() - 60000).toISOString(), // 1 minute ago
          },
          'user-2': {
            colorId: 'blue',
            selectedAt: new Date(Date.now() - 30000).toISOString(), // 30 seconds ago
          },
        },
        colorSelectionTimestamps: {
          'user-1': new Date(Date.now() - 60000).toISOString(),
          'user-2': new Date(Date.now() - 30000).toISOString(),
        },
      },
      timestamp: new Date().toISOString(),
    };

    // Emit the enhanced event
    socket.emit('room_info_updated', enhancedRoomData);
    console.log('Enhanced room_info_updated event sent:', {
      roomId: enhancedRoomData.room.id,
      hasGameSpecificData: !!enhancedRoomData.gameSpecificData,
      gameType: enhancedRoomData.gameSpecificData?.gameType,
      colorSelectionsCount: Object.keys(enhancedRoomData.gameSpecificData?.colorSelections || {}).length,
      availableColorsCount: enhancedRoomData.gameSpecificData?.availableColors?.length || 0,
    });
  }, 2000);
});

socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
});

socket.on('disconnect', (reason) => {
  console.log('Disconnected:', reason);
});

// Listen for any events to verify the server is responding
socket.onAny((eventName, ...args) => {
  console.log(`Received event: ${eventName}`, args);
});

console.log('Test script started. Connecting to socket server...');
console.log('This script will:');
console.log('1. Connect to the socket server');
console.log('2. Send an enhanced room_info_updated event with gameSpecificData');
console.log('3. Verify the client handles the enhanced data structure');
console.log('');
console.log('Make sure to:');
console.log('- Have the socket server running on localhost:3001');
console.log('- Use a valid authentication token');
console.log('- Check the browser console for enhanced data processing logs');
