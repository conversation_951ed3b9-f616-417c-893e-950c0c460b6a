import { WheelColor } from '../types/socket';

export const DEFAULT_WHEEL_COLORS: Omit<WheelColor, 'isAvailable' | 'selectedBy'>[] = [
  {
    id: 'red',
    name: 'Red',
    hex: '#ef4444',
  },
  {
    id: 'blue',
    name: 'Blue',
    hex: '#3b82f6',
  },
  {
    id: 'green',
    name: 'Green',
    hex: '#10b981',
  },
  {
    id: 'yellow',
    name: 'Yellow',
    hex: '#f59e0b',
  },
  {
    id: 'purple',
    name: 'Purple',
    hex: '#8b5cf6',
  },
  {
    id: 'orange',
    name: 'Orange',
    hex: '#f97316',
  },
  {
    id: 'pink',
    name: 'Pink',
    hex: '#ec4899',
  },
  {
    id: 'teal',
    name: 'Tea<PERSON>',
    hex: '#14b8a6',
  },
];

export const createAvailableColors = (
  selectedColors: Record<string, string> = {} // playerId -> colorId
): WheelColor[] => {
  return DEFAULT_WHEEL_COLORS.map(color => ({
    ...color,
    isAvailable: !Object.values(selectedColors).includes(color.id),
    selectedBy: Object.keys(selectedColors).find(playerId => selectedColors[playerId] === color.id),
  }));
};

export const getColorByName = (name: string): WheelColor | undefined => {
  const baseColor = DEFAULT_WHEEL_COLORS.find(color => 
    color.name.toLowerCase() === name.toLowerCase()
  );
  
  if (!baseColor) return undefined;
  
  return {
    ...baseColor,
    isAvailable: true,
  };
};

export const getColorById = (id: string): WheelColor | undefined => {
  const baseColor = DEFAULT_WHEEL_COLORS.find(color => color.id === id);

  if (!baseColor) return undefined;

  return {
    ...baseColor,
    isAvailable: true,
  };
};

export const getColorHexById = (id: string): string => {
  const color = DEFAULT_WHEEL_COLORS.find(color => color.id === id);
  return color?.hex || '#000000';
};

export const validateColorSelection = (
  colorId: string,
  selectedColors: Record<string, string> = {}
): { isValid: boolean; error?: string } => {
  const color = DEFAULT_WHEEL_COLORS.find(c => c.id === colorId);
  
  if (!color) {
    return { isValid: false, error: 'Invalid color selection' };
  }
  
  if (Object.values(selectedColors).includes(colorId)) {
    return { isValid: false, error: 'Color already selected by another player' };
  }
  
  return { isValid: true };
};

// Prize wheel configuration
export const WHEEL_CONFIG = {
  MIN_PLAYERS: 2,
  MAX_PLAYERS: 8,
  DEFAULT_SIZE: 380, // Increased size for better visibility
  SPIN_DURATION: 4000, // Slightly longer for more dramatic effect
  SEGMENT_BORDER_WIDTH: 3,
  CENTER_RADIUS: 35, // Larger center for better proportions
  OUTER_RING_WIDTH: 8, // New: decorative outer ring
  GLOW_INTENSITY: 0.4, // New: glow effect intensity
  METALLIC_HIGHLIGHT: 0.3, // New: metallic effect strength
} as const;

export type WheelColorId = typeof DEFAULT_WHEEL_COLORS[number]['id'];
