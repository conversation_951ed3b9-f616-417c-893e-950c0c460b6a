import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock fetch
global.fetch = vi.fn();

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};

// Mock environment variables
vi.mock('../config/env', () => ({
  env: {
    DEBUG: false,
    API_BASE_URL: 'http://localhost:3000',
    SOCKET_URL: 'http://localhost:3000',
  },
}));

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
  },
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    loading: vi.fn(),
    dismiss: vi.fn(),
  },
}));

// Mock socket.io-client
vi.mock('socket.io-client', () => ({
  io: vi.fn(() => ({
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    connect: vi.fn(),
    disconnect: vi.fn(),
    connected: false,
  })),
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => {
  const MockIcon = ({ className, ...props }: any) => (
    <div className={className} data-testid="mock-icon" {...props} />
  );

  return new Proxy({}, {
    get: () => MockIcon,
  });
});

// Setup test utilities
export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  username: 'testuser',
  email: '<EMAIL>',
  balance: 100.00,
  ...overrides,
});

export const createMockRoom = (overrides = {}) => ({
  id: 'test-room-id',
  name: 'Test Room',
  gameType: 'prize_wheel',
  status: 'waiting',
  playerCount: 1,
  maxPlayers: 8,
  betAmount: 1000, // in cents
  isPrivate: false,
  createdAt: new Date().toISOString(),
  ...overrides,
});

export const createMockPrizePool = (overrides = {}) => ({
  id: 'test-pool-id',
  room_id: 'test-room-id',
  total_pool: 100.00,
  entry_fee_per_player: 10.00,
  contributing_players: ['user1', 'user2'],
  house_edge_percentage: 5,
  house_edge_amount: 5.00,
  net_prize_amount: 95.00,
  status: 'accumulating' as const,
  game_type: 'prize_wheel' as const,
  room_name: 'Test Room',
  max_players: 8,
  player_count: 2,
  is_full: false,
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  metadata: {},
  ...overrides,
});

export const createMockTransaction = (overrides = {}) => ({
  id: 'test-transaction-id',
  user_id: 'test-user-id',
  type: 'entry_fee' as const,
  amount: 10.00,
  status: 'completed' as const,
  description: 'Test transaction',
  metadata: {
    room_id: 'test-room-id',
    game_type: 'prize_wheel',
  },
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

// Test helper functions
export const waitForNextTick = () => new Promise(resolve => setTimeout(resolve, 0));

export const mockApiResponse = <T>(data: T, success = true) => ({
  success,
  data,
  error: success ? null : { message: 'Test error' },
});

// Cleanup function for tests
export const cleanup = () => {
  vi.clearAllMocks();
  localStorageMock.clear();
  sessionStorageMock.clear();
};
