/**
 * Test for room loading state management
 * This test verifies that the loading state is properly managed
 * when joining a room and waiting for room_info_updated event
 */

// Mock socket store state
interface MockSocketState {
  waitingForRoomInfo: boolean;
  roomInfoTimeout: NodeJS.Timeout | null;
  currentRoom: any;
}

// Mock implementation of the loading state logic
class RoomLoadingStateManager {
  private state: MockSocketState = {
    waitingForRoomInfo: false,
    roomInfoTimeout: null,
    currentRoom: null
  };

  setWaitingForRoomInfo(waiting: boolean, roomId?: string) {
    // Clear existing timeout if any
    if (this.state.roomInfoTimeout) {
      clearTimeout(this.state.roomInfoTimeout);
    }

    let newTimeout: NodeJS.Timeout | null = null;
    
    // Set timeout to automatically clear waiting state after 10 seconds
    if (waiting && roomId) {
      newTimeout = setTimeout(() => {
        console.warn(`⚠️ Room info timeout for room ${roomId} - clearing waiting state`);
        this.state.waitingForRoomInfo = false;
        this.state.roomInfoTimeout = null;
      }, 10000); // 10 second timeout
    }

    this.state.waitingForRoomInfo = waiting;
    this.state.roomInfoTimeout = newTimeout;
  }

  async joinRoom(roomId: string) {
    console.log(`🚀 Starting room join for ${roomId}`);
    
    // Set waiting state before attempting to join
    this.setWaitingForRoomInfo(true, roomId);
    
    try {
      // Simulate room join API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      console.log(`✅ Room join successful for ${roomId}`);
      // Note: waitingForRoomInfo will be cleared when room_info_updated is received
      
    } catch (error) {
      // Clear waiting state on failure
      this.setWaitingForRoomInfo(false);
      throw error;
    }
  }

  onRoomInfoUpdated(roomId: string, roomData: any) {
    console.log(`📨 Room info updated received for ${roomId}`);
    
    // Update room state
    this.state.currentRoom = {
      id: roomId,
      ...roomData
    };
    
    // Clear waiting state since we received room info
    this.state.waitingForRoomInfo = false;
    if (this.state.roomInfoTimeout) {
      clearTimeout(this.state.roomInfoTimeout);
      this.state.roomInfoTimeout = null;
    }
    
    console.log(`✅ Room loading state cleared for ${roomId}`);
  }

  getState() {
    return { ...this.state };
  }
}

/**
 * Test the room loading state flow
 */
async function testRoomLoadingStateFlow() {
  console.log('🧪 Testing room loading state flow...\n');
  
  const manager = new RoomLoadingStateManager();
  const roomId = "68412c9af494b684c1c18ecf";
  
  // Test 1: Initial state
  console.log('📋 Test 1: Initial state');
  let state = manager.getState();
  console.log('Initial state:', {
    waitingForRoomInfo: state.waitingForRoomInfo,
    hasTimeout: !!state.roomInfoTimeout,
    hasCurrentRoom: !!state.currentRoom
  });
  
  if (!state.waitingForRoomInfo && !state.roomInfoTimeout && !state.currentRoom) {
    console.log('✅ Initial state test PASSED\n');
  } else {
    console.log('❌ Initial state test FAILED\n');
    return false;
  }
  
  // Test 2: Join room (should set waiting state)
  console.log('📋 Test 2: Join room');
  await manager.joinRoom(roomId);
  
  state = manager.getState();
  console.log('State after join:', {
    waitingForRoomInfo: state.waitingForRoomInfo,
    hasTimeout: !!state.roomInfoTimeout,
    hasCurrentRoom: !!state.currentRoom
  });
  
  if (state.waitingForRoomInfo && state.roomInfoTimeout && !state.currentRoom) {
    console.log('✅ Join room test PASSED\n');
  } else {
    console.log('❌ Join room test FAILED\n');
    return false;
  }
  
  // Test 3: Receive room_info_updated (should clear waiting state)
  console.log('📋 Test 3: Receive room_info_updated');
  const mockRoomData = {
    name: "czxcwee2",
    gameType: "prizewheel",
    status: "waiting",
    currentPlayers: 1,
    maxPlayers: 2,
    players: [
      {
        userId: "683b07882d7dbd11e92bf29d",
        username: "res2",
        position: 1,
        isReady: false
      }
    ]
  };
  
  manager.onRoomInfoUpdated(roomId, mockRoomData);
  
  state = manager.getState();
  console.log('State after room_info_updated:', {
    waitingForRoomInfo: state.waitingForRoomInfo,
    hasTimeout: !!state.roomInfoTimeout,
    hasCurrentRoom: !!state.currentRoom,
    roomId: state.currentRoom?.id
  });
  
  if (!state.waitingForRoomInfo && !state.roomInfoTimeout && state.currentRoom && state.currentRoom.id === roomId) {
    console.log('✅ Room info updated test PASSED\n');
  } else {
    console.log('❌ Room info updated test FAILED\n');
    return false;
  }
  
  console.log('🎉 All room loading state tests PASSED!');
  return true;
}

/**
 * Test timeout functionality
 */
async function testTimeoutFunctionality() {
  console.log('\n🧪 Testing timeout functionality...\n');
  
  const manager = new RoomLoadingStateManager();
  const roomId = "test-timeout-room";
  
  // Start join (sets waiting state with timeout)
  await manager.joinRoom(roomId);
  
  console.log('📋 Waiting state set, testing timeout...');
  
  // Wait for timeout to trigger (reduced for testing)
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // Check if timeout is still active
  let state = manager.getState();
  if (state.waitingForRoomInfo && state.roomInfoTimeout) {
    console.log('✅ Timeout is active');
  } else {
    console.log('❌ Timeout not working properly');
    return false;
  }
  
  console.log('🎉 Timeout functionality test PASSED!');
  return true;
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting room loading state tests...\n');
  
  const test1 = await testRoomLoadingStateFlow();
  const test2 = await testTimeoutFunctionality();
  
  console.log('\n📊 Test Results:');
  console.log(`   Room Loading State Flow: ${test1 ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Timeout Functionality: ${test2 ? '✅ PASS' : '❌ FAIL'}`);
  
  if (test1 && test2) {
    console.log('\n🎉 All tests PASSED! Room loading state management is working correctly.');
  } else {
    console.log('\n❌ Some tests failed. Please check the implementation.');
  }
  
  return test1 && test2;
}

// Run tests
runAllTests();
