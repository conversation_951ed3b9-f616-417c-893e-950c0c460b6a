import { renderHook, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { usePrizePoolData } from '../usePrizePoolData';
import { useGameStore } from '../../store/gameStore';
import { useAuthStore } from '../../store/authStore';
import { apiClient } from '../../services/api';

// Mock dependencies
vi.mock('../../store/gameStore');
vi.mock('../../store/authStore');
vi.mock('../../services/api');
vi.mock('../usePrizeWheelErrorHandler', () => ({
  usePrizeWheelErrorHandler: () => ({
    handlePrizePoolError: vi.fn(),
    handleApiErrorWithRetry: vi.fn((fn) => fn()),
  }),
}));

const mockUseGameStore = vi.mocked(useGameStore);
const mockUseAuthStore = vi.mocked(useAuthStore);
const mockApiClient = vi.mocked(apiClient);

describe('usePrizePoolData', () => {
  const mockPrizePool = {
    id: 'pool-123',
    room_id: 'room-123',
    total_pool: 100.00,
    entry_fee_per_player: 10.00,
    contributing_players: ['user1', 'user2'],
    house_edge_percentage: 5,
    house_edge_amount: 5.00,
    net_prize_amount: 95.00,
    status: 'accumulating' as const,
    game_type: 'prize_wheel' as const,
    room_name: 'Test Room',
    max_players: 8,
    player_count: 2,
    is_full: false,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    metadata: {},
  };

  const mockPotentialWinnings = {
    potential_winnings: [
      {
        user_id: 'user1',
        potential_winnings: 95.00,
        win_probability: 0.5,
        current_pool_share: 0.5,
      },
    ],
  };

  const mockUser = {
    id: 'user1',
    username: 'testuser',
    email: '<EMAIL>',
    balance: 50.00,
  };

  beforeEach(() => {
    vi.clearAllMocks();

    mockUseAuthStore.mockReturnValue({
      user: mockUser,
    } as any);

    mockUseGameStore.mockReturnValue({
      getPrizePoolData: vi.fn(() => mockPrizePool),
      updatePrizePoolData: vi.fn(),
      getPotentialWinnings: vi.fn(() => mockPotentialWinnings),
      updatePotentialWinnings: vi.fn(),
      prizePoolData: {
        currentPrizePool: mockPrizePool,
        prizePoolLoading: false,
        prizePoolError: null,
        entryFeeStatus: {},
        potentialWinnings: mockPotentialWinnings,
        transactionHistory: [],
      },
    } as any);

    mockApiClient.getRoomPrizePool.mockResolvedValue(mockPrizePool);
    mockApiClient.getPotentialWinnings.mockResolvedValue(mockPotentialWinnings);
  });

  it('loads prize pool data on mount', async () => {
    const { result } = renderHook(() => usePrizePoolData('room-123'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockApiClient.getRoomPrizePool).toHaveBeenCalledWith('room-123');
    expect(result.current.prizePool).toEqual(mockPrizePool);
  });

  it('loads potential winnings data', async () => {
    const { result } = renderHook(() => usePrizePoolData('room-123'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockApiClient.getPotentialWinnings).toHaveBeenCalledWith('room-123');
    expect(result.current.potentialWinnings).toEqual(mockPotentialWinnings);
  });

  it('provides derived data correctly', async () => {
    const { result } = renderHook(() => usePrizePoolData('room-123'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.totalPool).toBe(100.00);
    expect(result.current.netPrize).toBe(95.00);
    expect(result.current.houseEdge).toBe(5);
    expect(result.current.houseEdgeAmount).toBe(5.00);
    expect(result.current.entryFee).toBe(10.00);
    expect(result.current.maxPlayers).toBe(8);
    expect(result.current.currentPlayers).toBe(2);
    expect(result.current.contributingPlayers).toBe(2);
    expect(result.current.isAccumulating).toBe(true);
    expect(result.current.isLocked).toBe(false);
    expect(result.current.isDistributed).toBe(false);
    expect(result.current.isCancelled).toBe(false);
  });

  it('provides user potential winnings', async () => {
    const { result } = renderHook(() => usePrizePoolData('room-123'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.userPotentialWinnings).toEqual({
      user_id: 'user1',
      potential_winnings: 95.00,
      win_probability: 0.5,
      current_pool_share: 0.5,
    });
  });

  it('handles loading state', () => {
    const { result } = renderHook(() => usePrizePoolData('room-123'));

    expect(result.current.loading).toBe(true);
    expect(result.current.prizePool).toBe(null);
  });

  it('handles error state', async () => {
    const error = new Error('Failed to load prize pool');
    mockApiClient.getRoomPrizePool.mockRejectedValue(error);

    const { result } = renderHook(() => usePrizePoolData('room-123'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
  });

  it('refreshes data when called', async () => {
    const { result } = renderHook(() => usePrizePoolData('room-123'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Clear previous calls
    vi.clearAllMocks();

    await result.current.actions.refreshData();

    expect(mockApiClient.getRoomPrizePool).toHaveBeenCalledWith('room-123');
    expect(mockApiClient.getPotentialWinnings).toHaveBeenCalledWith('room-123');
  });

  it('clears data when called', () => {
    const { result } = renderHook(() => usePrizePoolData('room-123'));

    result.current.actions.clearData();

    expect(result.current.prizePool).toBe(null);
    expect(result.current.potentialWinnings).toBe(null);
    expect(result.current.error).toBe(null);
  });

  it('handles missing roomId', () => {
    const { result } = renderHook(() => usePrizePoolData());

    expect(result.current.prizePool).toBe(null);
    expect(result.current.loading).toBe(false);
  });

  it('reloads data when roomId changes', async () => {
    const { result, rerender } = renderHook(
      ({ roomId }) => usePrizePoolData(roomId),
      { initialProps: { roomId: 'room-123' } }
    );

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    // Clear previous calls
    vi.clearAllMocks();

    // Change roomId
    rerender({ roomId: 'room-456' });

    expect(mockApiClient.getRoomPrizePool).toHaveBeenCalledWith('room-456');
  });

  it('handles missing user gracefully', async () => {
    mockUseAuthStore.mockReturnValue({
      user: null,
    } as any);

    const { result } = renderHook(() => usePrizePoolData('room-123'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.userPotentialWinnings).toBe(null);
    expect(mockApiClient.getPotentialWinnings).not.toHaveBeenCalled();
  });

  it('syncs with game store data', async () => {
    const updatedPrizePool = { ...mockPrizePool, total_pool: 200.00 };
    
    mockUseGameStore.mockReturnValue({
      getPrizePoolData: vi.fn(() => updatedPrizePool),
      updatePrizePoolData: vi.fn(),
      getPotentialWinnings: vi.fn(() => mockPotentialWinnings),
      updatePotentialWinnings: vi.fn(),
      prizePoolData: {
        currentPrizePool: updatedPrizePool,
        prizePoolLoading: false,
        prizePoolError: null,
        entryFeeStatus: {},
        potentialWinnings: mockPotentialWinnings,
        transactionHistory: [],
      },
    } as any);

    const { result } = renderHook(() => usePrizePoolData('room-123'));

    await waitFor(() => {
      expect(result.current.totalPool).toBe(200.00);
    });
  });

  it('handles potential winnings loading failure gracefully', async () => {
    mockApiClient.getPotentialWinnings.mockRejectedValue(new Error('Failed to load'));
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

    const { result } = renderHook(() => usePrizePoolData('room-123'));

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(consoleSpy).toHaveBeenCalledWith('Failed to load potential winnings:', expect.any(Error));
    expect(result.current.prizePool).toEqual(mockPrizePool); // Prize pool should still load

    consoleSpy.mockRestore();
  });
});
