import { useState, useEffect, useCallback } from 'react';
import { socketService } from '../services/socket';
import { useSocketStore } from '../store/socketStore';

interface ReconnectionState {
  isReconnectionAvailable: boolean;
  reconnectionInfo: any | null;
  showReconnectionModal: boolean;
  timeRemaining: number;
}

interface UseReconnectionReturn extends ReconnectionState {
  checkForReconnection: () => void;
  attemptReconnection: () => Promise<boolean>;
  dismissReconnection: () => void;
  showReconnectionModal: boolean;
  setShowReconnectionModal: (show: boolean) => void;
}

export const useReconnection = (): UseReconnectionReturn => {
  const [state, setState] = useState<ReconnectionState>({
    isReconnectionAvailable: false,
    reconnectionInfo: null,
    showReconnectionModal: false,
    timeRemaining: 0
  });

  // Check for pending reconnection on mount and connection
  const checkForReconnection = useCallback(() => {
    const reconnectionInfo = socketService.checkForPendingReconnection();
    
    if (reconnectionInfo) {
      const now = new Date();
      const disconnectedAt = new Date(reconnectionInfo.disconnectedAt);
      const timeElapsed = now.getTime() - disconnectedAt.getTime();
      const fiveMinutes = 5 * 60 * 1000;
      const timeRemaining = Math.max(0, Math.floor((fiveMinutes - timeElapsed) / 1000));

      if (timeRemaining > 0) {
        setState(prev => ({
          ...prev,
          isReconnectionAvailable: true,
          reconnectionInfo,
          timeRemaining,
          showReconnectionModal: true
        }));
      } else {
        // Reconnection window expired
        socketService.clearReconnectionInfo();
      }
    }
  }, []);

  // Attempt to reconnect to the room
  const attemptReconnection = useCallback(async (): Promise<boolean> => {
    if (!state.reconnectionInfo) {
      return false;
    }

    try {
      const success = await socketService.attemptReconnection(state.reconnectionInfo);
      
      if (success) {
        setState(prev => ({
          ...prev,
          isReconnectionAvailable: false,
          reconnectionInfo: null,
          showReconnectionModal: false
        }));
      }
      
      return success;
    } catch (error) {
      console.error('Reconnection attempt failed:', error);
      return false;
    }
  }, [state.reconnectionInfo]);

  // Dismiss reconnection modal
  const dismissReconnection = useCallback(() => {
    setState(prev => ({
      ...prev,
      showReconnectionModal: false,
      isReconnectionAvailable: false,
      reconnectionInfo: null
    }));
    
    // Clear stored reconnection info
    socketService.clearReconnectionInfo();
  }, []);

  // Set modal visibility
  const setShowReconnectionModal = useCallback((show: boolean) => {
    setState(prev => ({
      ...prev,
      showReconnectionModal: show
    }));
  }, []);

  // Listen for socket events
  useEffect(() => {
    const handleConnect = () => {
      checkForReconnection();
    };

    const handleReconnectionEnabled = (data: any) => {
      setState(prev => ({
        ...prev,
        isReconnectionAvailable: true,
        reconnectionInfo: {
          roomId: data.roomId,
          reason: data.reason,
          disconnectedAt: new Date(),
          reconnectionWindow: data.reconnectionWindow || 300
        },
        timeRemaining: data.reconnectionWindow || 300,
        showReconnectionModal: true
      }));
    };

    const handlePlayerReadyDisconnected = (data: any) => {
      setState(prev => ({
        ...prev,
        isReconnectionAvailable: true,
        reconnectionInfo: {
          roomId: data.roomId,
          reason: 'disconnected',
          disconnectedAt: new Date(),
          reconnectionWindow: data.reconnectionWindow || 300,
          preservedState: true
        },
        timeRemaining: data.reconnectionWindow || 300,
        showReconnectionModal: true
      }));
    };

    const handleReconnectionSuccessful = () => {
      setState(prev => ({
        ...prev,
        isReconnectionAvailable: false,
        reconnectionInfo: null,
        showReconnectionModal: false
      }));
    };

    // Add event listeners
    socketService.on('connect', handleConnect);
    socketService.on('reconnection_enabled', handleReconnectionEnabled);
    socketService.on('player_ready_disconnected', handlePlayerReadyDisconnected);
    socketService.on('reconnection_successful', handleReconnectionSuccessful);

    // Check for reconnection on mount
    checkForReconnection();

    // Cleanup
    return () => {
      socketService.off('connect', handleConnect);
      socketService.off('reconnection_enabled', handleReconnectionEnabled);
      socketService.off('player_ready_disconnected', handlePlayerReadyDisconnected);
      socketService.off('reconnection_successful', handleReconnectionSuccessful);
    };
  }, [checkForReconnection]);

  // Countdown timer for time remaining
  useEffect(() => {
    if (!state.isReconnectionAvailable || state.timeRemaining <= 0) return;

    const timer = setInterval(() => {
      setState(prev => {
        const newTimeRemaining = prev.timeRemaining - 1;
        
        if (newTimeRemaining <= 0) {
          // Time expired
          socketService.clearReconnectionInfo();
          return {
            ...prev,
            isReconnectionAvailable: false,
            reconnectionInfo: null,
            showReconnectionModal: false,
            timeRemaining: 0
          };
        }
        
        return {
          ...prev,
          timeRemaining: newTimeRemaining
        };
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [state.isReconnectionAvailable, state.timeRemaining]);

  return {
    ...state,
    checkForReconnection,
    attemptReconnection,
    dismissReconnection,
    setShowReconnectionModal
  };
};

// Helper hook for room components
export const useRoomReconnection = (roomId?: string) => {
  const reconnection = useReconnection();
  const currentRoom = useSocketStore(state => state.currentRoom);

  // Only show reconnection for the current room
  const isRelevantReconnection = reconnection.reconnectionInfo?.roomId === roomId || 
                                 reconnection.reconnectionInfo?.roomId === currentRoom?.id;

  return {
    ...reconnection,
    showReconnectionModal: reconnection.showReconnectionModal && isRelevantReconnection,
    isReconnectionAvailable: reconnection.isReconnectionAvailable && isRelevantReconnection
  };
};

export default useReconnection;
