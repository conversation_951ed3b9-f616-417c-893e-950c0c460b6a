import { useState, useEffect, useCallback } from 'react';
import { useAuthStore } from '../store/authStore';
import { useSocketStore } from '../store/socketStore';
import { usePrizeWheelErrorHandler } from './usePrizeWheelErrorHandler';
import { apiClient } from '../services/api';
import type { UserBalanceUpdatedData, UserTransactionCompletedData } from '../types/socket';

export interface BalanceState {
  currentBalance: number;
  previousBalance: number;
  lastChangeAmount: number;
  lastChangeType: string | null;
  lastTransactionId: string | null;
  lastUpdated: Date | null;
  isValidating: boolean;
  validationError: string | null;
}

export interface BalanceActions {
  validateBalance: (requiredAmount: number) => Promise<boolean>;
  refreshBalance: () => Promise<void>;
  resetValidation: () => void;
}

export const useRealTimeBalance = () => {
  const { user, balance, updateBalance } = useAuthStore();
  const { socket } = useSocketStore();
  const { handleBalanceError, handleApiErrorWithRetry } = usePrizeWheelErrorHandler();

  const [state, setState] = useState<BalanceState>({
    currentBalance: balance || 0,
    previousBalance: balance || 0,
    lastChangeAmount: 0,
    lastChangeType: null,
    lastTransactionId: null,
    lastUpdated: null,
    isValidating: false,
    validationError: null,
  });

  // Validate balance against required amount
  const validateBalance = useCallback(async (requiredAmount: number): Promise<boolean> => {
    if (!user) return false;

    setState(prev => ({ ...prev, isValidating: true, validationError: null }));

    try {
      const result = await handleApiErrorWithRetry(
        () => apiClient.validateBalance({
          user_id: user.id,
          bet_amount: requiredAmount,
        }),
        3,
        1000,
        { context: 'balance_validation', showToast: false }
      );

      const isValid = result.has_sufficient_balance;
      
      setState(prev => ({
        ...prev,
        isValidating: false,
        validationError: isValid ? null : result.error_message || 'Insufficient balance',
        currentBalance: result.current_balance || prev.currentBalance,
      }));

      // Update auth store balance if provided
      if (result.current_balance !== undefined) {
        updateBalance(result.current_balance);
      }

      return isValid;
    } catch (error) {
      const prizeWheelError = handleBalanceError(error, state.currentBalance, requiredAmount);
      
      setState(prev => ({
        ...prev,
        isValidating: false,
        validationError: prizeWheelError.message,
      }));

      return false;
    }
  }, [user, handleApiErrorWithRetry, handleBalanceError, state.currentBalance, updateBalance]);

  // Refresh balance from server
  const refreshBalance = useCallback(async () => {
    if (!user) return;

    try {
      const userProfile = await handleApiErrorWithRetry(
        () => apiClient.getProfile(),
        2,
        1000,
        { context: 'refresh_balance', showToast: false }
      );

      const newBalance = userProfile.balance;
      updateBalance(newBalance);
      
      setState(prev => ({
        ...prev,
        previousBalance: prev.currentBalance,
        currentBalance: newBalance,
        lastChangeAmount: newBalance - prev.currentBalance,
        lastUpdated: new Date(),
      }));
    } catch (error) {
      console.warn('Failed to refresh balance:', error);
    }
  }, [user, handleApiErrorWithRetry, updateBalance]);

  // Reset validation state
  const resetValidation = useCallback(() => {
    setState(prev => ({
      ...prev,
      isValidating: false,
      validationError: null,
    }));
  }, []);

  // Handle real-time balance updates
  useEffect(() => {
    if (!socket || !user) return;

    const handleBalanceUpdate = (data: UserBalanceUpdatedData) => {
      if (data.userId !== user.id) return;

      const newBalance = data.newBalance;
      updateBalance(newBalance);

      setState(prev => ({
        ...prev,
        previousBalance: data.previousBalance,
        currentBalance: newBalance,
        lastChangeAmount: data.changeAmount,
        lastChangeType: data.changeType,
        lastTransactionId: data.transactionId,
        lastUpdated: new Date(data.timestamp),
      }));
    };

    const handleTransactionCompleted = (data: UserTransactionCompletedData) => {
      if (data.userId !== user.id) return;

      const newBalance = data.newBalance;
      updateBalance(newBalance);

      setState(prev => ({
        ...prev,
        previousBalance: prev.currentBalance,
        currentBalance: newBalance,
        lastChangeAmount: data.transaction.amount,
        lastChangeType: data.transaction.type,
        lastTransactionId: data.transaction.id,
        lastUpdated: new Date(data.timestamp),
      }));
    };

    socket.on('user_balance_updated', handleBalanceUpdate);
    socket.on('user_transaction_completed', handleTransactionCompleted);

    return () => {
      socket.off('user_balance_updated', handleBalanceUpdate);
      socket.off('user_transaction_completed', handleTransactionCompleted);
    };
  }, [socket, user, updateBalance]);

  // Sync with auth store balance
  useEffect(() => {
    if (balance !== undefined && balance !== state.currentBalance) {
      setState(prev => ({
        ...prev,
        previousBalance: prev.currentBalance,
        currentBalance: balance,
        lastChangeAmount: balance - prev.currentBalance,
        lastUpdated: new Date(),
      }));
    }
  }, [balance, state.currentBalance]);

  // Calculate derived data
  const derivedData = {
    hasInsufficientBalance: (requiredAmount: number) => state.currentBalance < requiredAmount,
    getShortfall: (requiredAmount: number) => Math.max(0, requiredAmount - state.currentBalance),
    hasRecentChange: state.lastUpdated && (Date.now() - state.lastUpdated.getTime()) < 30000, // 30 seconds
    isPositiveChange: state.lastChangeAmount > 0,
    isNegativeChange: state.lastChangeAmount < 0,
    formattedBalance: state.currentBalance.toFixed(2),
    formattedLastChange: Math.abs(state.lastChangeAmount).toFixed(2),
    changeDirection: state.lastChangeAmount > 0 ? 'increase' : state.lastChangeAmount < 0 ? 'decrease' : 'none',
  };

  const actions: BalanceActions = {
    validateBalance,
    refreshBalance,
    resetValidation,
  };

  return {
    ...state,
    ...derivedData,
    actions,
  };
};

export default useRealTimeBalance;
