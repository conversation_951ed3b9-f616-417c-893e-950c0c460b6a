import { useEffect, useCallback, useState } from 'react';
import { socketService } from '@/services/socket';
import { useGameStore } from '@/store/gameStore';
import { useSocketStore } from '@/store/socketStore';
import { useAuthStore } from '@/store/authStore';
import { env } from '@/config/env';
import toast from 'react-hot-toast';
import type {
  AvailableColorsEvent,
  ColorSelectionUpdateEvent,
  ColorStateSyncEvent,
  WheelColor,
} from '@/types/socket';

interface EnhancedColorSelectionState {
  availableColors: WheelColor[];
  takenColors: Record<string, { userId: string; username: string }>;
  playerColorMappings: Record<string, {
    userId: string;
    username: string;
    colorId: string;
    colorName: string;
    colorHex: string;
    selectedAt: string;
  }>;
  statistics: {
    totalPlayers: number;
    playersWithColors: number;
    selectionRate: number;
    availableCount: number;
    takenCount: number;
    totalColors?: number;
  };
  // New comprehensive data from color_selection_updated event
  assignments: Record<string, {
    color: { hex: string; id: string; name: string; };
    selectedAt: string;
    userId: string;
    username: string;
  }>;
  selectedColors: Record<string, {
    selectedAt: string;
    userId: string;
    username: string;
  }>;
  currentPlayerColor?: {
    hex: string;
    id: string;
    name: string;
  };
  isLoading: boolean;
  lastUpdate: string | null;
}

/**
 * Enhanced hook for Prize Wheel color selection with real-time updates
 * Implements the comprehensive color selection system from the guide
 */
export const useEnhancedColorSelection = () => {
  const gameStore = useGameStore();
  const { currentRoom } = useSocketStore();
  const { user } = useAuthStore();
  
  const [colorState, setColorState] = useState<EnhancedColorSelectionState>({
    availableColors: [],
    takenColors: {},
    playerColorMappings: {},
    statistics: {
      totalPlayers: 0,
      playersWithColors: 0,
      selectionRate: 0,
      availableCount: 0,
      takenCount: 0,
      totalColors: 8,
    },
    assignments: {},
    selectedColors: {},
    currentPlayerColor: undefined,
    isLoading: false,
    lastUpdate: null,
  });

  // Handle available colors event
  const handleAvailableColors = useCallback((event: AvailableColorsEvent) => {
    const { availableColors, takenColors, availableCount, takenCount, timestamp } = event.data;
    
    if (env.DEBUG) {
      console.log('🎨 Available colors updated:', {
        available: availableCount,
        taken: takenCount,
        roomId: event.data.roomId
      });
    }

    // Convert to WheelColor format
    const wheelAvailableColors: WheelColor[] = availableColors.map(color => ({
      ...color,
      isAvailable: true,
    }));

    // Convert taken colors to lookup map
    const takenColorsMap = takenColors.reduce((acc, color) => {
      acc[color.id] = {
        userId: color.takenBy.userId,
        username: color.takenBy.username,
      };
      return acc;
    }, {} as Record<string, { userId: string; username: string }>);

    setColorState(prev => ({
      ...prev,
      availableColors: wheelAvailableColors,
      takenColors: takenColorsMap,
      statistics: {
        ...prev.statistics,
        availableCount,
        takenCount,
      },
      lastUpdate: timestamp,
    }));

    // Update game store
    gameStore.handleColorStateUpdate({
      roomId: event.data.roomId,
      playerColors: Object.fromEntries(
        takenColors.map(color => [color.takenBy.userId, color.id])
      ),
      availableColors: availableColors.map(c => c.id),
      takenColors: Object.fromEntries(takenColors.map(c => [c.id, true])),
      timestamp,
    });
  }, [gameStore]);

  // Handle color selection update event
  const handleColorSelectionUpdate = useCallback((event: ColorSelectionUpdateEvent) => {
    const { player, action, availableColors, takenColors, timestamp } = event.data;
    
    if (env.DEBUG) {
      console.log('🎨 Color selection update:', {
        player: player.username,
        action,
        color: player.selectedColor?.name || player.unselectedColor?.name
      });
    }

    // Show notification
    if (action === 'selected' && player.selectedColor) {
      const isCurrentUser = player.userId === user?.id;
      const message = isCurrentUser 
        ? `You selected ${player.selectedColor.name}`
        : `${player.username} selected ${player.selectedColor.name}`;
      
      toast.success(message, {
        duration: 3000,
        icon: '🎨',
      });

      // Update player color assignment in game store
      gameStore.updatePlayerColorSelection(player.userId, player.selectedColor.id);
      
    } else if (action === 'unselected' && player.unselectedColor) {
      const isCurrentUser = player.userId === user?.id;
      const message = isCurrentUser 
        ? `You unselected ${player.unselectedColor.name}`
        : `${player.username} unselected their color`;
      
      toast(message, {
        duration: 2000,
        icon: '↩️',
      });

      // Remove player color assignment in game store
      gameStore.updatePlayerColorSelection(player.userId, '');
    }

    // Update available colors
    const wheelAvailableColors: WheelColor[] = availableColors.map(color => ({
      ...color,
      isAvailable: true,
    }));

    const takenColorsMap = takenColors.reduce((acc, color) => {
      acc[color.id] = {
        userId: color.takenBy.userId,
        username: color.takenBy.username,
      };
      return acc;
    }, {} as Record<string, { userId: string; username: string }>);

    setColorState(prev => ({
      ...prev,
      availableColors: wheelAvailableColors,
      takenColors: takenColorsMap,
      lastUpdate: timestamp,
    }));

    // Update game store with new state
    gameStore.handleColorStateUpdate({
      roomId: event.data.roomId,
      playerColors: Object.fromEntries(
        takenColors.map(color => [color.takenBy.userId, color.id])
      ),
      availableColors: availableColors.map(c => c.id),
      takenColors: Object.fromEntries(takenColors.map(c => [c.id, true])),
      timestamp,
    });
  }, [gameStore, user?.id]);

  // Handle complete color state sync event
  const handleColorStateSync = useCallback((event: ColorStateSyncEvent) => {
    const { 
      playerColorMappings, 
      availableColors, 
      takenColors, 
      statistics,
      timestamp 
    } = event.data;
    
    if (env.DEBUG) {
      console.log('🎨 Complete color state sync:', {
        totalPlayers: statistics.totalPlayers,
        selectionRate: statistics.selectionRate,
        roomId: event.data.roomId
      });
    }

    // Convert to our state format
    const wheelAvailableColors: WheelColor[] = availableColors.map(color => ({
      ...color,
      isAvailable: true,
    }));

    const takenColorsMap = takenColors.reduce((acc, color) => {
      acc[color.id] = {
        userId: color.takenBy.userId,
        username: color.takenBy.username,
      };
      return acc;
    }, {} as Record<string, { userId: string; username: string }>);

    const mappings = Object.fromEntries(
      Object.entries(playerColorMappings).map(([userId, mapping]) => [
        userId,
        {
          userId: mapping.userId,
          username: mapping.username,
          colorId: mapping.color.id,
          colorName: mapping.color.name,
          colorHex: mapping.color.hex,
          selectedAt: mapping.selectedAt,
        }
      ])
    );

    setColorState({
      availableColors: wheelAvailableColors,
      takenColors: takenColorsMap,
      playerColorMappings: mappings,
      statistics,
      isLoading: false,
      lastUpdate: timestamp,
    });

    // Sync all player color selections to game store
    Object.entries(playerColorMappings).forEach(([userId, mapping]) => {
      gameStore.updatePlayerColorSelection(userId, mapping.color.id);
    });

    // Update game store with complete state
    gameStore.handleColorStateUpdate({
      roomId: event.data.roomId,
      playerColors: Object.fromEntries(
        Object.entries(playerColorMappings).map(([userId, mapping]) => [userId, mapping.color.id])
      ),
      availableColors: availableColors.map(c => c.id),
      takenColors: Object.fromEntries(takenColors.map(c => [c.id, true])),
      timestamp,
    });
  }, [gameStore]);

  // Handle comprehensive color selection updated event
  const handleColorSelectionUpdated = useCallback((event: any) => {
    if (env.DEBUG) {
      console.log('🎨 Color selection updated event received:', event);
    }

    const { colorState, player, timestamp } = event;

    // Extract comprehensive data from the event
    const assignments = colorState.assignments || {};
    const selectedColors = colorState.selectedColors || {};
    const statistics = colorState.statistics || {};
    const availableColors = colorState.availableColors || [];
    const currentPlayerColor = colorState.currentPlayerColor;

    // Convert assignments to player color mappings
    const playerColorMappings = Object.fromEntries(
      Object.entries(assignments).map(([userId, assignment]: [string, any]) => [
        userId,
        {
          userId: assignment.userId,
          username: assignment.username,
          colorId: assignment.color.id,
          colorName: assignment.color.name,
          colorHex: assignment.color.hex,
          selectedAt: assignment.selectedAt,
        }
      ])
    );

    // Convert to WheelColor format
    const wheelAvailableColors: WheelColor[] = availableColors.map((color: any) => ({
      ...color,
      isAvailable: true,
    }));

    // Convert taken colors to lookup map
    const takenColorsMap = Object.fromEntries(
      Object.entries(selectedColors).map(([colorId, selection]: [string, any]) => [
        colorId,
        {
          userId: selection.userId,
          username: selection.username,
        }
      ])
    );

    setColorState(prev => ({
      ...prev,
      availableColors: wheelAvailableColors,
      takenColors: takenColorsMap,
      playerColorMappings,
      statistics: {
        totalPlayers: prev.statistics.totalPlayers,
        playersWithColors: statistics.takenCount || 0,
        selectionRate: statistics.selectionRate || 0,
        availableCount: statistics.availableCount || 0,
        takenCount: statistics.takenCount || 0,
        totalColors: statistics.totalColors || 8,
      },
      assignments,
      selectedColors,
      currentPlayerColor,
      isLoading: false,
      lastUpdate: timestamp,
    }));

    // Sync to game store
    Object.entries(playerColorMappings).forEach(([userId, mapping]) => {
      gameStore.updatePlayerColorSelection(userId, mapping.colorId);
    });

    // Show toast notification for color selection
    if (player && user?.id !== player.userId) {
      toast.success(`${player.username} selected ${player.color.name}!`, {
        duration: 2000,
        icon: '🎨',
      });
    }
  }, [gameStore, user?.id]);

  // Set up event listeners
  useEffect(() => {
    socketService.on('available_colors', handleAvailableColors);
    socketService.on('color_selection_update', handleColorSelectionUpdate);
    socketService.on('color_state_sync', handleColorStateSync);
    socketService.on('color_selection_updated', handleColorSelectionUpdated);

    return () => {
      socketService.off('available_colors', handleAvailableColors);
      socketService.off('color_selection_update', handleColorSelectionUpdate);
      socketService.off('color_state_sync', handleColorStateSync);
      socketService.off('color_selection_updated', handleColorSelectionUpdated);
    };
  }, [handleAvailableColors, handleColorSelectionUpdate, handleColorStateSync, handleColorSelectionUpdated]);

  // Enhanced color selection function with error handling
  const selectColor = useCallback(async (colorId: string) => {
    if (!currentRoom?.id) {
      toast.error('You must be in a room to select a color');
      return false;
    }

    setColorState(prev => ({ ...prev, isLoading: true }));

    try {
      await socketService.selectWheelColor(currentRoom.id, colorId);
      return true;
    } catch (error: any) {
      // Enhanced error handling based on the guide
      let errorMessage = 'Failed to select color';
      
      switch (error.code) {
        case 'INVALID_COLOR':
          errorMessage = 'Invalid color selected';
          break;
        case 'COLOR_ALREADY_TAKEN':
          errorMessage = 'This color is already taken by another player';
          break;
        case 'NO_ROOM_CONTEXT':
          errorMessage = 'You must be in a room to select a color';
          break;
        default:
          errorMessage = error.message || 'Failed to select color';
      }
      
      toast.error(errorMessage);
      return false;
    } finally {
      setColorState(prev => ({ ...prev, isLoading: false }));
    }
  }, [currentRoom?.id]);

  // Get current user's selected color
  const getCurrentUserColor = useCallback(() => {
    if (!user?.id) return null;
    return colorState.playerColorMappings[user.id] || null;
  }, [user?.id, colorState.playerColorMappings]);

  // Check if a color is available
  const isColorAvailable = useCallback((colorId: string) => {
    return colorState.availableColors.some(color => color.id === colorId);
  }, [colorState.availableColors]);

  return {
    // State
    availableColors: colorState.availableColors,
    takenColors: colorState.takenColors,
    playerColorMappings: colorState.playerColorMappings,
    statistics: colorState.statistics,
    isLoading: colorState.isLoading,
    lastUpdate: colorState.lastUpdate,

    // New comprehensive data from color_selection_updated event
    assignments: colorState.assignments,
    selectedColors: colorState.selectedColors,
    currentPlayerColor: colorState.currentPlayerColor,

    // Actions
    selectColor,

    // Helpers
    getCurrentUserColor,
    isColorAvailable,

    // Current user's color info
    currentUserColor: getCurrentUserColor(),
    hasSelectedColor: !!getCurrentUserColor(),
  };
};
