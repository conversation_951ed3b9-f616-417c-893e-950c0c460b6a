import { useCallback } from 'react';
import { prizeWheelError<PERSON><PERSON><PERSON>, PrizeWheelError, PrizeWheelErrorCode } from '../utils/prizeWheelErrorHandler';

export interface ErrorHandlerOptions {
  showToast?: boolean;
  logError?: boolean;
  context?: string;
  onError?: (error: PrizeWheelError) => void;
}

export const usePrizeWheelErrorHandler = () => {
  /**
   * Handle errors with consistent processing
   */
  const handleError = useCallback((
    error: any, 
    options: ErrorHandlerOptions = {}
  ): PrizeWheelError => {
    const {
      showToast = true,
      logError = true,
      context,
      onError,
    } = options;

    // Convert to PrizeWheelError format
    const prizeWheelError = prizeWheelErrorHandler.handleApiError(error, context);

    // Log error if enabled
    if (logError) {
      prizeWheelErrorHandler.logError(prizeWheelError);
    }

    // Show toast if enabled
    if (showToast) {
      prizeWheelErrorHandler.showErrorToast(prizeWheelError);
    }

    // Call custom error handler if provided
    if (onError) {
      onError(prizeWheelError);
    }

    return prizeWheelError;
  }, []);

  /**
   * Handle balance-related errors with specific logic
   */
  const handleBalanceError = useCallback((
    error: any,
    currentBalance?: number,
    requiredAmount?: number
  ): PrizeWheelError => {
    const prizeWheelError = handleError(error, {
      context: 'balance_validation',
      showToast: false, // We'll show a custom toast
    });

    // Enhanced balance error handling
    if (prizeWheelError.code === PrizeWheelErrorCode.INSUFFICIENT_BALANCE) {
      // Add balance details if not already present
      if (!prizeWheelError.details && currentBalance !== undefined && requiredAmount !== undefined) {
        prizeWheelError.details = {
          currentBalance: currentBalance * 100, // Convert to cents
          requiredAmount: requiredAmount * 100,
          shortfall: (requiredAmount - currentBalance) * 100,
        };
      }
    }

    // Show custom toast for balance errors
    prizeWheelErrorHandler.showErrorToast(prizeWheelError);

    return prizeWheelError;
  }, [handleError]);

  /**
   * Handle entry fee processing errors
   */
  const handleEntryFeeError = useCallback((
    error: any,
    userId?: string,
    roomId?: string,
    amount?: number
  ): PrizeWheelError => {
    const prizeWheelError = handleError(error, {
      context: 'entry_fee_processing',
    });

    // Add entry fee context
    if (prizeWheelError.details) {
      prizeWheelError.details = {
        ...prizeWheelError.details,
        userId,
        roomId,
        amount: amount ? amount * 100 : undefined, // Convert to cents
      };
    }

    return prizeWheelError;
  }, [handleError]);

  /**
   * Handle prize pool errors
   */
  const handlePrizePoolError = useCallback((
    error: any,
    prizePoolId?: string,
    roomId?: string
  ): PrizeWheelError => {
    const prizeWheelError = handleError(error, {
      context: 'prize_pool_management',
    });

    // Add prize pool context
    if (prizeWheelError.details) {
      prizeWheelError.details = {
        ...prizeWheelError.details,
        prizePoolId,
        roomId,
      };
    }

    return prizeWheelError;
  }, [handleError]);

  /**
   * Handle transaction errors
   */
  const handleTransactionError = useCallback((
    error: any,
    transactionId?: string,
    transactionType?: string
  ): PrizeWheelError => {
    const prizeWheelError = handleError(error, {
      context: 'transaction_processing',
    });

    // Add transaction context
    if (prizeWheelError.details) {
      prizeWheelError.details = {
        ...prizeWheelError.details,
        transactionId,
        transactionType,
      };
    }

    return prizeWheelError;
  }, [handleError]);

  /**
   * Handle room management errors
   */
  const handleRoomError = useCallback((
    error: any,
    roomId?: string,
    action?: string
  ): PrizeWheelError => {
    const prizeWheelError = handleError(error, {
      context: 'room_management',
    });

    // Add room context
    if (prizeWheelError.details) {
      prizeWheelError.details = {
        ...prizeWheelError.details,
        roomId,
        action,
      };
    }

    return prizeWheelError;
  }, [handleError]);

  /**
   * Handle API errors with retry logic
   */
  const handleApiErrorWithRetry = useCallback(async (
    apiCall: () => Promise<any>,
    maxRetries: number = 3,
    retryDelay: number = 1000,
    options: ErrorHandlerOptions = {}
  ): Promise<any> => {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error;

        // Don't retry for certain error types
        const prizeWheelError = prizeWheelErrorHandler.handleApiError(error);
        const nonRetryableErrors = [
          PrizeWheelErrorCode.INSUFFICIENT_BALANCE,
          PrizeWheelErrorCode.UNAUTHORIZED,
          PrizeWheelErrorCode.FORBIDDEN,
          PrizeWheelErrorCode.VALIDATION_ERROR,
          PrizeWheelErrorCode.ENTRY_FEE_ALREADY_PAID,
          PrizeWheelErrorCode.PRIZE_POOL_ALREADY_DISTRIBUTED,
        ];

        if (nonRetryableErrors.includes(prizeWheelError.code)) {
          break;
        }

        // Wait before retrying (except on last attempt)
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
        }
      }
    }

    // Handle the final error
    throw handleError(lastError, {
      ...options,
      context: `${options.context || 'api_call'}_after_${maxRetries}_retries`,
    });
  }, [handleError]);

  /**
   * Get user-friendly error message
   */
  const getErrorMessage = useCallback((error: any): string => {
    const prizeWheelError = prizeWheelErrorHandler.handleApiError(error);
    return prizeWheelErrorHandler.getUserFriendlyMessage(prizeWheelError);
  }, []);

  /**
   * Check if error is of specific type
   */
  const isErrorType = useCallback((error: any, errorCode: PrizeWheelErrorCode): boolean => {
    const prizeWheelError = prizeWheelErrorHandler.handleApiError(error);
    return prizeWheelError.code === errorCode;
  }, []);

  /**
   * Check if error is retryable
   */
  const isRetryableError = useCallback((error: any): boolean => {
    const prizeWheelError = prizeWheelErrorHandler.handleApiError(error);
    const retryableErrors = [
      PrizeWheelErrorCode.API_TIMEOUT,
      PrizeWheelErrorCode.NETWORK_ERROR,
      PrizeWheelErrorCode.SERVER_ERROR,
    ];
    return retryableErrors.includes(prizeWheelError.code);
  }, []);

  return {
    handleError,
    handleBalanceError,
    handleEntryFeeError,
    handlePrizePoolError,
    handleTransactionError,
    handleRoomError,
    handleApiErrorWithRetry,
    getErrorMessage,
    isErrorType,
    isRetryableError,
  };
};

export default usePrizeWheelErrorHandler;
