import { useEffect } from 'react';
import { socketService } from '@/services/socket';
import { useGameStore } from '@/store/gameStore';
import { useAuthStore } from '@/store/authStore';
import { env } from '@/config/env';
import type {
  ColorStateUpdateData,
  RoomUpdateSpecData,
  BalanceUpdateData,
  WheelColorStatusData,
  RoomColorStateData,
} from '@/types/socket';

/**
 * Hook to manage real-time game data updates from WebSocket events
 * Connects socket events to the appropriate stores for state management
 */
export const useRealTimeGameData = () => {
  const gameStore = useGameStore();
  const { updateBalance } = useAuthStore();

  useEffect(() => {
    // Enhanced color state update handler
    const handleColorStateUpdate = (data: ColorStateUpdateData) => {
      gameStore.handleColorStateUpdate(data);
      
      if (env.DEBUG) {
        console.log('Real-time color state update processed:', {
          roomId: data.roomId,
          playerColorCount: Object.keys(data.playerColors).length,
          availableColorCount: data.availableColors.length,
          timestamp: data.timestamp,
        });
      }
    };

    // Enhanced room update handler with real player data
    const handleRoomUpdateSpec = (data: RoomUpdateSpecData) => {
      gameStore.handleRoomUpdateSpec(data);
      
      if (env.DEBUG) {
        console.log('Real-time room update processed:', {
          roomId: data.roomId,
          gameState: data.gameState,
          participantCount: data.participantCount,
          prizePool: data.prizePool,
          playerCount: Object.keys(data.players).length,
          timestamp: data.timestamp,
        });
      }
    };

    // Balance update handler
    const handleBalanceUpdate = (data: BalanceUpdateData) => {
      gameStore.handleBalanceUpdate(data);
      
      // Update auth store balance if it's for the current user
      const currentUser = useAuthStore.getState().user;
      if (currentUser && data.userId === currentUser.id) {
        updateBalance(data.balance);
      }
      
      if (env.DEBUG) {
        console.log('Real-time balance update processed:', {
          userId: data.userId,
          username: data.username,
          newBalance: data.balance,
          previousBalance: data.previousBalance,
          transactionType: data.transaction.type,
          transactionAmount: data.transaction.amount,
          timestamp: data.timestamp,
        });
      }
    };

    // Room color state handler - now the primary handler for color updates
    const handleRoomColorStateUpdated = (data: RoomColorStateData) => {
      // Update all player color selections from the comprehensive state
      if (data.playerColors) {
        Object.entries(data.playerColors).forEach(([playerId, playerInfo]) => {
          if (playerInfo.colorId) {
            gameStore.updatePlayerColorSelection(playerId, playerInfo.colorId);
          }
        });
      }
      
      if (env.DEBUG) {
        console.log('Room color state update processed:', {
          roomId: data.roomId,
          selectorUserId: data.selectorUserId,
          selectedColorId: data.selectedColorId,
          playerColorsCount: Object.keys(data.playerColors || {}).length,
          timestamp: data.timestamp,
        });
      }
    };

    // Register event listeners
    socketService.on('colorStateUpdate', handleColorStateUpdate);
    socketService.on('room_update_spec', handleRoomUpdateSpec);
    socketService.on('balance_updated', handleBalanceUpdate);
    socketService.on('room_color_state_updated', handleRoomColorStateUpdated);

    // Cleanup function to remove event listeners
    return () => {
      socketService.off('colorStateUpdate', handleColorStateUpdate);
      socketService.off('room_update_spec', handleRoomUpdateSpec);
      socketService.off('balance_updated', handleBalanceUpdate);
      socketService.off('room_color_state_updated', handleRoomColorStateUpdated);
    };
  }, [gameStore, updateBalance]);

  // Return utility functions for manual data refresh if needed
  return {
    // Force refresh real-time data (if needed)
    refreshRealTimeData: () => {
      if (env.DEBUG) {
        console.log('Real-time data refresh requested');
      }
      // Could implement manual data refresh logic here if needed
    },
    
    // Get current real-time state
    getRealTimeState: () => ({
      participantCount: gameStore.getRealTimeParticipantCount(),
      prizePool: gameStore.getRealTimePrizePool(),
      colorState: gameStore.getRealTimeColorState(),
    }),
  };
};

/**
 * Hook specifically for Prize Wheel real-time data
 * Provides easy access to Prize Wheel specific real-time data
 */
export const usePrizeWheelRealTimeData = () => {
  const {
    getRealTimeParticipantCount,
    getRealTimePrizePool,
    getRealTimeColorState,
    getPlayerBalance,
    getPlayerInsufficientBalance,
  } = useGameStore();

  // Use the main real-time data hook
  useRealTimeGameData();

  return {
    participantCount: getRealTimeParticipantCount(),
    prizePool: getRealTimePrizePool(),
    colorState: getRealTimeColorState(),
    getPlayerBalance,
    getPlayerInsufficientBalance,
    
    // Helper functions for Prize Wheel specific data
    getAvailableColors: () => {
      const colorState = getRealTimeColorState();
      return colorState?.availableColors || [];
    },
    
    getTakenColors: () => {
      const colorState = getRealTimeColorState();
      return colorState?.takenColors || {};
    },
    
    getPlayerColors: () => {
      const colorState = getRealTimeColorState();
      return colorState?.playerColors || {};
    },
  };
};

export default useRealTimeGameData;
