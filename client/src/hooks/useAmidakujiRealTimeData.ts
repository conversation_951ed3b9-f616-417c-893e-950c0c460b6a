import { useEffect } from 'react';
import { socketService } from '@/services/socket';
import { useGameStore } from '@/store/gameStore';
import { useAuthStore } from '@/store/authStore';
import { env } from '@/config/env';
import type {
  PositionStateUpdateData,
  RoomUpdateSpecData,
  BalanceUpdateData,
  AmidakujiPositionStatusData,
  RoomPositionStateData,
} from '@/types/socket';

/**
 * Hook to manage real-time Amidakuji game data updates from WebSocket events
 * Connects socket events to the appropriate stores for state management
 */
export const useAmidakujiRealTimeData = () => {
  const gameStore = useGameStore();
  const { updateBalance } = useAuthStore();

  useEffect(() => {
    // Handle position state updates
    const handlePositionStateUpdate = (data: PositionStateUpdateData) => {
      if (env.DEBUG) {
        console.log('Position state update received:', data);
      }
      gameStore.updateRealTimePositionState(data);
    };

    // Handle room updates with position information
    const handleRoomUpdateSpec = (data: RoomUpdateSpecData) => {
      if (env.DEBUG) {
        console.log('Room update spec received (Amidakuji):', data);
      }
      
      // Update room data in game store if it contains position information
      if (data.players) {
        gameStore.updateRealTimeRoomData(data);
      }
    };

    // Handle balance updates
    const handleBalanceUpdate = (data: BalanceUpdateData) => {
      if (env.DEBUG) {
        console.log('Balance update received (Amidakuji):', data);
      }
      
      // Update user balance in auth store
      updateBalance(data.balance);

      // Update player balance in game store for real-time display (if method exists)
      if ('updatePlayerBalance' in gameStore && typeof gameStore.updatePlayerBalance === 'function') {
        (gameStore as any).updatePlayerBalance(data.userId, data.balance);
      }
    };

    // Handle Amidakuji position selection events
    const handleAmidakujiPositionSelected = (data: AmidakujiPositionStatusData) => {
      if (env.DEBUG) {
        console.log('Amidakuji position selected:', data);
      }
      
      // Update position selection in game store
      gameStore.updatePlayerPositionSelection(data.playerId, data.position);
    };

    // Handle room position state updates
    const handleRoomPositionStateUpdated = (data: RoomPositionStateData) => {
      if (env.DEBUG) {
        console.log('Room position state updated:', data);
      }
      
      // Update all position selections and ready states
      gameStore.updateRealTimePositionState(data);
    };

    // Handle Amidakuji pattern generation
    const handleAmidakujiPattern = (data: any) => {
      if (env.DEBUG) {
        console.log('Amidakuji pattern generated:', data);
      }
      
      // Update game store with pattern data
      gameStore.updateAmidakujiPattern(data);
    };

    // Handle path tracing events
    const handlePathTracing = (data: any) => {
      if (env.DEBUG) {
        console.log('Amidakuji path tracing:', data);
      }
      
      // Update game store with path tracing data
      gameStore.updateAmidakujiPathTracing(data);
    };

    // Register event listeners
    socketService.on('positionStateUpdate', handlePositionStateUpdate);
    socketService.on('room_update_spec', handleRoomUpdateSpec);
    socketService.on('balance_updated', handleBalanceUpdate);
    socketService.on('amidakuji_position_selected', handleAmidakujiPositionSelected);
    socketService.on('room_position_state_updated', handleRoomPositionStateUpdated);
    socketService.on('amidakuji_pattern', handleAmidakujiPattern);
    socketService.on('path_tracing', handlePathTracing);

    // Cleanup function to remove event listeners
    return () => {
      socketService.off('positionStateUpdate', handlePositionStateUpdate);
      socketService.off('room_update_spec', handleRoomUpdateSpec);
      socketService.off('balance_updated', handleBalanceUpdate);
      socketService.off('amidakuji_position_selected', handleAmidakujiPositionSelected);
      socketService.off('room_position_state_updated', handleRoomPositionStateUpdated);
      socketService.off('amidakuji_pattern', handleAmidakujiPattern);
      socketService.off('path_tracing', handlePathTracing);
    };
  }, [gameStore, updateBalance]);

  // Return utility functions for Amidakuji specific data
  return {
    // Force refresh real-time data (if needed)
    refreshRealTimeData: () => {
      if (env.DEBUG) {
        console.log('Amidakuji real-time data refresh requested');
      }
      // Could implement manual data refresh logic here if needed
    },
    
    // Get current real-time state
    getRealTimeState: () => ({
      participantCount: gameStore.getRealTimeParticipantCount(),
      prizePool: gameStore.getRealTimePrizePool(),
      positionState: gameStore.getRealTimePositionState(),
    }),

    // Helper functions for Amidakuji specific data
    getAvailablePositions: () => {
      const positionState = gameStore.getRealTimePositionState();
      return positionState?.availablePositions || [];
    },
    
    getTakenPositions: () => {
      const positionState = gameStore.getRealTimePositionState();
      return positionState?.takenPositions || {};
    },
    
    getPlayerPositions: () => {
      const positionState = gameStore.getRealTimePositionState();
      return positionState?.playerPositions || {};
    },

    // Player balance and status functions
    getPlayerBalance: (playerId: string) => {
      return gameStore.getPlayerBalance(playerId);
    },

    getPlayerInsufficientBalance: (playerId: string) => {
      return gameStore.getPlayerInsufficientBalance(playerId);
    },

    // Amidakuji specific game data
    getAmidakujiPattern: () => {
      return gameStore.getAmidakujiPattern();
    },

    getAmidakujiPathTracing: () => {
      return gameStore.getAmidakujiPathTracing();
    },
  };
};

export default useAmidakujiRealTimeData;
