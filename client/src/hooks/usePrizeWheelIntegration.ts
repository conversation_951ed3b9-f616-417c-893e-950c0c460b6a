import { useCallback, useMemo } from 'react';
import { usePrizePoolData } from './usePrizePoolData';
import { useRealTimeBalance } from './useRealTimeBalance';
import { useEntryFeeProcessing } from './useEntryFeeProcessing';
import { usePrizeWheelErrorHandler } from './usePrizeWheelErrorHandler';
import { useAuthStore } from '../store/authStore';
import { useGameStore } from '../store/gameStore';

export interface PrizeWheelIntegrationState {
  // Prize Pool
  prizePool: ReturnType<typeof usePrizePoolData>;
  
  // Balance Management
  balance: ReturnType<typeof useRealTimeBalance>;
  
  // Entry Fee Processing
  entryFee: ReturnType<typeof useEntryFeeProcessing>;
  
  // Overall State
  isReady: boolean;
  canParticipate: boolean;
  participationBlockers: string[];
  gamePhase: 'setup' | 'ready' | 'playing' | 'finished';
}

export interface PrizeWheelIntegrationActions {
  // Combined Actions
  prepareForGame: () => Promise<boolean>;
  exitGame: () => Promise<boolean>;
  refreshAllData: () => Promise<void>;
  
  // Validation
  validateParticipation: () => { canParticipate: boolean; blockers: string[] };
}

export const usePrizeWheelIntegration = (roomId: string, betAmount: number = 0) => {
  const { user } = useAuthStore();
  const { playerReadyStates } = useGameStore();
  const { handleError } = usePrizeWheelErrorHandler();

  // Initialize hooks
  const prizePool = usePrizePoolData(roomId);
  const balance = useRealTimeBalance();
  const entryFee = useEntryFeeProcessing(roomId, betAmount);

  // Get current user's ready state
  const isReady = user ? (playerReadyStates[user.id] || false) : false;

  // Validate participation eligibility
  const validateParticipation = useCallback(() => {
    const blockers: string[] = [];

    // Check if user is logged in
    if (!user) {
      blockers.push('Must be logged in to participate');
    }

    // Check if room has prize pool
    if (!prizePool.prizePool) {
      blockers.push('Prize pool not available');
    }

    // Check if prize pool is in correct state
    if (prizePool.prizePool && !prizePool.isAccumulating) {
      blockers.push('Prize pool is not accepting entries');
    }

    // Check balance requirements
    if (betAmount > 0) {
      if (balance.hasInsufficientBalance(betAmount)) {
        const shortfall = balance.getShortfall(betAmount);
        blockers.push(`Insufficient balance (need $${shortfall.toFixed(2)} more)`);
      }

      // Check entry fee status
      if (!entryFee.isPaid && entryFee.isRequired) {
        blockers.push('Entry fee must be paid');
      }

      if (entryFee.isFailed) {
        blockers.push('Entry fee payment failed');
      }
    }

    // Check room capacity
    if (prizePool.currentPlayers >= prizePool.maxPlayers) {
      blockers.push('Room is full');
    }

    // Check if balance validation is in progress
    if (balance.isValidating) {
      blockers.push('Balance validation in progress');
    }

    // Check if entry fee processing is in progress
    if (entryFee.processing) {
      blockers.push('Entry fee processing in progress');
    }

    return {
      canParticipate: blockers.length === 0,
      blockers,
    };
  }, [
    user,
    prizePool.prizePool,
    prizePool.isAccumulating,
    prizePool.currentPlayers,
    prizePool.maxPlayers,
    betAmount,
    balance,
    entryFee,
  ]);

  // Prepare for game (validate balance and pay entry fee)
  const prepareForGame = useCallback(async (): Promise<boolean> => {
    try {
      // Validate participation first
      const validation = validateParticipation();
      if (!validation.canParticipate) {
        console.warn('Cannot participate:', validation.blockers);
        return false;
      }

      // If entry fee is required and not paid, process it
      if (entryFee.isRequired && !entryFee.isPaid) {
        const feeProcessed = await entryFee.actions.processEntryFee();
        if (!feeProcessed) {
          return false;
        }
      }

      // Validate balance one more time
      if (betAmount > 0) {
        const balanceValid = await balance.actions.validateBalance(betAmount);
        if (!balanceValid) {
          return false;
        }
      }

      return true;
    } catch (error) {
      handleError(error, { context: 'prepare_for_game' });
      return false;
    }
  }, [validateParticipation, entryFee, betAmount, balance.actions, handleError]);

  // Exit game (process refund if applicable)
  const exitGame = useCallback(async (): Promise<boolean> => {
    try {
      // If entry fee was paid and can be refunded, process refund
      if (entryFee.canRefund) {
        const refundProcessed = await entryFee.actions.processRefund();
        if (!refundProcessed) {
          console.warn('Failed to process refund, but allowing exit');
        }
      }

      return true;
    } catch (error) {
      handleError(error, { context: 'exit_game' });
      return false;
    }
  }, [entryFee.canRefund, entryFee.actions, handleError]);

  // Refresh all data
  const refreshAllData = useCallback(async (): Promise<void> => {
    try {
      await Promise.all([
        prizePool.actions.refreshData(),
        balance.actions.refreshBalance(),
        entryFee.actions.checkPaymentStatus(),
      ]);
    } catch (error) {
      handleError(error, { context: 'refresh_all_data', showToast: false });
    }
  }, [prizePool.actions, balance.actions, entryFee.actions, handleError]);

  // Determine game phase
  const gamePhase = useMemo(() => {
    if (!prizePool.prizePool) return 'setup';
    if (prizePool.isAccumulating) return 'ready';
    if (prizePool.isLocked) return 'playing';
    if (prizePool.isDistributed) return 'finished';
    return 'setup';
  }, [prizePool.prizePool, prizePool.isAccumulating, prizePool.isLocked, prizePool.isDistributed]);

  // Get current participation status
  const { canParticipate, blockers: participationBlockers } = validateParticipation();

  const state: PrizeWheelIntegrationState = {
    prizePool,
    balance,
    entryFee,
    isReady,
    canParticipate,
    participationBlockers,
    gamePhase,
  };

  const actions: PrizeWheelIntegrationActions = {
    prepareForGame,
    exitGame,
    refreshAllData,
    validateParticipation,
  };

  return {
    ...state,
    actions,
  };
};

export default usePrizeWheelIntegration;
