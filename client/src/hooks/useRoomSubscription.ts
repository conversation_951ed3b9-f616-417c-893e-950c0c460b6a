/**
 * Enhanced Room Subscription Hook
 * Provides easy-to-use interface for room subscription management
 * with automatic lobby fallback and real-time room info updates
 */

import { useCallback, useEffect, useState } from 'react';
import { useSocketStore } from '@/store/socketStore';
import { useLobbyStore } from '@/store/lobbyStore';
import { enhancedSubscriptionManager } from '@/utils/enhancedSubscriptionManager';
import { env } from '@/config/env';

interface RoomSubscriptionState {
  isJoining: boolean;
  isLeaving: boolean;
  waitingForRoomInfo: boolean;
  currentSubscription: 'lobby' | 'room' | null;
  roomId: string | null;
  error: Error | null;
}

interface RoomInfoUpdate {
  roomId: string;
  // Complete room_info_updated data structure
  room?: {
    id: string;
    name: string;
    gameType: string;
    status: string;
    playerCount: number;
    maxPlayers: number;
    betAmount: number;
    prizePool: number;
    isPrivate: boolean;
    createdAt: string;
    updatedAt: string;
  };
  roomState?: {
    playerCount: number;
    readyCount: number;
    canStartGame: boolean;
    prizePool: number;
    gameInProgress: boolean;
    countdown: number | null;
  };
  players?: Array<{
    betAmount: number;
    isReady: boolean;
    joinedAt: string;
    position: number;
    userId: string;
    username: string;
  }>;
  gameConfig?: {
    betAmount: number;
    gameType: string;
    maxPlayers: number;
    minPlayers: number;
    settings: any;
  };
  gameSpecificData?: {
    gameType: string;
    colorSelections: Record<string, string>;
    availableColors: string[];
    playerColorMappings: Record<string, string>;
    colorSelectionTimestamps: Record<string, string>;
  };
  timestamp?: string;
  // Legacy compatibility fields
  playerCount?: number;
  maxPlayers?: number;
  status?: string;
  action?: 'player_joined' | 'player_left' | 'game_started' | 'game_ended' | 'room_updated';
  playerName?: string;
}

interface UseRoomSubscriptionOptions {
  onRoomInfoUpdate?: (update: RoomInfoUpdate) => void;
  onSubscriptionChange?: (type: 'lobby' | 'room' | null, roomId?: string) => void;
  autoLobbyFallback?: boolean;
}

export function useRoomSubscription(options: UseRoomSubscriptionOptions = {}) {
  const { onRoomInfoUpdate, onSubscriptionChange, autoLobbyFallback = true } = options;
  
  const [state, setState] = useState<RoomSubscriptionState>({
    isJoining: false,
    isLeaving: false,
    waitingForRoomInfo: false,
    currentSubscription: null,
    roomId: null,
    error: null,
  });

  const socketStore = useSocketStore();
  const lobbyStore = useLobbyStore();

  // Update subscription state based on current stores
  useEffect(() => {
    const currentState = enhancedSubscriptionManager.getCurrentState();
    setState(prev => ({
      ...prev,
      currentSubscription: currentState.currentSubscription,
      roomId: currentState.roomId,
      waitingForRoomInfo: socketStore.waitingForRoomInfo,
    }));
  }, [socketStore.currentRoom, socketStore.waitingForRoomInfo, lobbyStore.isSubscribed]);

  // Listen for room info updates
  useEffect(() => {
    const handleRoomInfoUpdate = (event: CustomEvent) => {
      const { roomId, roomInfo } = event.detail;
      
      if (env.DEBUG) {
        console.log('useRoomSubscription: Room info update received:', { roomId, roomInfo });
      }

      onRoomInfoUpdate?.({
        roomId,
        // Pass complete room_info_updated data structure
        room: roomInfo.room,
        roomState: roomInfo.roomState,
        players: roomInfo.players,
        gameConfig: roomInfo.gameConfig,
        gameSpecificData: roomInfo.gameSpecificData,
        timestamp: roomInfo.timestamp,
        // Legacy compatibility fields
        playerCount: roomInfo.playerCount || roomInfo.roomState?.playerCount,
        maxPlayers: roomInfo.maxPlayers || roomInfo.room?.maxPlayers,
        status: roomInfo.status || roomInfo.room?.status,
        action: roomInfo.action,
        playerName: roomInfo.playerName,
      });
    };

    window.addEventListener('roomInfoUpdate', handleRoomInfoUpdate as EventListener);
    return () => {
      window.removeEventListener('roomInfoUpdate', handleRoomInfoUpdate as EventListener);
    };
  }, [onRoomInfoUpdate]);

  // Listen for lobby updates
  useEffect(() => {
    const handleLobbyUpdate = (event: CustomEvent) => {
      const lobbyData = event.detail;
      
      if (env.DEBUG) {
        console.log('useRoomSubscription: Lobby update received:', lobbyData);
      }

      // Trigger lobby store update if needed
      if (lobbyStore.isSubscribed) {
        // The lobby store will handle the update
      }
    };

    window.addEventListener('lobbyUpdate', handleLobbyUpdate as EventListener);
    return () => {
      window.removeEventListener('lobbyUpdate', handleLobbyUpdate as EventListener);
    };
  }, [lobbyStore.isSubscribed]);

  // Enhanced room join with automatic subscription management
  const joinRoom = useCallback(async (
    roomId: string, 
    password?: string, 
    betAmount?: number
  ): Promise<void> => {
    setState(prev => ({ ...prev, isJoining: true, error: null }));

    try {
      await socketStore.joinRoom(roomId, password, betAmount);
      
      // Notify about subscription change
      onSubscriptionChange?.('room', roomId);
      
      setState(prev => ({ 
        ...prev, 
        isJoining: false, 
        currentSubscription: 'room',
        roomId,
      }));

      if (env.DEBUG) {
        console.log('useRoomSubscription: Room join completed successfully:', roomId);
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isJoining: false, 
        error: error as Error,
        // If auto fallback is enabled, we should be back in lobby
        currentSubscription: autoLobbyFallback ? 'lobby' : null,
        roomId: null,
      }));

      // Notify about subscription change (fallback to lobby)
      if (autoLobbyFallback) {
        onSubscriptionChange?.('lobby');
      }

      throw error;
    }
  }, [socketStore, onSubscriptionChange, autoLobbyFallback]);

  // Enhanced room leave with automatic lobby subscription
  const leaveRoom = useCallback(async (
    roomId: string, 
    reason: 'voluntary' | 'disconnected' | 'kicked' = 'voluntary'
  ): Promise<void> => {
    setState(prev => ({ ...prev, isLeaving: true, error: null }));

    try {
      await socketStore.leaveRoom(roomId, reason);
      
      // Notify about subscription change
      onSubscriptionChange?.('lobby');
      
      setState(prev => ({ 
        ...prev, 
        isLeaving: false,
        currentSubscription: 'lobby',
        roomId: null,
      }));

      if (env.DEBUG) {
        console.log('useRoomSubscription: Room leave completed successfully:', roomId);
      }
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLeaving: false, 
        error: error as Error,
      }));

      throw error;
    }
  }, [socketStore, onSubscriptionChange]);

  // Subscribe to lobby manually
  const subscribeToLobby = useCallback(async (): Promise<void> => {
    try {
      await lobbyStore.subscribeLobby();
      
      setState(prev => ({ 
        ...prev, 
        currentSubscription: 'lobby',
        roomId: null,
      }));

      onSubscriptionChange?.('lobby');

      if (env.DEBUG) {
        console.log('useRoomSubscription: Lobby subscription completed successfully');
      }
    } catch (error) {
      setState(prev => ({ ...prev, error: error as Error }));
      throw error;
    }
  }, [lobbyStore, onSubscriptionChange]);

  // Unsubscribe from lobby manually
  const unsubscribeFromLobby = useCallback(async (): Promise<void> => {
    try {
      await lobbyStore.unsubscribeLobby();
      
      setState(prev => ({ 
        ...prev, 
        currentSubscription: null,
        roomId: null,
      }));

      onSubscriptionChange?.(null);

      if (env.DEBUG) {
        console.log('useRoomSubscription: Lobby unsubscription completed successfully');
      }
    } catch (error) {
      setState(prev => ({ ...prev, error: error as Error }));
      throw error;
    }
  }, [lobbyStore, onSubscriptionChange]);

  // Get current subscription state
  const getSubscriptionState = useCallback(() => {
    return enhancedSubscriptionManager.getCurrentState();
  }, []);

  // Get transition history for debugging
  const getTransitionHistory = useCallback(() => {
    return enhancedSubscriptionManager.getTransitionHistory();
  }, []);

  return {
    // State
    ...state,
    isConnected: socketStore.isConnected,
    currentRoom: socketStore.currentRoom,
    lobbyRooms: [], // Lobby rooms are handled via room list updates
    
    // Actions
    joinRoom,
    leaveRoom,
    subscribeToLobby,
    unsubscribeFromLobby,
    
    // Utilities
    getSubscriptionState,
    getTransitionHistory,
    
    // Store actions (for backward compatibility)
    clearNotifications: socketStore.clearNotifications,
    removeNotification: socketStore.removeNotification,
  };
}

export type { RoomSubscriptionState, RoomInfoUpdate, UseRoomSubscriptionOptions };
