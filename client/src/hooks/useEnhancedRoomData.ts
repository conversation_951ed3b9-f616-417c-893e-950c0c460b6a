import { useMemo } from 'react';
import { useSocketStore } from '../store/socketStore';
import { useGameStore } from '../store/gameStore';
import { PrizeWheelGameData, AmidakujiGameData } from '../types/socket';

/**
 * Hook to access enhanced room data from room_info_updated events
 * Provides game-specific data with fallback to legacy data sources
 */
export const useEnhancedRoomData = () => {
  const { currentRoom, enhancedGameData } = useSocketStore();
  const { getRealTimeColorState, getRealTimePositionState } = useGameStore();

  // Extract game-specific data from the enhanced room data
  const gameSpecificData = useMemo(() => {
    return enhancedGameData;
  }, [enhancedGameData]);

  // Prize Wheel specific data
  const prizeWheelData = useMemo((): PrizeWheelGameData | null => {
    if (!gameSpecificData ||
        (gameSpecificData.gameType !== 'prizewheel' && gameSpecificData.gameType !== 'prize_wheel')) {
      return null;
    }

    // Check if this is actually complete prize wheel data with required properties
    const hasRequiredProperties = 'colorSelections' in gameSpecificData ||
                                 'availableColors' in gameSpecificData ||
                                 'playerColorMappings' in gameSpecificData;

    if (!hasRequiredProperties) {
      // This is just a basic gameSpecificData with only gameType, not full prize wheel data
      if (process.env.NODE_ENV === 'development') {
        console.log('gameSpecificData exists but lacks prize wheel properties:', gameSpecificData);
      }
      return null;
    }

    return gameSpecificData as PrizeWheelGameData;
  }, [gameSpecificData]);

  // Amidakuji specific data
  const amidakujiData = useMemo((): AmidakujiGameData | null => {
    if (!gameSpecificData || gameSpecificData.gameType !== 'amidakuji') {
      return null;
    }

    // Check if this is actually complete amidakuji data with required properties
    const hasRequiredProperties = 'positionSelections' in gameSpecificData ||
                                 'availablePositions' in gameSpecificData ||
                                 'playerPositionMappings' in gameSpecificData;

    if (!hasRequiredProperties) {
      // This is just a basic gameSpecificData with only gameType, not full amidakuji data
      if (process.env.NODE_ENV === 'development') {
        console.log('gameSpecificData exists but lacks amidakuji properties:', gameSpecificData);
      }
      return null;
    }

    return gameSpecificData as AmidakujiGameData;
  }, [gameSpecificData]);

  // Enhanced color data for Prize Wheel with fallback to legacy data
  const getEnhancedColorData = useMemo(() => {
    return () => {
      // Debug logging to understand the data structure
      if (process.env.NODE_ENV === 'development') {
        console.log('getEnhancedColorData called with:', {
          hasPrizeWheelData: !!prizeWheelData,
          prizeWheelDataKeys: prizeWheelData ? Object.keys(prizeWheelData) : [],
          hasColorSelections: !!(prizeWheelData && prizeWheelData.colorSelections),
          colorSelectionsType: prizeWheelData?.colorSelections ? typeof prizeWheelData.colorSelections : 'undefined',
          colorSelectionsValue: prizeWheelData?.colorSelections,
        });
      }

      if (prizeWheelData && prizeWheelData.colorSelections && typeof prizeWheelData.colorSelections === 'object') {
        // Use enhanced game-specific data with comprehensive null checks
        const colorSelections = prizeWheelData.colorSelections || {};
        const colorValues = Object.values(colorSelections).filter(Boolean); // Filter out null/undefined values

        return {
          playerColors: colorSelections,
          availableColors: prizeWheelData.availableColors || [],
          takenColors: Object.fromEntries(
            colorValues.map(colorId => [colorId, true])
          ),
          playerColorMappings: prizeWheelData.playerColorMappings || {},
          colorSelectionTimestamps: prizeWheelData.colorSelectionTimestamps || {},
          source: 'enhanced' as const,
        };
      }

      // Fallback to legacy real-time data
      const legacyColorState = getRealTimeColorState();
      if (legacyColorState) {
        return {
          playerColors: legacyColorState.playerColors,
          availableColors: legacyColorState.availableColors,
          takenColors: legacyColorState.takenColors,
          playerColorMappings: {},
          colorSelectionTimestamps: {},
          source: 'legacy' as const,
        };
      }

      // No data available
      return {
        playerColors: {},
        availableColors: [],
        takenColors: {},
        playerColorMappings: {},
        colorSelectionTimestamps: {},
        source: 'none' as const,
      };
    };
  }, [prizeWheelData, getRealTimeColorState]);

  // Enhanced position data for Amidakuji with fallback to legacy data
  const getEnhancedPositionData = useMemo(() => {
    return () => {
      if (amidakujiData && amidakujiData.positionSelections && typeof amidakujiData.positionSelections === 'object') {
        // Use enhanced game-specific data with comprehensive null checks
        const positionSelections = amidakujiData.positionSelections || {};
        const positionValues = Object.values(positionSelections).filter(Boolean); // Filter out null/undefined values

        return {
          playerPositions: positionSelections,
          availablePositions: amidakujiData.availablePositions || [],
          takenPositions: Object.fromEntries(
            positionValues.map(position => [position, true])
          ),
          playerPositionMappings: amidakujiData.playerPositionMappings || {},
          source: 'enhanced' as const,
        };
      }

      // Fallback to legacy real-time data
      const legacyPositionState = getRealTimePositionState();
      if (legacyPositionState) {
        return {
          playerPositions: legacyPositionState.playerPositions,
          availablePositions: legacyPositionState.availablePositions,
          takenPositions: legacyPositionState.takenPositions,
          playerPositionMappings: {},
          source: 'legacy' as const,
        };
      }

      // No data available
      return {
        playerPositions: {},
        availablePositions: [],
        takenPositions: {},
        playerPositionMappings: {},
        source: 'none' as const,
      };
    };
  }, [amidakujiData, getRealTimePositionState]);

  // Check if enhanced data is available
  const hasEnhancedData = useMemo(() => {
    return !!gameSpecificData;
  }, [gameSpecificData]);

  // Get game type from enhanced data or fallback to room data
  const gameType = useMemo(() => {
    return gameSpecificData?.gameType || currentRoom?.gameType?.toLowerCase();
  }, [gameSpecificData, currentRoom]);

  return {
    // Raw game-specific data
    gameSpecificData,
    prizeWheelData,
    amidakujiData,

    // Enhanced data getters
    getEnhancedColorData,
    getEnhancedPositionData,

    // Utility functions
    hasEnhancedData,
    gameType,
    
    // Convenience flags
    isPrizeWheel: gameType === 'prizewheel' || gameType === 'prize_wheel',
    isAmidakuji: gameType === 'amidakuji',
  };
};

export default useEnhancedRoomData;
