import { useState, useEffect, useCallback } from 'react';
import { useAuthStore } from '../store/authStore';
import { useGameStore } from '../store/gameStore';
import { usePrizeWheelErrorHandler } from './usePrizeWheelErrorHandler';
import { useRealTimeBalance } from './useRealTimeBalance';
import { apiClient } from '../services/api';
import toast from 'react-hot-toast';
import type { EntryFeeProcessResult, EntryFeeRefundResult } from '../types/api';

export type EntryFeeStatus = 'not_required' | 'required' | 'pending' | 'paid' | 'failed' | 'refunded';

export interface EntryFeeState {
  status: EntryFeeStatus;
  amount: number;
  processing: boolean;
  error: string | null;
  transactionId: string | null;
  paidAt: Date | null;
  refundedAt: Date | null;
  lastChecked: Date | null;
}

export interface EntryFeeActions {
  processEntryFee: () => Promise<boolean>;
  processRefund: () => Promise<boolean>;
  checkPaymentStatus: () => Promise<void>;
  resetState: () => void;
}

export const useEntryFeeProcessing = (roomId: string, entryFeeAmount: number) => {
  const { user } = useAuthStore();
  const { getEntryFeeStatus, updateEntryFeeStatus } = useGameStore();
  const { handleEntryFeeError, handleApiErrorWithRetry } = usePrizeWheelErrorHandler();
  const { validateBalance, refreshBalance } = useRealTimeBalance().actions;

  const [state, setState] = useState<EntryFeeState>({
    status: 'not_required',
    amount: entryFeeAmount,
    processing: false,
    error: null,
    transactionId: null,
    paidAt: null,
    refundedAt: null,
    lastChecked: null,
  });

  // Process entry fee payment
  const processEntryFee = useCallback(async (): Promise<boolean> => {
    if (!user || state.processing) return false;

    // Validate balance first
    const hasValidBalance = await validateBalance(entryFeeAmount);
    if (!hasValidBalance) {
      setState(prev => ({ 
        ...prev, 
        status: 'failed',
        error: 'Insufficient balance for entry fee' 
      }));
      return false;
    }

    setState(prev => ({ 
      ...prev, 
      processing: true, 
      error: null,
      status: 'pending'
    }));
    
    updateEntryFeeStatus(user.id, 'pending');

    try {
      const result: EntryFeeProcessResult = await handleApiErrorWithRetry(
        () => apiClient.processEntryFee({
          user_id: user.id,
          room_id: roomId,
          bet_amount: entryFeeAmount,
          metadata: {
            room_name: roomId,
            game_type: 'prize_wheel',
            max_players: 8,
            timestamp: new Date().toISOString(),
          },
        }),
        2, // max retries
        2000, // retry delay
        { context: 'entry_fee_processing', showToast: false }
      );

      if (result.success) {
        setState(prev => ({
          ...prev,
          processing: false,
          status: 'paid',
          transactionId: result.transaction.id,
          paidAt: new Date(),
          error: null,
        }));

        updateEntryFeeStatus(user.id, 'paid');

        toast.success(`Entry fee paid: $${entryFeeAmount.toFixed(2)}`, {
          icon: '💰',
          duration: 4000,
        });

        // Refresh balance to reflect the change
        await refreshBalance();

        return true;
      } else {
        setState(prev => ({
          ...prev,
          processing: false,
          status: 'failed',
          error: result.error_message || 'Entry fee processing failed',
        }));

        updateEntryFeeStatus(user.id, 'failed');
        return false;
      }
    } catch (error) {
      const prizeWheelError = handleEntryFeeError(error, user.id, roomId, entryFeeAmount);
      
      setState(prev => ({
        ...prev,
        processing: false,
        status: 'failed',
        error: prizeWheelError.message,
      }));

      updateEntryFeeStatus(user.id, 'failed');
      return false;
    }
  }, [
    user, 
    roomId, 
    entryFeeAmount, 
    state.processing, 
    validateBalance, 
    updateEntryFeeStatus, 
    handleApiErrorWithRetry, 
    handleEntryFeeError,
    refreshBalance
  ]);

  // Process refund
  const processRefund = useCallback(async (): Promise<boolean> => {
    if (!user || state.processing || state.status !== 'paid') return false;

    setState(prev => ({ 
      ...prev, 
      processing: true, 
      error: null 
    }));

    try {
      const result: EntryFeeRefundResult = await handleApiErrorWithRetry(
        () => apiClient.processRefund({
          user_id: user.id,
          room_id: roomId,
          bet_amount: entryFeeAmount,
          metadata: {
            reason: 'player_unready',
            timestamp: new Date().toISOString(),
          },
        }),
        2,
        1500,
        { context: 'entry_fee_refund', showToast: false }
      );

      if (result.success) {
        setState(prev => ({
          ...prev,
          processing: false,
          status: 'refunded',
          transactionId: result.transaction.id,
          refundedAt: new Date(),
          error: null,
        }));

        updateEntryFeeStatus(user.id, 'pending'); // Reset to allow re-payment

        toast.success(`Entry fee refunded: $${result.refunded_amount.toFixed(2)}`, {
          icon: '💸',
          duration: 3000,
        });

        // Refresh balance to reflect the refund
        await refreshBalance();

        return true;
      } else {
        setState(prev => ({
          ...prev,
          processing: false,
          error: result.error_message || 'Refund processing failed',
        }));

        return false;
      }
    } catch (error) {
      const prizeWheelError = handleEntryFeeError(error, user.id, roomId, entryFeeAmount);
      
      setState(prev => ({
        ...prev,
        processing: false,
        error: prizeWheelError.message,
      }));

      return false;
    }
  }, [
    user, 
    roomId, 
    entryFeeAmount, 
    state.processing, 
    state.status,
    updateEntryFeeStatus, 
    handleApiErrorWithRetry, 
    handleEntryFeeError,
    refreshBalance
  ]);

  // Check payment status
  const checkPaymentStatus = useCallback(async () => {
    if (!user) return;

    try {
      const result = await handleApiErrorWithRetry(
        () => apiClient.checkPaymentStatus(user.id, roomId),
        2,
        1000,
        { context: 'payment_status_check', showToast: false }
      );

      const newStatus: EntryFeeStatus = result.has_paid_entry_fee ? 'paid' : 'required';
      
      setState(prev => ({
        ...prev,
        status: newStatus,
        lastChecked: new Date(result.checked_at),
        error: null,
      }));

      updateEntryFeeStatus(user.id, result.has_paid_entry_fee ? 'paid' : 'pending');
    } catch (error) {
      console.warn('Failed to check payment status:', error);
    }
  }, [user, roomId, handleApiErrorWithRetry, updateEntryFeeStatus]);

  // Reset state
  const resetState = useCallback(() => {
    setState({
      status: entryFeeAmount > 0 ? 'required' : 'not_required',
      amount: entryFeeAmount,
      processing: false,
      error: null,
      transactionId: null,
      paidAt: null,
      refundedAt: null,
      lastChecked: null,
    });
  }, [entryFeeAmount]);

  // Initialize and sync with game store
  useEffect(() => {
    if (!user) return;

    const storeStatus = getEntryFeeStatus(user.id);
    if (storeStatus) {
      setState(prev => ({
        ...prev,
        status: storeStatus === 'paid' ? 'paid' : 'required',
      }));
    } else if (entryFeeAmount > 0) {
      setState(prev => ({ ...prev, status: 'required' }));
      checkPaymentStatus();
    } else {
      setState(prev => ({ ...prev, status: 'not_required' }));
    }
  }, [user, entryFeeAmount, getEntryFeeStatus, checkPaymentStatus]);

  // Update amount when it changes
  useEffect(() => {
    setState(prev => ({ ...prev, amount: entryFeeAmount }));
  }, [entryFeeAmount]);

  // Calculate derived data
  const derivedData = {
    isRequired: entryFeeAmount > 0,
    isPaid: state.status === 'paid',
    isPending: state.status === 'pending',
    isFailed: state.status === 'failed',
    isRefunded: state.status === 'refunded',
    canPay: state.status === 'required' || state.status === 'failed',
    canRefund: state.status === 'paid' && !state.processing,
    formattedAmount: state.amount.toFixed(2),
    statusDisplay: state.status.replace('_', ' ').toUpperCase(),
  };

  const actions: EntryFeeActions = {
    processEntryFee,
    processRefund,
    checkPaymentStatus,
    resetState,
  };

  return {
    ...state,
    ...derivedData,
    actions,
  };
};

export default useEntryFeeProcessing;
