import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useGameStore } from '../store/gameStore';
import { useAuthStore } from '../store/authStore';
import { apiClient } from '../services/api';
import { usePrizeWheelErrorHandler } from './usePrizeWheelErrorHandler';
import type { PrizePool, PotentialWinningsResponse } from '../types/api';

export interface PrizePoolState {
  prizePool: PrizePool | null;
  potentialWinnings: PotentialWinningsResponse | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface PrizePoolActions {
  loadPrizePool: (roomId: string) => Promise<void>;
  loadPotentialWinnings: (roomId: string) => Promise<void>;
  refreshData: () => Promise<void>;
  clearData: () => void;
}

export const usePrizePoolData = (roomId?: string) => {
  const { user } = useAuthStore();
  const gameStore = useGameStore();
  const { handlePrizePoolError, handleApiErrorWithRetry } = usePrizeWheelErrorHandler();

  // Memoize game store functions to prevent unnecessary re-renders
  const memoizedGameStoreFunctions = useMemo(() => ({
    getPrizePoolData: gameStore.getPrizePoolData,
    updatePrizePoolData: gameStore.updatePrizePoolData,
    getPotentialWinnings: gameStore.getPotentialWinnings,
    updatePotentialWinnings: gameStore.updatePotentialWinnings,
  }), [gameStore.getPrizePoolData, gameStore.updatePrizePoolData, gameStore.getPotentialWinnings, gameStore.updatePotentialWinnings]);

  const [state, setState] = useState<PrizePoolState>({
    prizePool: null,
    potentialWinnings: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  // Debouncing refs to prevent rapid successive API calls
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastRoomIdRef = useRef<string | undefined>(roomId);

  // Load prize pool data with loading state check
  const loadPrizePool = useCallback(async (targetRoomId: string) => {
    // Prevent concurrent calls for the same room
    if (state.loading) {
      console.log('Prize pool already loading, skipping duplicate request');
      return;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const prizePool = await handleApiErrorWithRetry(
        () => apiClient.getRoomPrizePool(targetRoomId),
        3,
        1000,
        { context: 'load_prize_pool', showToast: false }
      );

      memoizedGameStoreFunctions.updatePrizePoolData(prizePool);
      setState(prev => ({
        ...prev,
        prizePool,
        loading: false,
        lastUpdated: new Date(),
      }));
    } catch (error) {
      const prizeWheelError = handlePrizePoolError(error, undefined, targetRoomId);
      setState(prev => ({
        ...prev,
        loading: false,
        error: prizeWheelError.message,
      }));
    }
  }, [handleApiErrorWithRetry, handlePrizePoolError, memoizedGameStoreFunctions, state.loading]);

  // Load potential winnings
  const loadPotentialWinnings = useCallback(async (targetRoomId: string) => {
    if (!user) return;

    try {
      const potentialWinnings = await handleApiErrorWithRetry(
        () => apiClient.getPotentialWinnings(targetRoomId),
        2,
        1500,
        { context: 'load_potential_winnings', showToast: false }
      );

      memoizedGameStoreFunctions.updatePotentialWinnings(potentialWinnings);
      setState(prev => ({
        ...prev,
        potentialWinnings,
        lastUpdated: new Date(),
      }));
    } catch (error) {
      // Don't show errors for potential winnings as it's not critical
      console.warn('Failed to load potential winnings:', error);
    }
  }, [user, handleApiErrorWithRetry, memoizedGameStoreFunctions]);

  // Refresh all data
  const refreshData = useCallback(async () => {
    if (!roomId) return;

    await Promise.all([
      loadPrizePool(roomId),
      loadPotentialWinnings(roomId),
    ]);
  }, [roomId, loadPrizePool, loadPotentialWinnings]);

  // Clear data
  const clearData = useCallback(() => {
    setState({
      prizePool: null,
      potentialWinnings: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  // Auto-load data when roomId changes with debouncing
  useEffect(() => {
    // Clear any existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // If roomId is the same as last time, don't reload
    if (roomId === lastRoomIdRef.current && roomId && state.prizePool) {
      return;
    }

    lastRoomIdRef.current = roomId;

    if (roomId) {
      // Debounce the API call to prevent rapid successive calls
      debounceTimeoutRef.current = setTimeout(() => {
        refreshData();
      }, 300); // 300ms debounce
    } else {
      clearData();
    }

    // Cleanup timeout on unmount
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [roomId, refreshData, clearData, state.prizePool]);

  // Sync with game store - removed prizePoolData dependency to prevent infinite loop
  useEffect(() => {
    const storePrizePool = memoizedGameStoreFunctions.getPrizePoolData();
    const storePotentialWinnings = memoizedGameStoreFunctions.getPotentialWinnings();

    // Only update if the data has actually changed
    setState(prev => {
      const hasChanges =
        (storePrizePool && storePrizePool !== prev.prizePool) ||
        (storePotentialWinnings && storePotentialWinnings !== prev.potentialWinnings);

      if (!hasChanges) return prev;

      return {
        ...prev,
        prizePool: storePrizePool || prev.prizePool,
        potentialWinnings: storePotentialWinnings || prev.potentialWinnings,
      };
    });
  }, []); // Empty dependency array - this will only run once on mount

  // Calculate derived data
  const derivedData = {
    totalPool: state.prizePool?.total_pool || 0,
    netPrize: state.prizePool?.net_prize_amount || 0,
    houseEdge: state.prizePool?.house_edge_percentage || 0,
    houseEdgeAmount: state.prizePool?.house_edge_amount || 0,
    entryFee: state.prizePool?.entry_fee_per_player || 0,
    maxPlayers: state.prizePool?.max_players || 0,
    currentPlayers: state.prizePool?.player_count || 0,
    contributingPlayers: state.prizePool?.contributing_players?.length || 0,
    isAccumulating: state.prizePool?.status === 'accumulating',
    isLocked: state.prizePool?.status === 'locked',
    isDistributed: state.prizePool?.status === 'distributed',
    isCancelled: state.prizePool?.status === 'cancelled',
    userPotentialWinnings: user ? state.potentialWinnings?.potential_winnings?.find(
      pw => pw.user_id === user.id
    ) : null,
  };

  const actions: PrizePoolActions = {
    loadPrizePool,
    loadPotentialWinnings,
    refreshData,
    clearData,
  };

  return {
    ...state,
    ...derivedData,
    actions,
  };
};

export default usePrizePoolData;
