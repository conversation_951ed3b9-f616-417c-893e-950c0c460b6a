import { useState, useEffect, useCallback } from 'react';
import { useGameStore } from '../store/gameStore';
import { useAuthStore } from '../store/authStore';
import { apiClient } from '../services/api';
import { usePrizeWheelErrorHandler } from './usePrizeWheelErrorHandler';
import type { PrizePool, PotentialWinningsResponse } from '../types/api';

export interface PrizePoolState {
  prizePool: PrizePool | null;
  potentialWinnings: PotentialWinningsResponse | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface PrizePoolActions {
  loadPrizePool: (roomId: string) => Promise<void>;
  loadPotentialWinnings: (roomId: string) => Promise<void>;
  refreshData: () => Promise<void>;
  clearData: () => void;
}

export const usePrizePoolData = (roomId?: string) => {
  const { user } = useAuthStore();
  const { 
    getPrizePoolData, 
    updatePrizePoolData, 
    getPotentialWinnings,
    updatePotentialWinnings,
    prizePoolData 
  } = useGameStore();
  const { handlePrizePoolError, handleApiErrorWithRetry } = usePrizeWheelErrorHandler();

  const [state, setState] = useState<PrizePoolState>({
    prizePool: null,
    potentialWinnings: null,
    loading: false,
    error: null,
    lastUpdated: null,
  });

  // Load prize pool data
  const loadPrizePool = useCallback(async (targetRoomId: string) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const prizePool = await handleApiErrorWithRetry(
        () => apiClient.getRoomPrizePool(targetRoomId),
        3,
        1000,
        { context: 'load_prize_pool', showToast: false }
      );

      updatePrizePoolData(prizePool);
      setState(prev => ({
        ...prev,
        prizePool,
        loading: false,
        lastUpdated: new Date(),
      }));
    } catch (error) {
      const prizeWheelError = handlePrizePoolError(error, undefined, targetRoomId);
      setState(prev => ({
        ...prev,
        loading: false,
        error: prizeWheelError.message,
      }));
    }
  }, [handleApiErrorWithRetry, handlePrizePoolError, updatePrizePoolData]);

  // Load potential winnings
  const loadPotentialWinnings = useCallback(async (targetRoomId: string) => {
    if (!user) return;

    try {
      const potentialWinnings = await handleApiErrorWithRetry(
        () => apiClient.getPotentialWinnings(targetRoomId),
        2,
        1500,
        { context: 'load_potential_winnings', showToast: false }
      );

      updatePotentialWinnings(potentialWinnings);
      setState(prev => ({
        ...prev,
        potentialWinnings,
        lastUpdated: new Date(),
      }));
    } catch (error) {
      // Don't show errors for potential winnings as it's not critical
      console.warn('Failed to load potential winnings:', error);
    }
  }, [user, handleApiErrorWithRetry, updatePotentialWinnings]);

  // Refresh all data
  const refreshData = useCallback(async () => {
    if (!roomId) return;

    await Promise.all([
      loadPrizePool(roomId),
      loadPotentialWinnings(roomId),
    ]);
  }, [roomId, loadPrizePool, loadPotentialWinnings]);

  // Clear data
  const clearData = useCallback(() => {
    setState({
      prizePool: null,
      potentialWinnings: null,
      loading: false,
      error: null,
      lastUpdated: null,
    });
  }, []);

  // Auto-load data when roomId changes
  useEffect(() => {
    if (roomId) {
      refreshData();
    } else {
      clearData();
    }
  }, [roomId, refreshData, clearData]);

  // Sync with game store
  useEffect(() => {
    const storePrizePool = getPrizePoolData();
    const storePotentialWinnings = getPotentialWinnings();

    setState(prev => ({
      ...prev,
      prizePool: storePrizePool || prev.prizePool,
      potentialWinnings: storePotentialWinnings || prev.potentialWinnings,
    }));
  }, [getPrizePoolData, getPotentialWinnings, prizePoolData]);

  // Calculate derived data
  const derivedData = {
    totalPool: state.prizePool?.total_pool || 0,
    netPrize: state.prizePool?.net_prize_amount || 0,
    houseEdge: state.prizePool?.house_edge_percentage || 0,
    houseEdgeAmount: state.prizePool?.house_edge_amount || 0,
    entryFee: state.prizePool?.entry_fee_per_player || 0,
    maxPlayers: state.prizePool?.max_players || 0,
    currentPlayers: state.prizePool?.player_count || 0,
    contributingPlayers: state.prizePool?.contributing_players?.length || 0,
    isAccumulating: state.prizePool?.status === 'accumulating',
    isLocked: state.prizePool?.status === 'locked',
    isDistributed: state.prizePool?.status === 'distributed',
    isCancelled: state.prizePool?.status === 'cancelled',
    userPotentialWinnings: user ? state.potentialWinnings?.potential_winnings?.find(
      pw => pw.user_id === user.id
    ) : null,
  };

  const actions: PrizePoolActions = {
    loadPrizePool,
    loadPotentialWinnings,
    refreshData,
    clearData,
  };

  return {
    ...state,
    ...derivedData,
    actions,
  };
};

export default usePrizePoolData;
