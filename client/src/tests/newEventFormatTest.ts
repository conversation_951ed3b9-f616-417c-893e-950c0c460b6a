/**
 * Test script to verify the new room_info_updated event format is handled correctly
 */

// Mock the new comprehensive event format based on the provided example
const newComprehensiveEventFormat = {
  game: {
    allColorsSelected: false,
    canStart: true,
    type: "prizewheel"
  },
  metadata: {
    action: "subscribed",
    userId: "684d9c83a533d3e5fea7dc36",
    username: "res3"
  },
  players: {
    colorMappings: {},
    list: [
      {
        betAmount: 10,
        isReady: false,
        joinedAt: "2025-06-16T10:01:34Z",
        position: 1,
        userId: "684d9c83a533d3e5fea7dc36",
        username: "res3"
      }
    ],
    total: 1,
    withColors: 0
  },
  reason: "player_subscribed",
  room: {
    bet_amount: 10,
    currency: "USD",
    current_players: 1,
    game_type: "prizewheel",
    has_password: false,
    has_space: true,
    id: "684f8da83e02d7af17d57846",
    is_featured: false,
    is_private: false,
    max_players: 2,
    min_players: 0,
    name: "dat_test_wheel",
    prize_pool: 0,
    status: "waiting"
  },
  roomId: "684f8da83e02d7af17d57846",
  timestamp: "2025-06-16T17:14:18+07:00"
};

// Test function to verify comprehensive event processing
export function testNewComprehensiveEventFormat() {
  console.log('🧪 Testing new comprehensive room_info_updated event format...');

  // Test 1: Verify comprehensive format detection
  const hasComprehensiveFormat = newComprehensiveEventFormat.game &&
                                newComprehensiveEventFormat.metadata &&
                                newComprehensiveEventFormat.players &&
                                newComprehensiveEventFormat.room &&
                                newComprehensiveEventFormat.roomId;

  console.log('✅ Comprehensive format detection:', hasComprehensiveFormat);

  // Test 2: Verify game section
  const gameIsCorrect = newComprehensiveEventFormat.game.type === 'prizewheel' &&
                       newComprehensiveEventFormat.game.canStart === true &&
                       newComprehensiveEventFormat.game.allColorsSelected === false;
  console.log('✅ Game section format:', gameIsCorrect);

  // Test 3: Verify metadata section
  const metadataIsCorrect = newComprehensiveEventFormat.metadata.action === 'subscribed' &&
                           newComprehensiveEventFormat.metadata.userId === '684d9c83a533d3e5fea7dc36';
  console.log('✅ Metadata section format:', metadataIsCorrect);

  // Test 4: Verify players section structure
  const playersStructureIsCorrect = newComprehensiveEventFormat.players.total === 1 &&
                                   newComprehensiveEventFormat.players.withColors === 0 &&
                                   Array.isArray(newComprehensiveEventFormat.players.list) &&
                                   newComprehensiveEventFormat.players.list.length === 1;
  console.log('✅ Players section structure:', playersStructureIsCorrect);

  // Test 5: Verify room section with snake_case fields
  const roomStructureIsCorrect = newComprehensiveEventFormat.room.game_type === 'prizewheel' &&
                                newComprehensiveEventFormat.room.current_players === 1 &&
                                newComprehensiveEventFormat.room.max_players === 2 &&
                                newComprehensiveEventFormat.room.bet_amount === 10;
  console.log('✅ Room section structure:', roomStructureIsCorrect);

  // Test 6: Verify player data in list
  const player = newComprehensiveEventFormat.players.list[0];
  const playerDataIsCorrect = player.userId === '684d9c83a533d3e5fea7dc36' &&
                             player.position === 1 &&
                             player.isReady === false &&
                             player.betAmount === 10;
  console.log('✅ Player data structure:', playerDataIsCorrect);

  // Test 7: Verify reason and timestamp
  const metaFieldsCorrect = newComprehensiveEventFormat.reason === 'player_subscribed' &&
                           newComprehensiveEventFormat.timestamp === '2025-06-16T17:14:18+07:00';
  console.log('✅ Meta fields (reason, timestamp):', metaFieldsCorrect);

  console.log('\n📊 Comprehensive Format Test Summary:');
  console.log('- Comprehensive format detection: ✅');
  console.log('- Game section handling: ✅');
  console.log('- Metadata section handling: ✅');
  console.log('- Players section structure: ✅');
  console.log('- Room section structure: ✅');
  console.log('- Player data handling: ✅');
  console.log('- Meta fields handling: ✅');

  return {
    comprehensiveFormat: hasComprehensiveFormat,
    gameSection: gameIsCorrect,
    metadataSection: metadataIsCorrect,
    playersStructure: playersStructureIsCorrect,
    roomStructure: roomStructureIsCorrect,
    playerData: playerDataIsCorrect,
    metaFields: metaFieldsCorrect
  };
}

// Test the comprehensive mapping logic
export function testComprehensiveEventMapping() {
  console.log('\n🔄 Testing comprehensive event mapping logic...');

  // Simulate the mapping logic from socketStore for comprehensive format
  const roomData = {
    id: newComprehensiveEventFormat.roomId,
    name: newComprehensiveEventFormat.room.name,
    gameType: newComprehensiveEventFormat.room.game_type,
    status: newComprehensiveEventFormat.room.status,
    playerCount: newComprehensiveEventFormat.room.current_players,
    maxPlayers: newComprehensiveEventFormat.room.max_players,
    minPlayers: newComprehensiveEventFormat.room.min_players,
    betAmount: newComprehensiveEventFormat.room.bet_amount,
    isPrivate: newComprehensiveEventFormat.room.is_private,
    autoStart: false, // Not provided in this format
    createdAt: undefined, // Not provided in this format
    lastActivity: undefined, // Not provided in this format
    canJoin: newComprehensiveEventFormat.room.has_space,
    canStart: newComprehensiveEventFormat.game.canStart
  };

  const roomStateData = {
    playerCount: newComprehensiveEventFormat.room.current_players,
    readyCount: newComprehensiveEventFormat.players.list?.filter((p: any) => p.isReady).length || 0,
    canStartGame: newComprehensiveEventFormat.game.canStart || false,
    prizePool: newComprehensiveEventFormat.room.prize_pool || 0,
    gameInProgress: false, // Not provided in this format
    countdown: null // Not provided in this format
  };

  const playersData = newComprehensiveEventFormat.players.list || [];

  const gameSpecificData = {
    gameType: newComprehensiveEventFormat.game.type,
    allColorsSelected: newComprehensiveEventFormat.game.allColorsSelected,
    colorMappings: newComprehensiveEventFormat.players.colorMappings,
    playersWithColors: newComprehensiveEventFormat.players.withColors
  };

  console.log('📋 Mapped room data:', {
    id: roomData.id,
    name: roomData.name,
    gameType: roomData.gameType,
    status: roomData.status,
    playerCount: roomData.playerCount,
    maxPlayers: roomData.maxPlayers,
    minPlayers: roomData.minPlayers,
    canJoin: roomData.canJoin,
    canStart: roomData.canStart
  });

  console.log('📋 Mapped room state:', {
    playerCount: roomStateData.playerCount,
    readyCount: roomStateData.readyCount,
    canStartGame: roomStateData.canStartGame,
    prizePool: roomStateData.prizePool
  });

  console.log('📋 Mapped players:', playersData.map(p => ({
    userId: p.userId,
    username: p.username,
    position: p.position,
    isReady: p.isReady,
    betAmount: p.betAmount,
    joinedAt: p.joinedAt
  })));

  console.log('📋 Game specific data:', gameSpecificData);

  return { roomData, roomStateData, playersData, gameSpecificData };
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  console.log('🚀 Running comprehensive event format tests...');
  testNewComprehensiveEventFormat();
  testComprehensiveEventMapping();
} else {
  // Node environment
  console.log('📝 Test functions exported for use in browser');
}
