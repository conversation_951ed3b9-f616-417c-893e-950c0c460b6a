@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }

  * {
    @apply border-gray-200;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-error {
    @apply btn bg-error-600 text-white hover:bg-error-700 focus:ring-error-500;
  }

  .btn-outline {
    @apply btn border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Input Components */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }

  .input-error {
    @apply border-error-300 focus:ring-error-500 focus:border-error-500;
  }

  .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .error-text {
    @apply mt-1 text-sm text-error-600;
  }

  /* Card Components */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Status Indicators */
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .status-online {
    @apply status-indicator bg-success-100 text-success-800;
  }

  .status-offline {
    @apply status-indicator bg-gray-100 text-gray-800;
  }

  .status-waiting {
    @apply status-indicator bg-warning-100 text-warning-800;
  }

  .status-playing {
    @apply status-indicator bg-primary-100 text-primary-800;
  }

  .status-finished {
    @apply status-indicator bg-gray-100 text-gray-800;
  }

  /* Loading Spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-200 border-t-primary-600;
  }

  .spinner-sm {
    @apply w-4 h-4;
  }

  .spinner-md {
    @apply w-6 h-6;
  }

  .spinner-lg {
    @apply w-8 h-8;
  }

  /* Notification Styles */
  .notification {
    @apply p-4 rounded-lg shadow-lg border-l-4;
  }

  .notification-info {
    @apply notification bg-blue-50 border-blue-400 text-blue-800;
  }

  .notification-success {
    @apply notification bg-success-50 border-success-400 text-success-800;
  }

  .notification-warning {
    @apply notification bg-warning-50 border-warning-400 text-warning-800;
  }

  .notification-error {
    @apply notification bg-error-50 border-error-400 text-error-800;
  }

  /* Game Room Styles */
  .room-card {
    @apply card hover:shadow-md transition-shadow cursor-pointer;
  }

  .room-card:hover {
    @apply border-primary-200;
  }

  .player-avatar {
    @apply w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-sm font-medium text-gray-600;
  }

  .player-ready {
    @apply text-success-600;
  }

  .player-not-ready {
    @apply text-gray-400;
  }

  /* Connection Status */
  .connection-indicator {
    @apply flex items-center space-x-2 text-sm;
  }

  .connection-dot {
    @apply w-2 h-2 rounded-full;
  }

  .connection-connected .connection-dot {
    @apply bg-success-500;
  }

  .connection-connecting .connection-dot {
    @apply bg-warning-500 animate-pulse;
  }

  .connection-disconnected .connection-dot {
    @apply bg-error-500;
  }

  .connection-reconnecting .connection-dot {
    @apply bg-warning-500 animate-pulse;
  }
}

@layer utilities {
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }

  /* Scrollbar styles */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }
}
