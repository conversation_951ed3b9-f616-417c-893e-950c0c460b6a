import React from 'react';
import { User, Mail, Calendar, MapPin, Trophy, TrendingUp } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';

const ProfilePage: React.FC = () => {
  const { user } = useAuthStore();

  if (!user) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900">Profile not found</h2>
        <p className="mt-2 text-gray-600">Unable to load user profile.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <div className="card">
        <div className="card-body">
          <div className="flex items-center space-x-6">
            <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
              {user.profile?.avatar ? (
                <img
                  src={user.profile.avatar}
                  alt={user.username}
                  className="w-24 h-24 rounded-full"
                />
              ) : (
                <User className="w-12 h-12 text-gray-400" />
              )}
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {user.profile?.firstName && user.profile?.lastName
                  ? `${user.profile.firstName} ${user.profile.lastName}`
                  : user.username}
              </h1>
              <p className="text-gray-600">@{user.username}</p>
              <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                <div className="flex items-center">
                  <Mail className="w-4 h-4 mr-1" />
                  {user.email}
                </div>
                {user.profile?.country && (
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {user.profile.country}
                  </div>
                )}
                {user.createdAt && (
                  <div className="flex items-center">
                    <Calendar className="w-4 h-4 mr-1" />
                    Joined {new Date(user.createdAt).toLocaleDateString()}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Balance Card */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Account Balance</h3>
        </div>
        <div className="card-body">
          <div className="text-center">
            <div className="text-4xl font-bold text-primary-600">
              ${user.balance}
            </div>
            <p className="text-gray-600 mt-2">{user.currency || 'USD'}</p>
            <div className="mt-4 space-x-2">
              <button className="btn-primary">Deposit</button>
              <button className="btn-outline">Withdraw</button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="card">
          <div className="card-body text-center">
            <Trophy className="w-8 h-8 text-yellow-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">0</div>
            <div className="text-sm text-gray-600">Games Won</div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body text-center">
            <TrendingUp className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">0</div>
            <div className="text-sm text-gray-600">Total Winnings</div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body text-center">
            <User className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">0</div>
            <div className="text-sm text-gray-600">Games Played</div>
          </div>
        </div>
        
        <div className="card">
          <div className="card-body text-center">
            <Calendar className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-gray-900">0%</div>
            <div className="text-sm text-gray-600">Win Rate</div>
          </div>
        </div>
      </div>

      {/* Profile Settings */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-medium text-gray-900">Profile Settings</h3>
        </div>
        <div className="card-body">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="label">First Name</label>
                <input
                  type="text"
                  value={user.profile?.firstName || ''}
                  className="input"
                  placeholder="Enter first name"
                />
              </div>
              <div>
                <label className="label">Last Name</label>
                <input
                  type="text"
                  value={user.profile?.lastName || ''}
                  className="input"
                  placeholder="Enter last name"
                />
              </div>
            </div>
            
            <div>
              <label className="label">Email</label>
              <input
                type="email"
                value={user.email}
                className="input"
                disabled
              />
            </div>
            
            <div>
              <label className="label">Country</label>
              <input
                type="text"
                value={user.profile?.country || ''}
                className="input"
                placeholder="Enter country"
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <button className="btn-outline">Cancel</button>
              <button className="btn-primary">Save Changes</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
