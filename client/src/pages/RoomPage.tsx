import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Settings, Play, DollarSign } from 'lucide-react';
import { useGameStore } from '@/store/gameStore';
import { useSocketStore } from '@/store/socketStore';
import { useAuthStore } from '@/store/authStore';
import { useRealTimeGameData } from '@/hooks/useRealTimeGameData';
import { useRoomSubscription } from '@/hooks/useRoomSubscription';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import LeaveRoomButton from '@/components/UI/LeaveRoomButton';
import RoomCapacity from '@/components/UI/RoomCapacity';
import PlayerList from '@/components/Game/PlayerList';
import PlayerSlots from '@/components/Game/PlayerSlots';
import GameArea from '@/components/Game/GameArea';
import { PrizeWheelReadySection } from '@/components/Game/PrizeWheelReadySection';
import { AmidakujiReadySection } from '@/components/Game/AmidakujiReadySection';
import RoomStateDebug from '@/components/Debug/RoomStateDebug';
import RoomPlayerDebugPanel from '@/components/Debug/RoomPlayerDebugPanel';
import toast from 'react-hot-toast';

const RoomPage: React.FC = () => {
  const { roomId } = useParams<{ roomId: string }>();
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { currentRoom, loadRoomDetails } = useGameStore();
  const {
    currentRoom: socketRoom,
    currentGame,
    leaveRoom: socketLeaveRoom,
    isLeavingRoom,
    waitingForRoomInfo
  } = useSocketStore();

  const [isLoading, setIsLoading] = React.useState(true);

  // Initialize real-time data handling
  useRealTimeGameData();

  // Use room subscription hook to get waiting state
  const { waitingForRoomInfo: hookWaitingForRoomInfo } = useRoomSubscription();

  // Use socket room data if available, otherwise fall back to API data
  const room = socketRoom || currentRoom;

  // Debug logging for room state updates (moved to top to avoid hooks order issues)
  React.useEffect(() => {
    if (room && process.env.NODE_ENV === 'development') {
      console.log('🏠 Room state in RoomPage:', {
        roomId: room.id,
        playerCount: room.playerCount,
        playersLength: room.players?.length,
        players: room.players?.map(p => ({
          userId: p.userId,
          username: p.username,
          position: p.position,
          isReady: p.isReady,
        })),
        source: socketRoom ? 'socket' : 'api',
        // Additional debug info
        hasSocketRoom: !!socketRoom,
        hasCurrentRoom: !!currentRoom,
        socketRoomId: socketRoom?.id,
        currentRoomId: currentRoom?.id,
        socketRoomPlayerCount: socketRoom?.playerCount,
        socketRoomPlayersLength: socketRoom?.players?.length,
        currentRoomPlayerCount: currentRoom?.playerCount,
        currentRoomPlayersLength: currentRoom?.players?.length,
      });
    }
  }, [room?.players, room?.playerCount, socketRoom, currentRoom]);

  React.useEffect(() => {
    if (roomId) {
      // If we have socket room data, use it and skip API call
      if (socketRoom && socketRoom.id === roomId) {
        setIsLoading(false);
        return;
      }

      // If socketRoom is null, it means user is not in any room
      // Don't fetch room details for a room the user is not in
      if (socketRoom === null) {
        console.log('User is not in any room, redirecting to rooms list');
        navigate('/rooms');
        return;
      }

      // Only load from API if we don't have socket data but user might be in a room
      // This should rarely happen in normal flow
      console.log('Loading room details from API as fallback');
      loadRoomDetails(roomId)
        .then(() => setIsLoading(false))
        .catch(() => {
          toast.error('Failed to load room details');
          navigate('/rooms');
        });
    }
  }, [roomId, loadRoomDetails, navigate, socketRoom]);

  const handleLeaveRoom = async (roomIdToLeave: string) => {
    if (!roomIdToLeave) return;

    try {
      // Only use socket leave room - it will handle all the cleanup
      await socketLeaveRoom(roomIdToLeave, 'voluntary');

      // Navigate back to rooms list
      navigate('/rooms');

      toast.success('Successfully left room!', {
        duration: 3000,
        icon: '👋',
      });
    } catch (error: any) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to leave room';
      const errorCode = error?.code;

      console.error('Leave room error:', {
        roomId: roomIdToLeave,
        error,
        errorMessage,
        errorCode
      });

      // Enhanced error handling with specific icons and messages
      switch (errorCode) {
        case 'PLAYER_NOT_IN_ROOM':
          toast.error('You are not currently in this room', {
            duration: 3000,
            icon: '👤',
          });
          break;
        case 'ROOM_NOT_FOUND':
          toast.error('Room no longer exists', {
            duration: 3000,
            icon: '🏠',
          });
          break;
        case 'LEAVE_ROOM_TIMEOUT':
          toast.error('Request timed out. Please try again.', {
            duration: 4000,
            icon: '⏱️',
          });
          break;
        case 'UNAUTHORIZED':
          toast.error('You are not authorized to leave this room', {
            duration: 4000,
            icon: '🔐',
          });
          break;
        case 'LEAVE_ROOM_FAILED':
          toast.error('Unable to leave room. Please try again.', {
            duration: 4000,
            icon: '⚠️',
          });
          break;
        default:
          toast.error(errorMessage, {
            duration: 4000,
            icon: '❌',
          });
          break;
      }
    }
  };



  // Show loading if we're still loading room details OR waiting for room info after joining
  if (isLoading || waitingForRoomInfo || hookWaitingForRoomInfo) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-gray-600">
            {waitingForRoomInfo || hookWaitingForRoomInfo
              ? 'Loading room information...'
              : 'Loading room details...'}
          </p>
        </div>
      </div>
    );
  }





  if (!room) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900">Room not found</h2>
        <p className="mt-2 text-gray-600">The room you're looking for doesn't exist.</p>
        <button
          onClick={() => navigate('/rooms')}
          className="mt-4 btn-primary"
        >
          Back to Rooms
        </button>
      </div>
    );
  }

  const isHost = room.players?.some(p => p.userId === user?.id && p.isHost) || false;
  const canStartGame = socketRoom?.canStartGame || false;

  return (
    <div className="space-y-4">
      {/* Debug Components */}
      <RoomStateDebug roomId={room.id} />
      <RoomPlayerDebugPanel className="mb-4" />

      {/* Compact Room Header */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-4">
            <div>
              <h1 className="text-xl font-bold text-gray-900">{room.name}</h1>
              <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                <span className="flex items-center gap-1">
                  <span className="w-2 h-2 rounded-full bg-purple-500"></span>
                  {room.gameType}
                </span>
                <RoomCapacity
                  currentPlayers={room.playerCount || (room as any).currentPlayerCount || 0}
                  maxPlayers={room.maxPlayers || 0}
                  size="sm"
                  showIcon={true}
                  showBadge={false}
                  showPercentage={false}
                />
                <span className="flex items-center gap-1">
                  <DollarSign className="w-3 h-3" />
                  ${room.betAmount || (currentRoom as any)?.config?.betAmount || (currentRoom as any)?.betAmount || 0}
                </span>
                {socketRoom && (
                  <span className="flex items-center gap-1">
                    <div className={`w-2 h-2 rounded-full ${
                      (room.status === 'WAITING' || room.status === 'ROOM_STATUS_WAITING') ? 'bg-yellow-500' :
                      (room.status === 'PLAYING' || room.status === 'ROOM_STATUS_PLAYING') ? 'bg-green-500' : 'bg-gray-500'
                    }`}></div>
                    {socketRoom.readyCount}/{socketRoom.playerCount} ready
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Game Start Button for hosts */}
            {(room.status === 'WAITING' || room.status === 'ROOM_STATUS_WAITING') &&
             (['PRIZEWHEEL', 'AMIDAKUJI', 'GAME_TYPE_PRIZE_WHEEL', 'GAME_TYPE_AMIDAKUJI'].includes(room.gameType)) &&
             isHost && canStartGame && (
              <button className="btn-primary">
                <Play className="w-4 h-4 mr-2" />
                Start Game
              </button>
            )}

            {isHost && (
              <button className="btn-outline">
                <Settings className="w-4 h-4" />
              </button>
            )}

            <LeaveRoomButton
              roomId={room.id}
              roomName={room.name}
              isLeaving={isLeavingRoom(room.id)}
              onLeave={handleLeaveRoom}
              size="sm"
              variant="danger"
              showConfirmation={true}
            />
          </div>
        </div>
      </div>

      {/* Debug Info for Game Type and Status */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-yellow-50 border border-yellow-200 rounded p-3 text-sm">
          <div><strong>Game Type:</strong> "{room.gameType}" (uppercase: "{room.gameType?.toUpperCase()}")</div>
          <div><strong>Status:</strong> "{room.status}" (uppercase: "{room.status?.toUpperCase()}")</div>
          <div><strong>Max Players:</strong> {room.maxPlayers}</div>
          <div><strong>Current Players:</strong> {room.players?.length || 0}</div>
          <div><strong>Should Show Prize Wheel:</strong> {(room.gameType?.toUpperCase() === 'PRIZEWHEEL' || room.gameType?.toUpperCase() === 'GAME_TYPE_PRIZE_WHEEL') &&
       (room.status?.toUpperCase() === 'WAITING' || room.status?.toUpperCase() === 'ROOM_STATUS_WAITING') ? 'YES' : 'NO'}</div>
        </div>
      )}

      {/* Main Game Layout */}
      {(room.gameType?.toUpperCase() === 'PRIZEWHEEL' || room.gameType?.toUpperCase() === 'GAME_TYPE_PRIZE_WHEEL') &&
       (room.status?.toUpperCase() === 'WAITING' || room.status?.toUpperCase() === 'ROOM_STATUS_WAITING') ? (
        <div className="space-y-4">
          {/* Player Slots - Full Width */}
          <PlayerSlots
            maxPlayers={room.maxPlayers}
            currentUserId={user?.id}
            players={room.players?.map(p => ({
              userId: (p as any).userId || (p as any).id || '',
              username: p.username,
              avatar: (p as any).avatar,
              position: p.position,
              isReady: p.isReady,
              isHost: (p as any).isHost,
              balance: (p as any).balance,
              insufficientBalance: (p as any).insufficientBalance,
              colorId: (p as any).colorId,
              colorName: (p as any).colorName,
              colorHex: (p as any).colorHex,
            })) || []}
          />

          {/* Prize Wheel Game Area */}
          <PrizeWheelReadySection
            roomId={room.id}
            currentUserId={user?.id || ''}
          />
        </div>
      ) : (room.gameType?.toUpperCase() === 'AMIDAKUJI' || room.gameType?.toUpperCase() === 'GAME_TYPE_AMIDAKUJI') &&
          (room.status?.toUpperCase() === 'WAITING' || room.status?.toUpperCase() === 'ROOM_STATUS_WAITING') ? (
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-4">
          {/* Players List - Compact */}
          <div className="xl:col-span-1">
            <PlayerList
              players={room.players?.map(p => ({
                id: (p as any).userId || (p as any).id || '',
                ...p
              })) || []}
              currentUserId={user?.id}
              showBalance={true}
              showColorIndicator={false}
            />
          </div>

          {/* Amidakuji Game Area */}
          <div className="xl:col-span-3">
            <AmidakujiReadySection
              roomId={room.id}
              currentUserId={user?.id || ''}
            />
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          {/* Players List */}
          <div className="lg:col-span-1">
            <PlayerList
              players={room.players?.map(p => ({
                id: (p as any).userId || (p as any).id || '',
                ...p
              })) || []}
              currentUserId={user?.id}
              showBalance={true}
              showColorIndicator={false}
            />
          </div>

          {/* Default Game Area */}
          <div className="lg:col-span-2">
            <GameArea
              room={room}
              currentGame={currentGame}
              gameType={room.gameType}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default RoomPage;
