/**
 * Test Page for Room List Events
 * Provides a dedicated page to test room_list_updated event handling
 */

import React from 'react';
import { RoomListEventTest } from '@/components/Test/RoomListEventTest';

const TestRoomEventsPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            Room List Event Testing
          </h1>
          <p className="text-gray-600">
            Test and monitor room_list_updated events, including the new 'subscribe' action type
          </p>
        </div>
        
        <RoomListEventTest />
        
        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">Testing Instructions</h2>
          <div className="space-y-3 text-sm text-gray-700">
            <div className="flex items-start space-x-2">
              <span className="font-semibold text-blue-600">1.</span>
              <span>Click "Subscribe to Lobby" to start receiving room list events</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-semibold text-blue-600">2.</span>
              <span>Monitor the event logs for incoming room_list_updated events</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-semibold text-blue-600">3.</span>
              <span>Look for events with action "subscribe" that contain room data</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-semibold text-blue-600">4.</span>
              <span>Use "Start Direct Listening" to see raw socket events (for debugging)</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="font-semibold text-blue-600">5.</span>
              <span>Check the browser console for detailed logging information</span>
            </div>
          </div>
          
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <div className="font-semibold text-yellow-800">Expected Event Structure:</div>
            <pre className="mt-2 text-xs text-yellow-700 overflow-x-auto">
{`{
  "action": "subscribe",
  "count": 4,
  "rooms": [...],
  "socketId": "...",
  "userId": "...",
  "username": "...",
  "source": "manager-service",
  "timestamp": "..."
}`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestRoomEventsPage;
