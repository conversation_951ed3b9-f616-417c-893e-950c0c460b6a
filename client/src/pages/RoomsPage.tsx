import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Users, Search, Filter, RefreshCw } from 'lucide-react';
import { useGameStore } from '@/store/gameStore';
import { useLobbyStore, useRoomListUpdates } from '@/store/lobbyStore';
import { useSocketStore } from '@/store/socketStore';
import { config } from '@/config/env';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import ConnectionStatus from '@/components/UI/ConnectionStatus';
import RoomCard from '@/components/Game/RoomCard';
import { logLobbySubscriptionState, createLobbySubscriptionGuard } from '@/utils/lobbySubscriptionDebug';
import { roomSubscriptionFlowManager } from '@/utils/roomSubscriptionFlow';
import toast from 'react-hot-toast';

const RoomsPage: React.FC = () => {
  const navigate = useNavigate();
  const {
    rooms,
    roomsLoading,
    roomsError,
    filters,
    setFilters,
    clearError,
    handleRoomListUpdate,
  } = useGameStore();
  const { joinRoom } = useSocketStore();
  const {
    isSubscribed,
    subscriptionError,
    subscribeLobby,
    unsubscribeLobby,
    clearError: clearLobbyError,
  } = useLobbyStore();

  const [searchTerm, setSearchTerm] = React.useState('');
  const [showFilters, setShowFilters] = React.useState(false);
  const [recentlyUpdatedRooms, setRecentlyUpdatedRooms] = React.useState<Set<string>>(new Set());
  const [joiningRooms, setJoiningRooms] = React.useState<Set<string>>(new Set());
  const [lastJoinAttempt, setLastJoinAttempt] = React.useState<{ roomId: string; timestamp: number } | null>(null);

  // Subscribe to lobby updates only (no HTTP calls to /rooms)
  React.useEffect(() => {
    const guard = createLobbySubscriptionGuard('RoomsPage-Init');

    const initializeLobby = async () => {
      logLobbySubscriptionState('RoomsPage-Init-Before');

      // Only subscribe if not already subscribed
      if (guard.canSubscribe()) {
        try {
          console.log('RoomsPage: Initializing lobby subscription...');
          await subscribeLobby();
          logLobbySubscriptionState('RoomsPage-Init-Success');
          toast.success('Connected to real-time updates');
        } catch (error) {
          console.error('RoomsPage: Failed to initialize lobby:', error);
          logLobbySubscriptionState('RoomsPage-Init-Error');
          toast.error('Failed to connect to real-time updates');
        }
      } else {
        console.log('RoomsPage: Subscription blocked by guard, skipping initialization');
        logLobbySubscriptionState('RoomsPage-Init-Blocked');
      }
    };

    initializeLobby();

    // Cleanup: unsubscribe when leaving the page
    return () => {
      console.log('RoomsPage: Cleaning up lobby subscription...');
      logLobbySubscriptionState('RoomsPage-Cleanup');
      unsubscribeLobby().catch(console.error);
    };
  }, []); // Remove dependencies to prevent re-running

  // Handle room list updates from socket
  useRoomListUpdates(React.useCallback((data) => {
    handleRoomListUpdate(data);

    // Mark room as recently updated (only for single room updates, not initial_load)
    if (data.action !== 'initial_load' && 'room' in data && data.room?.id) {
      setRecentlyUpdatedRooms(prev => new Set(prev).add(data.room.id));

      // Remove the highlight after 3 seconds
      setTimeout(() => {
        setRecentlyUpdatedRooms(prev => {
          const newSet = new Set(prev);
          newSet.delete(data.room.id);
          return newSet;
        });
      }, 3000);
    }

    // Show toast notifications for room updates (only for real updates, not initial load)
    if (data.action !== 'initial_load' && 'room' in data && data.room) {
      switch (data.action) {
        case 'created':
          toast.success(`New room "${data.room.name}" created!`);
          break;
        case 'deleted':
          toast.success(`Room "${data.room.name}" was removed`);
          break;
        case 'player_count_changed':
          // Show subtle notification for player count changes
          toast(`Room "${data.room.name}": ${data.room.playerCount}/${data.room.maxPlayers} players`, {
            duration: 2000,
            icon: '👥',
          });
          break;
        // Don't show toasts for regular updates to avoid spam
      }
    }
  }, [handleRoomListUpdate]));

  // Handle errors
  React.useEffect(() => {
    if (roomsError) {
      toast.error(roomsError);
      clearError();
    }
  }, [roomsError, clearError]);

  React.useEffect(() => {
    if (subscriptionError) {
      toast.error(`Lobby subscription error: ${subscriptionError}`);
      clearLobbyError();
    }
  }, [subscriptionError, clearLobbyError]);

  const handleRefresh = async () => {
    try {
      console.log('RoomsPage: Refreshing lobby subscription...');
      // Only unsubscribe if currently subscribed
      if (isSubscribed) {
        await unsubscribeLobby();
      }
      // Re-subscribe to get fresh data
      await subscribeLobby();
      toast.success('Rooms refreshed via socket');
    } catch (error) {
      console.error('Failed to refresh rooms:', error);
      toast.error('Failed to refresh rooms');
    }
  };

  const handleJoinRoom = async (roomId: string, password?: string) => {
    // Prevent rapid duplicate join attempts (debounce)
    const now = Date.now();
    if (lastJoinAttempt &&
        lastJoinAttempt.roomId === roomId &&
        now - lastJoinAttempt.timestamp < 2000) { // 2 second debounce
      console.warn('RoomsPage: Ignoring rapid duplicate join attempt for room:', roomId);
      return;
    }

    // Check if already joining this room
    if (joiningRooms.has(roomId)) {
      console.warn('RoomsPage: Already joining room, ignoring duplicate request:', roomId);
      return;
    }

    // Update last join attempt
    setLastJoinAttempt({ roomId, timestamp: now });

    // Add room to joining state
    setJoiningRooms(prev => new Set(prev).add(roomId));

    // Starting room join flow

    try {
      // Execute comprehensive room join flow with subscription management
      await roomSubscriptionFlowManager.executeJoinRoomFlow({
        roomId,
        password,
        joinFunction: async () => {
          await joinRoom(roomId, password);
        },
        onSuccess: () => {
          // Navigate to room on successful join
          navigate(`/rooms/${roomId}`);
          toast.success('Successfully joined room!', {
            duration: 3000,
            icon: '🎮',
          });
        },
        onFailure: (error) => {
          // Error handling is done in the flow manager
          // Just log the error here for component-level tracking
          console.error('RoomsPage: Join room flow failed:', error);
        }
      });

      console.log('RoomsPage: Join room flow completed successfully');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to join room';
      const errorCode = (error as any)?.code;

      // Enhanced error handling with specific icons and durations
      switch (errorCode) {
        case 'INSUFFICIENT_BALANCE':
          toast.error('Insufficient balance to join this room', {
            duration: 4000,
            icon: '💰',
          });
          break;
        case 'ROOM_FULL':
          toast.error('Room is full. Try another room!', {
            duration: 3000,
            icon: '🚫',
          });
          break;
        case 'INVALID_ROOM_PASSWORD':
          toast.error('Invalid room password', {
            duration: 3000,
            icon: '🔒',
          });
          break;
        case 'PLAYER_ALREADY_IN_ROOM':
          toast.error('You are already in this room', {
            duration: 3000,
            icon: '👤',
          });
          break;
        case 'ROOM_NOT_FOUND':
          toast.error('Room no longer available', {
            duration: 3000,
            icon: '❌',
          });
          break;
        case 'AUTHENTICATION_FAILED':
          toast.error('Authentication failed. Please log in again.', {
            duration: 5000,
            icon: '🔐',
          });
          break;
        case 'JOIN_ROOM_FAILED':
          toast.error('Unable to join room. Please try again.', {
            duration: 4000,
            icon: '⚠️',
          });
          break;
        default:
          // Handle timeout and other generic errors
          if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
            toast.error('Join request timed out. Please try again.', {
              duration: 4000,
              icon: '⏱️',
            });
          } else if (errorMessage.includes('multiple attempts')) {
            toast.error('Failed to join after multiple attempts. Please check your connection.', {
              duration: 5000,
              icon: '🔄',
            });
          } else {
            toast.error(errorMessage, {
              duration: 4000,
              icon: '❌',
            });
          }
          break;
      }

      console.error('Room join error:', {
        roomId,
        errorCode,
        errorMessage,
        error,
        currentlySubscribed: isSubscribed,
      });
    } finally {
      // Remove room from joining state
      setJoiningRooms(prev => {
        const newSet = new Set(prev);
        newSet.delete(roomId);
        return newSet;
      });
    }
  };

  const handleFilterChange = (key: string, value: any) => {
    setFilters({ [key]: value });
    // Filters are applied client-side to socket data - no HTTP call needed
  };

  const filteredRooms = rooms.filter(room =>
    room.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    room.gameType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <div className="flex items-center space-x-3">
            <h1 className="text-2xl font-bold text-gray-900">Game Rooms</h1>
            {/* Enhanced connection status indicator */}
            <ConnectionStatus size="md" />
          </div>
          <p className="text-gray-600">
            {isSubscribed
              ? 'Real-time updates enabled - rooms update automatically'
              : 'Join a room to start playing'
            }
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefresh}
            disabled={roomsLoading}
            className="btn-outline"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${roomsLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="card">
        <div className="card-body">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search rooms..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input pl-10"
                />
              </div>
            </div>

            {/* Filter toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="btn-outline"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </button>
          </div>

          {/* Filters */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="label">Game Type</label>
                  <select
                    value={filters.gameType || ''}
                    onChange={(e) => handleFilterChange('gameType', e.target.value || undefined)}
                    className="input"
                  >
                    <option value="">All Types</option>
                    {config.game.gameTypes.map(type => (
                      <option key={type} value={type}>{type}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="label">Status</label>
                  <select
                    value={filters.status || ''}
                    onChange={(e) => handleFilterChange('status', e.target.value || undefined)}
                    className="input"
                  >
                    <option value="">All Status</option>
                    <option value="WAITING">Waiting</option>
                    <option value="STARTING">Starting</option>
                    <option value="PLAYING">Playing</option>
                  </select>
                </div>

                <div>
                  <label className="label">Min Bet</label>
                  <input
                    type="number"
                    placeholder="0"
                    value={filters.minBet || ''}
                    onChange={(e) => handleFilterChange('minBet', e.target.value ? Number(e.target.value) : undefined)}
                    className="input"
                  />
                </div>

                <div>
                  <label className="label">Max Bet</label>
                  <input
                    type="number"
                    placeholder="1000"
                    value={filters.maxBet || ''}
                    onChange={(e) => handleFilterChange('maxBet', e.target.value ? Number(e.target.value) : undefined)}
                    className="input"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Rooms Grid */}
      {roomsLoading ? (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" />
        </div>
      ) : filteredRooms.length === 0 ? (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No rooms found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || Object.keys(filters).length > 0
              ? 'Try adjusting your search or filters'
              : 'No rooms are currently available'}
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRooms.map((room) => (
            <RoomCard
              key={room.id}
              room={room}
              onJoin={handleJoinRoom}
              isRecentlyUpdated={recentlyUpdatedRooms.has(room.id)}
              isJoining={joiningRooms.has(room.id)}
            />
          ))}
        </div>
      )}

      {/* Note: Pagination removed - all rooms come via socket subscription */}


    </div>
  );
};

export default RoomsPage;
