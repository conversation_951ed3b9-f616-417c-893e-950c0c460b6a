// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    status_code: number;
    timestamp: string;
    request_id: string;
  };
  meta?: {
    timestamp: string;
    request_id: string;
  };
}

// User Types
export interface User {
  id: string;
  username: string;
  email: string;
  profile: {
    firstName?: string;
    lastName?: string;
    avatar?: string;
    dateOfBirth?: string;
    country?: string;
  };
  balance: number;
  currency?: string;
  roles?: string[];
  permissions?: string[];
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  lastLoginAt?: string;
  statistics?: {
    gamesPlayed: number;
    gamesWon: number;
    totalWinnings: number;
    totalLosses: number;
  };
}

// Authentication Types
export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  country: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Room Types
export interface Room {
  id: string;
  name: string;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI';
  status: 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED';
  creatorId?: string;
  // Flat structure to match backend response
  maxPlayers: number;
  currentPlayerCount: number;
  currentPlayers?: number; // Alternative field name for compatibility
  playerCount?: number; // Alternative field name for compatibility
  betAmount: number;
  currency: string;
  gameDuration?: number;
  isPrivate: boolean;
  password?: string;
  players: Player[];
  createdAt: string;
  updatedAt: string;
  prizePool?: number; // Total prize pool for the room
  // Optional nested config for backward compatibility
  config?: {
    maxPlayers: number;
    betAmount: number;
    currency: string;
    gameDuration: number;
    gameSpecificConfig: Record<string, any>;
  };
}

export interface Player {
  id: string;
  userId: string;
  username: string;
  avatar?: string;
  position: number;
  isReady: boolean;
  betAmount: number;
  joinedAt: string;
  isHost?: boolean;
}

export interface CreateRoomRequest {
  name: string;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI';
  betAmount: number;
  currency: string;
  maxPlayers: number;
  isPrivate: boolean;
  password?: string;
}

export interface JoinRoomRequest {
  roomId: string;
  password?: string;
}

export interface GetRoomsRequest {
  page?: number;
  limit?: number;
  gameType?: string;
  status?: string;
  minBet?: number;
  maxBet?: number;
}

export interface RoomsResponse {
  rooms: Room[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Game Types
export interface Game {
  id: string;
  roomId: string;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI';
  status: 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED';
  players: Player[];
  config: Record<string, any>;
  result?: GameResult;
  startedAt?: string;
  finishedAt?: string;
  duration?: number;
}

export interface GameResult {
  winnerId: string;
  winnerUsername: string;
  winAmount: number;
  gameData: Record<string, any>;
  timestamp: string;
}

// Transaction Types
export interface Transaction {
  id: string;
  user_id: string;
  userId?: string; // For backward compatibility
  type: 'ready_fee' | 'refund' | 'prize_win' | 'deposit' | 'withdrawal' | 'DEPOSIT' | 'WITHDRAWAL' | 'BET' | 'WIN' | 'REFUND';
  amount: number;
  currency?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  description: string;
  metadata: {
    room_id?: string;
    session_id?: string;
    game_type?: string;
    service?: string;
    processed_at?: string;
    [key: string]: any;
  };
  gameId?: string; // For backward compatibility
  roomId?: string; // For backward compatibility
  created_at: string;
  updated_at: string;
  createdAt?: string; // For backward compatibility
  updatedAt?: string; // For backward compatibility
}

// Error Types
export interface ApiError {
  code: string;
  message: string;
  status_code: number;
  timestamp: string;
  request_id: string;
}

// Pagination Types
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginationResponse {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Prize Pool Types
export interface PrizePool {
  id: string;
  room_id: string;
  total_pool: number;
  entry_fee_per_player: number;
  contributing_players: string[];
  house_edge_percentage: number;
  house_edge_amount: number;
  net_prize_amount: number;
  status: 'accumulating' | 'locked' | 'distributed' | 'cancelled';
  game_type: 'prize_wheel';
  room_name: string;
  max_players: number;
  player_count: number;
  is_full: boolean;
  locked_at?: string;
  distributed_at?: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface PrizePoolsResponse {
  prize_pools: PrizePool[];
  pagination: PaginationResponse;
}

export interface PotentialWinnings {
  user_id: string;
  potential_winnings: number;
  win_probability: number;
  current_pool_share: number;
}

export interface PotentialWinningsResponse {
  potential_winnings: PotentialWinnings[];
}

// Entry Fee Types
export interface EntryFeeValidationRequest {
  user_id: string;
  bet_amount: number;
}

export interface EntryFeeValidationResult {
  success: boolean;
  has_sufficient_balance: boolean;
  current_balance: number;
  required_amount: number;
  shortfall?: number;
  error_code?: string;
  error_message?: string;
}

export interface EntryFeeProcessRequest {
  user_id: string;
  room_id: string;
  bet_amount: number;
  metadata?: {
    room_name?: string;
    game_type?: string;
    max_players?: number;
    timestamp?: string;
  };
}

export interface EntryFeeProcessResult {
  success: boolean;
  transaction: Transaction;
  prize_pool: {
    room_id: string;
    total_pool: number;
    contributing_players: string[];
    player_count: number;
  };
  current_balance: number;
  error_code?: string;
  error_message?: string;
}

export interface EntryFeeRefundRequest {
  user_id: string;
  room_id: string;
  bet_amount: number;
  metadata?: {
    reason?: string;
    timestamp?: string;
  };
}

export interface EntryFeeRefundResult {
  success: boolean;
  transaction: Transaction;
  refunded_amount: number;
  current_balance: number;
  error_code?: string;
  error_message?: string;
}

export interface PaymentStatusResult {
  success: boolean;
  user_id: string;
  room_id: string;
  has_paid_entry_fee: boolean;
  checked_at: string;
}

// Prize Distribution Types
export interface GameResultParticipant {
  user_id: string;
  username: string;
  selected_color: string;
  bet_amount: number;
  is_winner: boolean;
}

export interface GameResults {
  game_session_id: string;
  winning_color: string;
  final_angle: number;
  spin_duration: number;
  participants: GameResultParticipant[];
  timestamp: string;
}

export interface PrizeDistributionRequest {
  game_results: GameResults;
}

export interface PrizeDistributionWinner {
  user_id: string;
  username: string;
  prize_amount: number;
  new_balance: number;
  transaction_id: string;
}

export interface PrizeDistributionSummary {
  total_pool: number;
  net_prize_amount: number;
  house_edge_amount: number;
  winner_count: number;
  participant_count: number;
}

export interface PrizeDistributionResult {
  distribution_id: string;
  prize_pool_id: string;
  total_distributed: number;
  house_edge_collected: number;
  winners: PrizeDistributionWinner[];
  distribution_summary: PrizeDistributionSummary;
  distributed_at: string;
}

// Enhanced Room Types
export interface EnhancedRoomDetails {
  room: {
    id: string;
    name: string;
    game_type: string;
    status: string;
    current_players: number;
    max_players: number;
    min_players: number;
    bet_amount: number;
    prize_pool: number;
    currency: string;
    is_private: boolean;
    creator: {
      id: string;
      username: string;
    };
    configuration: {
      game_duration: number;
      spin_duration: number;
      result_display_duration: number;
      wheel_sections: number;
      colors: string[];
    };
    players: EnhancedPlayer[];
    game_sessions: GameSession[];
    created_at: string;
    updated_at: string;
  };
}

export interface EnhancedPlayer {
  user_id: string;
  username: string;
  position: number;
  is_ready: boolean;
  bet_amount: number;
  selected_color?: string;
  session_count: number;
  status: 'pending' | 'active' | 'completed' | 'cancelled';
  joined_at: string;
}

export interface GameSession {
  id: string;
  session_id: string;
  status: string;
  bet_amount: number;
  win_amount: number;
  started_at: string;
  ended_at: string;
}

export interface KickPlayerRequest {
  user_id: string;
  reason: string;
  notify_player: boolean;
}

export interface KickPlayerResult {
  kicked_user_id: string;
  reason: string;
  kicked_at: string;
  kicked_by: string;
}
