// Environment configuration
export const env = {
  // API Configuration
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  API_VERSION: import.meta.env.VITE_API_VERSION || 'v1',

  // Socket Configuration
  SOCKET_URL: import.meta.env.VITE_SOCKET_URL || 'http://localhost:3001',
  SOCKET_PATH: import.meta.env.VITE_SOCKET_PATH || '/socket.io/',

  // App Configuration
  APP_NAME: import.meta.env.VITE_APP_NAME || 'XZ Game',
  APP_VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
  NODE_ENV: import.meta.env.NODE_ENV || 'development',

  // Feature Flags
  ENABLE_CHAT: import.meta.env.VITE_ENABLE_CHAT === 'true',
  ENABLE_NOTIFICATIONS: import.meta.env.VITE_ENABLE_NOTIFICATIONS !== 'false',
  ENABLE_SOUND: import.meta.env.VITE_ENABLE_SOUND !== 'false',
  ENABLE_ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',

  // Development
  DEBUG: import.meta.env.VITE_DEBUG === 'true',
  MOCK_API: import.meta.env.VITE_MOCK_API === 'true',
} as const;

// Derived configuration
export const config = {
  // API URLs
  api: {
    baseURL: env.API_BASE_URL,  // Connection to API Gateway
    auth: {
      login: '/api/v1/auth/login',
      register: '/api/v1/auth/register',
      logout: '/api/v1/auth/logout',
      refresh: '/api/v1/auth/refresh',
    },
    users: {
      profile: '/api/v1/users/profile',
      balance: '/api/v1/users/balance',
      transactions: '/api/v1/users/transactions',
    },
    rooms: {
      list: '/api/v1/rooms',
      create: '/api/v1/rooms',
      details: (id: string) => `/api/v1/rooms/${id}`,
      join: (id: string) => `/api/v1/rooms/${id}/join`,
      leave: (id: string) => `/api/v1/rooms/${id}/leave`,
    },
    games: {
      history: '/api/v1/games/history',
      details: (id: string) => `/api/v1/games/${id}`,
    },
  },

  // Socket configuration
  socket: {
    url: env.SOCKET_URL,
    options: {
      path: env.SOCKET_PATH,
      transports: ['websocket', 'polling'] as any,
      upgrade: true,
      rememberUpgrade: true,
      timeout: 20000,
      forceNew: false,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      maxReconnectionAttempts: 5,
      randomizationFactor: 0.5,
    },
  },

  // Local storage keys
  storage: {
    accessToken: 'xzgame_access_token',
    refreshToken: 'xzgame_refresh_token',
    user: 'xzgame_user',
    preferences: 'xzgame_preferences',
    gameSettings: 'xzgame_game_settings',
  },

  // UI Configuration
  ui: {
    defaultPageSize: 20,
    maxPageSize: 100,
    debounceDelay: 300,
    toastDuration: 5000,
    animationDuration: 300,
  },

  // Game Configuration
  game: {
    maxRoomNameLength: 50,
    minBetAmount: 1,
    maxBetAmount: 10000,
    defaultBetAmount: 100,
    maxPlayersPerRoom: 8,
    minPlayersPerRoom: 2,
    gameTypes: ['PRIZEWHEEL', 'AMIDAKUJI'] as const,
    currencies: ['USD', 'EUR', 'GBP'] as const,
  },

  // Validation
  validation: {
    username: {
      minLength: 3,
      maxLength: 30,
      pattern: /^[a-zA-Z0-9_-]+$/,
    },
    password: {
      minLength: 8,
      maxLength: 128,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: false,
    },
    email: {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    },
  },

  // Timeouts and intervals
  timeouts: {
    apiRequest: 30000,
    socketConnect: 10000,
    gameAction: 8000,  // Reduced from 15000 to 8000 for faster UX
    wheelColorSelection: 12000,  // Specific timeout for wheel color selection
    heartbeat: 30000,
  },

  // Error retry configuration
  retry: {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
  },
} as const;

// Type exports
export type GameType = typeof config.game.gameTypes[number];
export type Currency = typeof config.game.currencies[number];

// Validation helpers
export const isDevelopment = env.NODE_ENV === 'development';
export const isProduction = env.NODE_ENV === 'production';
export const isDebugMode = env.DEBUG && isDevelopment;
