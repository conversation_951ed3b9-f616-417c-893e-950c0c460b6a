/**
 * Test Suite for Room List Event Handling
 * Tests the enhanced room_list_updated event handling, especially the 'subscribe' action
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import type { RoomListUpdateData } from '../types/socket';

// Mock socket service
const mockSocketService = {
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  isConnected: true,
};

// Mock lobby store
const mockLobbyStore = {
  handleRoomListUpdate: vi.fn(),
  subscribeLobby: vi.fn(),
  unsubscribeLobby: vi.fn(),
};

describe('Room List Event Handling', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('RoomListUpdateData Type Validation', () => {
    it('should accept subscribe action with proper structure', () => {
      const subscribeEvent: RoomListUpdateData = {
        action: 'subscribe',
        count: 4,
        rooms: [
          {
            id: '68412c9af494b684c1c18ecf',
            name: 'czxcwee2',
            gameType: 'prizewheel',
            status: 'waiting',
            playerCount: 1,
            maxPlayers: 2,
            betAmount: 10,
            currency: 'USD',
            isPrivate: false,
            createdAt: '2025-06-05T05:35:22.488Z',
            updatedAt: '2025-06-11T18:22:12.456Z',
          }
        ],
        socketId: 'dxp2GGgVJrLgtRUHAAAB',
        userId: '683b07882d7dbd11e92bf29d',
        username: 'res2',
        source: 'manager-service',
        timestamp: '2025-06-12T14:59:07.790Z',
      };

      expect(subscribeEvent.action).toBe('subscribe');
      expect(subscribeEvent.count).toBe(4);
      expect(subscribeEvent.rooms).toHaveLength(1);
      expect(subscribeEvent.userId).toBe('683b07882d7dbd11e92bf29d');
      expect(subscribeEvent.source).toBe('manager-service');
    });

    it('should accept subscribe action with snake_case fields (real server format)', () => {
      const serverSubscribeEvent = {
        action: 'subscribe' as const,
        count: 4,
        rooms: [
          {
            id: '68412c9af494b684c1c18ecf',
            name: 'czxcwee2',
            game_type: 'prizewheel',
            status: 'waiting',
            current_players: 1,
            max_players: 2,
            min_players: 2,
            bet_amount: 10,
            currency: 'USD',
            prize_pool: 0,
            is_private: false,
            has_password: false,
            has_space: true,
            is_featured: false,
            creator_id: '68333053f035ba37b20bf3a4',
            created_at: '2025-06-05T05:35:22.488Z',
            updated_at: '2025-06-11T18:22:12.456Z',
          }
        ],
        socketId: 'sP9VW05z2XQ2TSaDAAAB',
        userId: '683b07882d7dbd11e92bf29d',
        username: 'res2',
        source: 'manager-service',
        timestamp: '2025-06-12T15:15:16.831Z',
      };

      expect(serverSubscribeEvent.action).toBe('subscribe');
      expect(serverSubscribeEvent.count).toBe(4);
      expect(serverSubscribeEvent.rooms).toHaveLength(1);
      expect(serverSubscribeEvent.rooms[0].game_type).toBe('prizewheel');
      expect(serverSubscribeEvent.rooms[0].current_players).toBe(1);
      expect(serverSubscribeEvent.rooms[0].max_players).toBe(2);
      expect(serverSubscribeEvent.rooms[0].bet_amount).toBe(10);
    });

    it('should accept unsubscribe action', () => {
      const unsubscribeEvent: RoomListUpdateData = {
        action: 'unsubscribe',
        count: 0,
        rooms: [],
        socketId: 'dxp2GGgVJrLgtRUHAAAB',
        userId: '683b07882d7dbd11e92bf29d',
        username: 'res2',
        source: 'manager-service',
        timestamp: '2025-06-12T15:00:00.000Z',
      };

      expect(unsubscribeEvent.action).toBe('unsubscribe');
      expect(unsubscribeEvent.count).toBe(0);
      expect(unsubscribeEvent.rooms).toHaveLength(0);
    });

    it('should maintain backward compatibility with existing actions', () => {
      const legacyEvent: RoomListUpdateData = {
        action: 'initial_load',
        rooms: [],
        timestamp: '2025-06-12T15:00:00.000Z',
      };

      expect(legacyEvent.action).toBe('initial_load');
      expect(legacyEvent.rooms).toEqual([]);
    });
  });

  describe('Event Processing Logic', () => {
    it('should handle subscribe events with room data', () => {
      const testEvent: RoomListUpdateData = {
        action: 'subscribe',
        count: 2,
        rooms: [
          {
            id: 'room1',
            name: 'Test Room 1',
            gameType: 'prizewheel',
            status: 'waiting',
            playerCount: 1,
            maxPlayers: 4,
            betAmount: 10,
            currency: 'USD',
            isPrivate: false,
            createdAt: '2025-06-12T15:00:00.000Z',
          },
          {
            id: 'room2',
            name: 'Test Room 2',
            gameType: 'amidakuji',
            status: 'waiting',
            playerCount: 0,
            maxPlayers: 3,
            betAmount: 20,
            currency: 'USD',
            isPrivate: false,
            createdAt: '2025-06-12T15:00:00.000Z',
          }
        ],
        userId: 'test-user',
        username: 'testuser',
        source: 'manager-service',
        timestamp: '2025-06-12T15:00:00.000Z',
      };

      // Simulate event processing
      const processedRooms = testEvent.rooms.map(room => ({
        id: room.id,
        name: room.name,
        gameType: room.gameType,
        playerCount: room.playerCount,
        maxPlayers: room.maxPlayers,
        status: room.status,
        betAmount: room.betAmount,
      }));

      expect(processedRooms).toHaveLength(2);
      expect(processedRooms[0].gameType).toBe('prizewheel');
      expect(processedRooms[1].gameType).toBe('amidakuji');
    });

    it('should extract room information correctly from different field formats', () => {
      // Test with snake_case fields (as received from server)
      const serverEvent = {
        action: 'subscribe' as const,
        count: 1,
        rooms: [{
          id: 'room1',
          name: 'Test Room',
          game_type: 'prizewheel',
          status: 'waiting',
          current_players: 1,
          max_players: 4,
          bet_amount: 10,
          currency: 'USD',
          is_private: false,
          created_at: '2025-06-12T15:00:00.000Z',
        }],
        userId: 'test-user',
        username: 'testuser',
        source: 'manager-service',
        timestamp: '2025-06-12T15:00:00.000Z',
      };

      const room = serverEvent.rooms[0];
      const normalizedRoom = {
        id: room.id,
        name: room.name,
        gameType: (room as any).game_type || (room as any).gameType,
        playerCount: (room as any).current_players || (room as any).playerCount,
        maxPlayers: (room as any).max_players || (room as any).maxPlayers,
        betAmount: (room as any).bet_amount || (room as any).betAmount,
        status: room.status,
      };

      expect(normalizedRoom.gameType).toBe('prizewheel');
      expect(normalizedRoom.playerCount).toBe(1);
      expect(normalizedRoom.maxPlayers).toBe(4);
      expect(normalizedRoom.betAmount).toBe(10);
    });
  });

  describe('Error Handling', () => {
    it('should handle subscribe events with errors', () => {
      const errorEvent: RoomListUpdateData = {
        action: 'subscribe',
        count: 0,
        rooms: [],
        error: 'Failed to load rooms',
        timestamp: '2025-06-12T15:00:00.000Z',
      };

      expect(errorEvent.error).toBe('Failed to load rooms');
      expect(errorEvent.rooms).toHaveLength(0);
    });

    it('should handle malformed room data gracefully', () => {
      const malformedEvent = {
        action: 'subscribe' as const,
        count: 1,
        rooms: [
          {
            id: 'room1',
            // Missing required fields
          }
        ],
        timestamp: '2025-06-12T15:00:00.000Z',
      };

      const room = malformedEvent.rooms[0];
      const safeRoom = {
        id: room.id || 'unknown',
        name: (room as any).name || 'Unknown Room',
        gameType: (room as any).gameType || (room as any).game_type || 'prizewheel',
        playerCount: (room as any).playerCount || (room as any).current_players || 0,
        maxPlayers: (room as any).maxPlayers || (room as any).max_players || 4,
        betAmount: (room as any).betAmount || (room as any).bet_amount || 0,
        status: (room as any).status || 'waiting',
      };

      expect(safeRoom.id).toBe('room1');
      expect(safeRoom.name).toBe('Unknown Room');
      expect(safeRoom.gameType).toBe('prizewheel');
      expect(safeRoom.playerCount).toBe(0);
    });
  });

  describe('Integration Tests', () => {
    it('should simulate complete event flow', () => {
      const events: RoomListUpdateData[] = [
        {
          action: 'subscribe',
          count: 2,
          rooms: [
            {
              id: 'room1',
              name: 'Room 1',
              gameType: 'prizewheel',
              status: 'waiting',
              playerCount: 0,
              maxPlayers: 4,
              betAmount: 10,
              currency: 'USD',
              isPrivate: false,
              createdAt: '2025-06-12T15:00:00.000Z',
            }
          ],
          userId: 'user1',
          username: 'testuser',
          timestamp: '2025-06-12T15:00:00.000Z',
        },
        {
          action: 'player_count_changed',
          room: {
            id: 'room1',
            name: 'Room 1',
            gameType: 'prizewheel',
            status: 'waiting',
            playerCount: 1,
            maxPlayers: 4,
            betAmount: 10,
            currency: 'USD',
            isPrivate: false,
            createdAt: '2025-06-12T15:00:00.000Z',
          },
          timestamp: '2025-06-12T15:01:00.000Z',
        }
      ];

      // Simulate processing each event
      events.forEach(event => {
        if (event.action === 'subscribe') {
          expect(event.rooms).toBeDefined();
          expect(event.count).toBeGreaterThanOrEqual(0);
        } else if (event.action === 'player_count_changed') {
          expect(event.room).toBeDefined();
          expect(event.room.playerCount).toBe(1);
        }
      });

      expect(events).toHaveLength(2);
    });
  });
});
