/**
 * Test script to verify color_selection_updated event handling
 */

import { socketService } from '../services/socket';

// Mock the socket data from your example
const mockColorSelectionUpdatedData = {
  action: "color_selected",
  colorState: {
    assignments: {
      "684d9c83a533d3e5fea7dc36": {
        color: {
          hex: "#FFC0CB",
          id: "pink",
          name: "Pink"
        },
        selectedAt: "2025-06-17T17:34:45+07:00",
        userId: "684d9c83a533d3e5fea7dc36",
        username: "res3"
      }
    },
    availableColors: [
      { hex: "#FF0000", id: "red", name: "Red" },
      { hex: "#0000FF", id: "blue", name: "Blue" },
      { hex: "#00FF00", id: "green", name: "Green" },
      { hex: "#FFFF00", id: "yellow", name: "Yellow" },
      { hex: "#800080", id: "purple", name: "<PERSON>" },
      { hex: "#FFA500", id: "orange", name: "Orange" },
      { hex: "#008080", id: "teal", name: "<PERSON><PERSON>" }
    ],
    currentPlayerColor: {
      hex: "#FFC0CB",
      id: "pink",
      name: "Pink"
    },
    selectedColors: {
      pink: {
        selectedAt: "2025-06-17T17:34:45+07:00",
        userId: "684d9c83a533d3e5fea7dc36",
        username: "res3"
      }
    },
    statistics: {
      availableCount: 7,
      selectionRate: 12.5,
      takenCount: 1,
      totalColors: 8
    },
    taken: [
      {
        hex: "#FFC0CB",
        id: "pink",
        name: "Pink"
      }
    ]
  },
  event: "color_selection_updated",
  metadata: {
    gameType: "prizewheel",
    reason: "player_color_selection",
    triggeredBy: "684d9c83a533d3e5fea7dc36"
  },
  player: {
    color: {
      hex: "#FFC0CB",
      id: "pink",
      name: "Pink"
    },
    selectedAt: "2025-06-17T17:34:45+07:00",
    userId: "684d9c83a533d3e5fea7dc36",
    username: "res3"
  },
  roomId: "684f8da83e02d7af17d57846",
  timestamp: "2025-06-17T17:34:45+07:00"
};

// Test function to verify the event handling
export function testColorSelectionUpdated() {
  console.log('🧪 Testing color_selection_updated event handling...');

  // Set up event listener
  socketService.on('color_selection_updated', (data) => {
    console.log('✅ Received color_selection_updated event:', {
      action: data.action,
      player: data.player.username,
      color: data.player.color.name,
      roomId: data.roomId,
      availableCount: data.colorState.statistics.availableCount,
      selectionRate: data.colorState.statistics.selectionRate
    });

    // Check if room state was updated
    const currentRoom = socketService.currentRoom;
    if (currentRoom?.colorState) {
      console.log('✅ Room color state updated:', {
        playerColors: currentRoom.colorState.playerColors,
        availableColors: currentRoom.colorState.availableColors.length,
        takenColors: Object.keys(currentRoom.colorState.takenColors).length
      });

      // Check extended color state
      const extendedState = (currentRoom as any).extendedColorState;
      if (extendedState) {
        console.log('✅ Extended color state stored:', {
          assignments: Object.keys(extendedState.assignments).length,
          statistics: extendedState.statistics,
          currentPlayerColor: extendedState.currentPlayerColor
        });
      }
    }
  });

  // Simulate room state for testing
  (socketService as any).roomState = {
    id: "684f8da83e02d7af17d57846",
    name: "Test Room",
    gameType: "PRIZEWHEEL",
    status: "WAITING",
    players: [],
    playerCount: 1,
    maxPlayers: 8,
    readyCount: 0,
    canStartGame: false,
    colorState: {
      playerColors: {},
      availableColors: [],
      takenColors: {}
    }
  };

  // Emit the test event
  (socketService as any).emit('color_selection_updated', mockColorSelectionUpdatedData);

  // Test the updateRoomColorState method directly
  (socketService as any).updateRoomColorState('color_selection_updated', mockColorSelectionUpdatedData);

  console.log('🧪 Test completed!');
  console.log('🎯 UI Components created:');
  console.log('  - ColorSelectionStats: Real-time statistics display');
  console.log('  - PlayerColorAssignments: Detailed player assignments');
  console.log('  - ColorSelectionActivity: Live activity feed');
  console.log('  - Enhanced useEnhancedColorSelection hook');
}

// Test UI components with mock data
export function testUIComponents() {
  console.log('🎨 Testing UI Components with rich data...');

  const mockStatistics = {
    availableCount: 7,
    selectionRate: 12.5,
    takenCount: 1,
    totalColors: 8
  };

  const mockAssignments = {
    "684d9c83a533d3e5fea7dc36": {
      color: { hex: "#FFC0CB", id: "pink", name: "Pink" },
      selectedAt: "2025-06-17T17:34:45+07:00",
      userId: "684d9c83a533d3e5fea7dc36",
      username: "res3"
    }
  };

  console.log('✅ Mock data prepared for UI components:');
  console.log('  - Statistics:', mockStatistics);
  console.log('  - Assignments:', Object.keys(mockAssignments).length, 'players');
  console.log('  - Components ready for integration');
}

// Export for use in other tests
export { mockColorSelectionUpdatedData };
