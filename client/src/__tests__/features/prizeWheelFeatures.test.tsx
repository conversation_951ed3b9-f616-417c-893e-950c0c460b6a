/**
 * Comprehensive tests for Prize Wheel enhanced features
 * Tests prize pool functionality, entry fee processing, balance validation, and socket events
 */

import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';

// Components
import { PrizePoolDisplay } from '../../components/Game/PrizePoolDisplay';
import { BalanceAndEntryFee } from '../../components/Game/BalanceAndEntryFee';
import { TransactionHistory } from '../../components/Game/TransactionHistory';
import { WinnerAnnouncement } from '../../components/Game/WinnerAnnouncement';
import { PrizeWheelReadySection } from '../../components/Game/PrizeWheelReadySection';

// Hooks
import { usePrizePoolData } from '../../hooks/usePrizePoolData';
import { useRealTimeBalance } from '../../hooks/useRealTimeBalance';
import { useEntryFeeProcessing } from '../../hooks/useEntryFeeProcessing';
import { usePrizeWheelIntegration } from '../../hooks/usePrizeWheelIntegration';
import { usePrizeWheelErrorHandler } from '../../hooks/usePrizeWheelErrorHandler';

// Services
import { apiClient } from '../../services/api';
import { socketManager } from '../../services/socket';

// Test utilities
import { createMockPrizePool, createMockTransaction, createMockUser } from '../utils/factories';
import { TestWrapper } from '../utils/TestWrapper';

// Mock dependencies
vi.mock('../../services/api');
vi.mock('../../services/socket');
vi.mock('react-hot-toast');

describe('Prize Pool Functionality', () => {
  const mockPrizePool = createMockPrizePool();

  beforeEach(() => {
    vi.clearAllMocks();
    apiClient.getRoomPrizePool = vi.fn().mockResolvedValue(mockPrizePool);
  });

  describe('PrizePoolDisplay Component', () => {
    it('should display prize pool information correctly', async () => {
      render(
        <TestWrapper>
          <PrizePoolDisplay roomId="test-room" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Prize Pool')).toBeInTheDocument();
        expect(screen.getByText(`$${mockPrizePool.total_pool.toFixed(2)}`)).toBeInTheDocument();
        expect(screen.getByText(`${mockPrizePool.player_count}/${mockPrizePool.max_players} players`)).toBeInTheDocument();
      });
    });

    it('should show potential winnings when enabled', async () => {
      render(
        <TestWrapper>
          <PrizePoolDisplay roomId="test-room" showPotentialWinnings={true} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Your Potential Winnings')).toBeInTheDocument();
        expect(screen.getByText(`$${mockPrizePool.net_prize_amount.toFixed(2)}`)).toBeInTheDocument();
      });
    });

    it('should handle loading state', () => {
      apiClient.getRoomPrizePool = vi.fn().mockImplementation(() => new Promise(() => {}));

      render(
        <TestWrapper>
          <PrizePoolDisplay roomId="test-room" />
        </TestWrapper>
      );

      expect(screen.getByText('Loading prize pool...')).toBeInTheDocument();
    });

    it('should handle error state', async () => {
      apiClient.getRoomPrizePool = vi.fn().mockRejectedValue(new Error('Failed to load'));

      render(
        <TestWrapper>
          <PrizePoolDisplay roomId="test-room" />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/Failed to load prize pool/)).toBeInTheDocument();
      });
    });
  });

  describe('usePrizePoolData Hook', () => {
    it('should load prize pool data on mount', async () => {
      const { result } = renderHook(() => usePrizePoolData('test-room'), {
        wrapper: TestWrapper,
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
        expect(result.current.prizePool).toEqual(mockPrizePool);
      });

      expect(apiClient.getRoomPrizePool).toHaveBeenCalledWith('test-room');
    });

    it('should provide derived data correctly', async () => {
      const { result } = renderHook(() => usePrizePoolData('test-room'), {
        wrapper: TestWrapper,
      });

      await waitFor(() => {
        expect(result.current.totalPool).toBe(mockPrizePool.total_pool);
        expect(result.current.netPrize).toBe(mockPrizePool.net_prize_amount);
        expect(result.current.houseEdge).toBe(mockPrizePool.house_edge_percentage);
        expect(result.current.playerCount).toBe(mockPrizePool.player_count);
      });
    });

    it('should refresh data when called', async () => {
      const { result } = renderHook(() => usePrizePoolData('test-room'), {
        wrapper: TestWrapper,
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.actions.refreshData();
      });

      expect(apiClient.getRoomPrizePool).toHaveBeenCalledTimes(2);
    });
  });
});

describe('Entry Fee Processing', () => {
  const mockEntryFeeRequest = {
    user_id: 'test-user',
    room_id: 'test-room',
    bet_amount: 10.00,
    metadata: {
      game_type: 'prize_wheel' as const,
      timestamp: new Date().toISOString(),
    },
  };

  const mockEntryFeeResult = {
    success: true,
    transaction: createMockTransaction(),
    current_balance: 90.00,
    previous_balance: 100.00,
    entry_fee_amount: 10.00,
    timestamp: new Date().toISOString(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    apiClient.validateBalance = vi.fn().mockResolvedValue({ success: true, has_sufficient_balance: true });
    apiClient.processEntryFee = vi.fn().mockResolvedValue(mockEntryFeeResult);
    apiClient.processRefund = vi.fn().mockResolvedValue({ success: true, refunded_amount: 10.00 });
  });

  describe('BalanceAndEntryFee Component', () => {
    it('should display balance and entry fee information', async () => {
      render(
        <TestWrapper>
          <BalanceAndEntryFee 
            roomId="test-room" 
            betAmount={10.00}
            showEntryFeeStatus={true}
          />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Current Balance')).toBeInTheDocument();
        expect(screen.getByText('Entry Fee')).toBeInTheDocument();
        expect(screen.getByText('$10.00')).toBeInTheDocument();
      });
    });

    it('should show insufficient balance warning', async () => {
      apiClient.validateBalance = vi.fn().mockResolvedValue({ 
        success: false, 
        has_sufficient_balance: false,
        current_balance: 5.00,
        required_amount: 10.00,
      });

      render(
        <TestWrapper>
          <BalanceAndEntryFee 
            roomId="test-room" 
            betAmount={10.00}
            showEntryFeeStatus={true}
          />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText(/Insufficient balance/)).toBeInTheDocument();
        expect(screen.getByText(/Need \$5\.00 more/)).toBeInTheDocument();
      });
    });

    it('should process entry fee when button clicked', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <BalanceAndEntryFee 
            roomId="test-room" 
            betAmount={10.00}
            showEntryFeeStatus={true}
          />
        </TestWrapper>
      );

      const payButton = await screen.findByText('Pay Entry Fee');
      await user.click(payButton);

      await waitFor(() => {
        expect(apiClient.processEntryFee).toHaveBeenCalledWith(
          expect.objectContaining({
            user_id: expect.any(String),
            room_id: 'test-room',
            bet_amount: 10.00,
          })
        );
      });
    });
  });

  describe('useEntryFeeProcessing Hook', () => {
    it('should process entry fee successfully', async () => {
      const { result } = renderHook(() => useEntryFeeProcessing('test-room', 10.00), {
        wrapper: TestWrapper,
      });

      await act(async () => {
        const success = await result.current.processEntryFee();
        expect(success).toBe(true);
      });

      expect(result.current.status).toBe('paid');
      expect(result.current.isPaid).toBe(true);
      expect(apiClient.processEntryFee).toHaveBeenCalled();
    });

    it('should handle entry fee processing failure', async () => {
      apiClient.processEntryFee = vi.fn().mockRejectedValue(new Error('Processing failed'));

      const { result } = renderHook(() => useEntryFeeProcessing('test-room', 10.00), {
        wrapper: TestWrapper,
      });

      await act(async () => {
        const success = await result.current.processEntryFee();
        expect(success).toBe(false);
      });

      expect(result.current.status).toBe('failed');
      expect(result.current.error).toBeTruthy();
    });

    it('should process refund successfully', async () => {
      const { result } = renderHook(() => useEntryFeeProcessing('test-room', 10.00), {
        wrapper: TestWrapper,
      });

      // First pay entry fee
      await act(async () => {
        await result.current.processEntryFee();
      });

      // Then process refund
      await act(async () => {
        const success = await result.current.processRefund();
        expect(success).toBe(true);
      });

      expect(result.current.status).toBe('refunded');
      expect(apiClient.processRefund).toHaveBeenCalled();
    });
  });
});

describe('Balance Validation', () => {
  const mockUser = createMockUser({ balance: 100.00 });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useRealTimeBalance Hook', () => {
    it('should track balance correctly', async () => {
      const { result } = renderHook(() => useRealTimeBalance(), {
        wrapper: TestWrapper,
      });

      await waitFor(() => {
        expect(result.current.current).toBe(mockUser.balance);
        expect(result.current.loading).toBe(false);
      });
    });

    it('should detect insufficient balance', async () => {
      const { result } = renderHook(() => useRealTimeBalance(), {
        wrapper: TestWrapper,
      });

      await waitFor(() => {
        expect(result.current.hasInsufficientBalance(150.00)).toBe(true);
        expect(result.current.hasInsufficientBalance(50.00)).toBe(false);
      });
    });

    it('should calculate shortfall correctly', async () => {
      const { result } = renderHook(() => useRealTimeBalance(), {
        wrapper: TestWrapper,
      });

      await waitFor(() => {
        expect(result.current.getShortfall(150.00)).toBe(50.00);
        expect(result.current.getShortfall(50.00)).toBe(0);
      });
    });

    it('should update balance on socket events', async () => {
      const { result } = renderHook(() => useRealTimeBalance(), {
        wrapper: TestWrapper,
      });

      // Simulate balance update event
      act(() => {
        const mockSocket = socketManager.getSocket();
        const balanceUpdateHandler = mockSocket?.listeners('user_balance_updated')[0];
        balanceUpdateHandler?.({
          userId: mockUser.id,
          newBalance: 120.00,
          previousBalance: 100.00,
          changeAmount: 20.00,
          changeReason: 'prize_payout',
          timestamp: new Date().toISOString(),
        });
      });

      await waitFor(() => {
        expect(result.current.current).toBe(120.00);
      });
    });
  });
});

describe('Socket Events', () => {
  let mockSocket: any;

  beforeEach(() => {
    mockSocket = {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
      connect: vi.fn(),
      disconnect: vi.fn(),
      connected: true,
    };
    socketManager.getSocket = vi.fn().mockReturnValue(mockSocket);
  });

  describe('Socket Event Handling', () => {
    it('should handle user_balance_updated event', async () => {
      const balanceUpdateData = {
        userId: 'test-user',
        newBalance: 120.00,
        previousBalance: 100.00,
        changeAmount: 20.00,
        changeReason: 'prize_payout',
        timestamp: new Date().toISOString(),
      };

      render(
        <TestWrapper>
          <PrizeWheelReadySection roomId="test-room" />
        </TestWrapper>
      );

      // Verify event listener was registered
      expect(mockSocket.on).toHaveBeenCalledWith('user_balance_updated', expect.any(Function));

      // Simulate event
      const balanceHandler = mockSocket.on.mock.calls.find(call => call[0] === 'user_balance_updated')[1];
      act(() => {
        balanceHandler(balanceUpdateData);
      });

      // Verify balance was updated
      await waitFor(() => {
        expect(screen.getByText('$120.00')).toBeInTheDocument();
      });
    });

    it('should handle wheel_result event', async () => {
      const wheelResultData = {
        roomId: 'test-room',
        winner: {
          userId: 'test-user',
          username: 'testuser',
          selectedColor: 'red',
          prizeAmount: 95.00,
        },
        winningColor: 'red',
        prizePool: createMockPrizePool(),
        participantCount: 2,
        gameId: 'game-123',
        timestamp: new Date().toISOString(),
      };

      render(
        <TestWrapper>
          <PrizeWheelReadySection roomId="test-room" />
        </TestWrapper>
      );

      // Verify event listener was registered
      expect(mockSocket.on).toHaveBeenCalledWith('wheel_result', expect.any(Function));

      // Simulate event
      const resultHandler = mockSocket.on.mock.calls.find(call => call[0] === 'wheel_result')[1];
      act(() => {
        resultHandler(wheelResultData);
      });

      // Verify winner announcement is shown
      await waitFor(() => {
        expect(screen.getByText('🎉 WINNER! 🎉')).toBeInTheDocument();
        expect(screen.getByText('testuser')).toBeInTheDocument();
        expect(screen.getByText('$95.00')).toBeInTheDocument();
      });
    });

    it('should handle enhanced_room_info_updated event', async () => {
      const roomInfoData = {
        room: {
          id: 'test-room',
          name: 'Test Room',
          playerCount: 3,
          maxPlayers: 8,
        },
        prizePool: createMockPrizePool({ player_count: 3 }),
        playerStatuses: {},
        timestamp: new Date().toISOString(),
      };

      render(
        <TestWrapper>
          <PrizeWheelReadySection roomId="test-room" />
        </TestWrapper>
      );

      // Verify event listener was registered
      expect(mockSocket.on).toHaveBeenCalledWith('enhanced_room_info_updated', expect.any(Function));

      // Simulate event
      const roomHandler = mockSocket.on.mock.calls.find(call => call[0] === 'enhanced_room_info_updated')[1];
      act(() => {
        roomHandler(roomInfoData);
      });

      // Verify room info was updated
      await waitFor(() => {
        expect(screen.getByText('3/8 players')).toBeInTheDocument();
      });
    });
  });
});

describe('Integration Tests', () => {
  describe('usePrizeWheelIntegration Hook', () => {
    it('should provide comprehensive integration', async () => {
      const { result } = renderHook(() => usePrizeWheelIntegration('test-room', 10.00), {
        wrapper: TestWrapper,
      });

      await waitFor(() => {
        expect(result.current.prizePool).toBeDefined();
        expect(result.current.balance).toBeDefined();
        expect(result.current.entryFee).toBeDefined();
        expect(result.current.actions).toBeDefined();
      });
    });

    it('should validate participation correctly', async () => {
      const { result } = renderHook(() => usePrizeWheelIntegration('test-room', 10.00), {
        wrapper: TestWrapper,
      });

      await waitFor(() => {
        const validation = result.current.actions.validateParticipation();
        expect(validation.canParticipate).toBeDefined();
        expect(validation.blockers).toBeDefined();
      });
    });

    it('should prepare for game successfully', async () => {
      const { result } = renderHook(() => usePrizeWheelIntegration('test-room', 10.00), {
        wrapper: TestWrapper,
      });

      await waitFor(() => {
        expect(result.current.canParticipate).toBe(true);
      });

      await act(async () => {
        const success = await result.current.actions.prepareForGame();
        expect(success).toBe(true);
      });
    });
  });

  describe('Complete Game Flow', () => {
    it('should handle complete prize wheel game flow', async () => {
      const user = userEvent.setup();

      render(
        <TestWrapper>
          <PrizeWheelReadySection roomId="test-room" />
        </TestWrapper>
      );

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Prize Pool')).toBeInTheDocument();
      });

      // Pay entry fee
      const payButton = await screen.findByText(/Pay Entry Fee/);
      await user.click(payButton);

      // Verify entry fee was processed
      await waitFor(() => {
        expect(apiClient.processEntryFee).toHaveBeenCalled();
      });

      // Verify ready state
      await waitFor(() => {
        expect(screen.getByText(/Ready to Play/)).toBeInTheDocument();
      });
    });
  });
});

describe('Error Handling', () => {
  describe('usePrizeWheelErrorHandler Hook', () => {
    it('should handle API errors correctly', async () => {
      const { result } = renderHook(() => usePrizeWheelErrorHandler(), {
        wrapper: TestWrapper,
      });

      const mockError = {
        response: {
          data: {
            error_code: 'INSUFFICIENT_BALANCE',
            error_message: 'Not enough balance',
            details: { currentBalance: 5, requiredAmount: 10 },
          },
        },
      };

      const handledError = result.current.handleError(mockError);
      expect(handledError.code).toBe('INSUFFICIENT_BALANCE');
      expect(handledError.message).toContain('Not enough balance');
    });

    it('should retry API calls with exponential backoff', async () => {
      const { result } = renderHook(() => usePrizeWheelErrorHandler(), {
        wrapper: TestWrapper,
      });

      let attempts = 0;
      const mockApiCall = vi.fn(() => {
        attempts++;
        if (attempts < 3) {
          throw new Error('Network error');
        }
        return Promise.resolve('success');
      });

      const response = await result.current.handleApiErrorWithRetry(mockApiCall, 3, 100);
      expect(response).toBe('success');
      expect(attempts).toBe(3);
    });
  });
});
