/**
 * Comprehensive tests for Prize Wheel custom hooks
 */

import { renderHook, act, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';

// Hooks
import { usePrizePoolData } from '../../hooks/usePrizePoolData';
import { useRealTimeBalance } from '../../hooks/useRealTimeBalance';
import { useEntryFeeProcessing } from '../../hooks/useEntryFeeProcessing';
import { usePrizeWheelIntegration } from '../../hooks/usePrizeWheelIntegration';
import { usePrizeWheelErrorHandler } from '../../hooks/usePrizeWheelErrorHandler';

// Services
import { apiClient } from '../../services/api';
import { socketManager } from '../../services/socket';

// Test utilities
import { createMockPrizePool, createMockTransaction, createMockUser } from '../utils/factories';
import { TestWrapper } from '../utils/TestWrapper';

// Mock dependencies
vi.mock('../../services/api');
vi.mock('../../services/socket');
vi.mock('react-hot-toast');

describe('usePrizePoolData Hook', () => {
  const mockPrizePool = createMockPrizePool();

  beforeEach(() => {
    vi.clearAllMocks();
    apiClient.getRoomPrizePool = vi.fn().mockResolvedValue(mockPrizePool);
    apiClient.getPotentialWinnings = vi.fn().mockResolvedValue({
      potential_winnings: 95.00,
      win_probability: 0.5,
      return_multiplier: 9.5,
    });
  });

  it('should load prize pool data on mount', async () => {
    const { result } = renderHook(() => usePrizePoolData('test-room'), {
      wrapper: TestWrapper,
    });

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.prizePool).toEqual(mockPrizePool);
    });

    expect(apiClient.getRoomPrizePool).toHaveBeenCalledWith('test-room');
  });

  it('should provide derived data correctly', async () => {
    const { result } = renderHook(() => usePrizePoolData('test-room'), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.totalPool).toBe(mockPrizePool.total_pool);
      expect(result.current.netPrize).toBe(mockPrizePool.net_prize_amount);
      expect(result.current.houseEdge).toBe(mockPrizePool.house_edge_percentage);
      expect(result.current.playerCount).toBe(mockPrizePool.player_count);
      expect(result.current.maxPlayers).toBe(mockPrizePool.max_players);
      expect(result.current.isFull).toBe(mockPrizePool.is_full);
    });
  });

  it('should handle error state', async () => {
    const errorMessage = 'Failed to load prize pool';
    apiClient.getRoomPrizePool = vi.fn().mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => usePrizePoolData('test-room'), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
      expect(result.current.prizePool).toBeNull();
    });
  });

  it('should refresh data when called', async () => {
    const { result } = renderHook(() => usePrizePoolData('test-room'), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    await act(async () => {
      await result.current.actions.refreshData();
    });

    expect(apiClient.getRoomPrizePool).toHaveBeenCalledTimes(2);
  });

  it('should load potential winnings when requested', async () => {
    const { result } = renderHook(() => usePrizePoolData('test-room'), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    await act(async () => {
      await result.current.actions.loadPotentialWinnings();
    });

    expect(apiClient.getPotentialWinnings).toHaveBeenCalledWith('test-room');
    expect(result.current.potentialWinnings).toBeDefined();
  });
});

describe('useRealTimeBalance Hook', () => {
  const mockUser = createMockUser({ balance: 100.00 });

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock auth store
    vi.mocked(require('../../store/authStore')).useAuthStore.mockReturnValue({
      user: mockUser,
      updateBalance: vi.fn(),
    });
  });

  it('should initialize with user balance', async () => {
    const { result } = renderHook(() => useRealTimeBalance(), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.current).toBe(mockUser.balance);
      expect(result.current.loading).toBe(false);
    });
  });

  it('should detect insufficient balance correctly', async () => {
    const { result } = renderHook(() => useRealTimeBalance(), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.hasInsufficientBalance(150.00)).toBe(true);
      expect(result.current.hasInsufficientBalance(50.00)).toBe(false);
      expect(result.current.hasInsufficientBalance(100.00)).toBe(false);
    });
  });

  it('should calculate shortfall correctly', async () => {
    const { result } = renderHook(() => useRealTimeBalance(), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.getShortfall(150.00)).toBe(50.00);
      expect(result.current.getShortfall(50.00)).toBe(0);
      expect(result.current.getShortfall(100.00)).toBe(0);
    });
  });

  it('should validate balance against required amount', async () => {
    const { result } = renderHook(() => useRealTimeBalance(), {
      wrapper: TestWrapper,
    });

    await act(async () => {
      const isValid = await result.current.validateBalance(50.00);
      expect(isValid).toBe(true);
    });

    await act(async () => {
      const isValid = await result.current.validateBalance(150.00);
      expect(isValid).toBe(false);
    });
  });
});

describe('useEntryFeeProcessing Hook', () => {
  const mockEntryFeeResult = {
    success: true,
    transaction: createMockTransaction(),
    current_balance: 90.00,
    previous_balance: 100.00,
    entry_fee_amount: 10.00,
    timestamp: new Date().toISOString(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    apiClient.processEntryFee = vi.fn().mockResolvedValue(mockEntryFeeResult);
    apiClient.processRefund = vi.fn().mockResolvedValue({
      success: true,
      refunded_amount: 10.00,
      current_balance: 100.00,
      timestamp: new Date().toISOString(),
    });
  });

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useEntryFeeProcessing('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    expect(result.current.status).toBe('required');
    expect(result.current.processing).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.isPaid).toBe(false);
    expect(result.current.isRequired).toBe(true);
    expect(result.current.canPay).toBe(true);
    expect(result.current.canRefund).toBe(false);
  });

  it('should process entry fee successfully', async () => {
    const { result } = renderHook(() => useEntryFeeProcessing('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    await act(async () => {
      const success = await result.current.processEntryFee();
      expect(success).toBe(true);
    });

    expect(result.current.status).toBe('paid');
    expect(result.current.isPaid).toBe(true);
    expect(result.current.canRefund).toBe(true);
    expect(apiClient.processEntryFee).toHaveBeenCalledWith(
      expect.objectContaining({
        room_id: 'test-room',
        bet_amount: 10.00,
      })
    );
  });

  it('should handle entry fee processing failure', async () => {
    const errorMessage = 'Insufficient balance';
    apiClient.processEntryFee = vi.fn().mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useEntryFeeProcessing('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    await act(async () => {
      const success = await result.current.processEntryFee();
      expect(success).toBe(false);
    });

    expect(result.current.status).toBe('failed');
    expect(result.current.error).toBe(errorMessage);
    expect(result.current.isPaid).toBe(false);
  });

  it('should process refund successfully', async () => {
    const { result } = renderHook(() => useEntryFeeProcessing('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    // First pay entry fee
    await act(async () => {
      await result.current.processEntryFee();
    });

    expect(result.current.status).toBe('paid');

    // Then process refund
    await act(async () => {
      const success = await result.current.processRefund();
      expect(success).toBe(true);
    });

    expect(result.current.status).toBe('refunded');
    expect(apiClient.processRefund).toHaveBeenCalledWith(
      expect.objectContaining({
        room_id: 'test-room',
        bet_amount: 10.00,
      })
    );
  });

  it('should not allow refund if not paid', async () => {
    const { result } = renderHook(() => useEntryFeeProcessing('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    await act(async () => {
      const success = await result.current.processRefund();
      expect(success).toBe(false);
    });

    expect(apiClient.processRefund).not.toHaveBeenCalled();
  });
});

describe('usePrizeWheelIntegration Hook', () => {
  const mockPrizePool = createMockPrizePool();
  const mockUser = createMockUser({ balance: 100.00 });

  beforeEach(() => {
    vi.clearAllMocks();
    apiClient.getRoomPrizePool = vi.fn().mockResolvedValue(mockPrizePool);
    apiClient.processEntryFee = vi.fn().mockResolvedValue({
      success: true,
      transaction: createMockTransaction(),
      current_balance: 90.00,
    });
  });

  it('should provide comprehensive integration', async () => {
    const { result } = renderHook(() => usePrizeWheelIntegration('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.prizePool).toBeDefined();
      expect(result.current.balance).toBeDefined();
      expect(result.current.entryFee).toBeDefined();
      expect(result.current.actions).toBeDefined();
    });
  });

  it('should validate participation correctly', async () => {
    const { result } = renderHook(() => usePrizeWheelIntegration('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      const validation = result.current.actions.validateParticipation();
      expect(validation.canParticipate).toBe(true);
      expect(validation.blockers).toHaveLength(0);
    });
  });

  it('should identify participation blockers', async () => {
    // Mock insufficient balance
    const poorUser = createMockUser({ balance: 5.00 });
    vi.mocked(require('../../store/authStore')).useAuthStore.mockReturnValue({
      user: poorUser,
      updateBalance: vi.fn(),
    });

    const { result } = renderHook(() => usePrizeWheelIntegration('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      const validation = result.current.actions.validateParticipation();
      expect(validation.canParticipate).toBe(false);
      expect(validation.blockers).toContain(expect.stringContaining('Insufficient balance'));
    });
  });

  it('should prepare for game successfully', async () => {
    const { result } = renderHook(() => usePrizeWheelIntegration('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.canParticipate).toBe(true);
    });

    await act(async () => {
      const success = await result.current.actions.prepareForGame();
      expect(success).toBe(true);
    });

    expect(apiClient.processEntryFee).toHaveBeenCalled();
  });

  it('should handle preparation failure', async () => {
    apiClient.processEntryFee = vi.fn().mockRejectedValue(new Error('Processing failed'));

    const { result } = renderHook(() => usePrizeWheelIntegration('test-room', 10.00), {
      wrapper: TestWrapper,
    });

    await waitFor(() => {
      expect(result.current.canParticipate).toBe(true);
    });

    await act(async () => {
      const success = await result.current.actions.prepareForGame();
      expect(success).toBe(false);
    });
  });
});

describe('usePrizeWheelErrorHandler Hook', () => {
  it('should handle different error types correctly', () => {
    const { result } = renderHook(() => usePrizeWheelErrorHandler(), {
      wrapper: TestWrapper,
    });

    // Test insufficient balance error
    const balanceError = {
      response: {
        data: {
          error_code: 'INSUFFICIENT_BALANCE',
          error_message: 'Not enough balance',
          details: { currentBalance: 5, requiredAmount: 10 },
        },
      },
    };

    const handledError = result.current.handleBalanceError(balanceError, 5, 10);
    expect(handledError.code).toBe('INSUFFICIENT_BALANCE');
    expect(handledError.message).toContain('Not enough balance');
  });

  it('should retry API calls with exponential backoff', async () => {
    const { result } = renderHook(() => usePrizeWheelErrorHandler(), {
      wrapper: TestWrapper,
    });

    let attempts = 0;
    const mockApiCall = vi.fn(() => {
      attempts++;
      if (attempts < 3) {
        throw new Error('Network error');
      }
      return Promise.resolve('success');
    });

    const response = await result.current.handleApiErrorWithRetry(mockApiCall, 3, 100);
    expect(response).toBe('success');
    expect(attempts).toBe(3);
  });

  it('should identify error types correctly', () => {
    const { result } = renderHook(() => usePrizeWheelErrorHandler(), {
      wrapper: TestWrapper,
    });

    const error = { code: 'INSUFFICIENT_BALANCE' };
    expect(result.current.isErrorType(error, 'INSUFFICIENT_BALANCE')).toBe(true);
    expect(result.current.isErrorType(error, 'NETWORK_ERROR')).toBe(false);
  });

  it('should handle non-retryable errors', async () => {
    const { result } = renderHook(() => usePrizeWheelErrorHandler(), {
      wrapper: TestWrapper,
    });

    const mockApiCall = vi.fn(() => {
      throw { code: 'UNAUTHORIZED', message: 'Not authorized' };
    });

    await expect(
      result.current.handleApiErrorWithRetry(mockApiCall, 3, 100)
    ).rejects.toThrow();

    expect(mockApiCall).toHaveBeenCalledTimes(1); // Should not retry
  });
});
