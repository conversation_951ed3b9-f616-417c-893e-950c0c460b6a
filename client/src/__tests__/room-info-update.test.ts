/**
 * Test for room_info_updated event handling and custom event dispatch
 * This test verifies that the fix for missing player slots is working correctly
 */

import { describe, it, expect } from 'vitest';

describe('Room Info Update Fix Verification', () => {
  it('should verify that the fix is in place in socketStore', async () => {
    // Read the socket store file content to verify the fix is present
    const fs = await import('fs');
    const path = await import('path');

    const socketStorePath = path.resolve(__dirname, '../store/socketStore.ts');
    const content = fs.readFileSync(socketStorePath, 'utf-8');

    // Verify that the fix line is present (updated to match new implementation)
    expect(content).toContain('roomSubscriptionFlowManager.handleRoomInfoUpdate(roomId, normalizedData)');

    // Verify it's in the correct context (after the console.log)
    const lines = content.split('\n');
    const fixLineIndex = lines.findIndex(line =>
      line.includes('roomSubscriptionFlowManager.handleRoomInfoUpdate(roomId, normalizedData)')
    );

    expect(fixLineIndex).toBeGreaterThan(-1);

    // Verify it's after the console.log statement
    const consoleLogIndex = lines.findIndex(line =>
      line.includes('console.log(\'Enhanced room info updated received:\'')
    );

    expect(fixLineIndex).toBeGreaterThan(consoleLogIndex);
  });

  it('should verify that roomSubscriptionFlowManager is imported', async () => {
    const fs = await import('fs');
    const path = await import('path');

    const socketStorePath = path.resolve(__dirname, '../store/socketStore.ts');
    const content = fs.readFileSync(socketStorePath, 'utf-8');

    // Verify the import is present
    expect(content).toContain('import { roomSubscriptionFlowManager } from \'@/utils/roomSubscriptionFlow\'');
  });

  it('should verify that the fix is in the room_info_updated event handler', async () => {
    const fs = await import('fs');
    const path = await import('path');

    const socketStorePath = path.resolve(__dirname, '../store/socketStore.ts');
    const content = fs.readFileSync(socketStorePath, 'utf-8');

    // Find the room_info_updated event handler
    const handlerStartIndex = content.indexOf('socketService.on(\'room_info_updated\'');
    expect(handlerStartIndex).toBeGreaterThan(-1);

    // Find the end of this handler (next socketService.on call)
    const nextHandlerIndex = content.indexOf('socketService.on(', handlerStartIndex + 1);
    const handlerContent = content.substring(handlerStartIndex, nextHandlerIndex);

    // Verify the fix is within this handler (updated to match new implementation)
    expect(handlerContent).toContain('roomSubscriptionFlowManager.handleRoomInfoUpdate(roomId, normalizedData)');
  });
});
