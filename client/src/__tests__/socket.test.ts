import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { socketService } from '../services/socket';
import { config } from '@/config/env';

// Mock socket.io-client
const mockSocket = {
  connected: false,
  connect: vi.fn(),
  disconnect: vi.fn(),
  emit: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
};

vi.mock('socket.io-client', () => ({
  io: vi.fn(() => mockSocket),
}));

// Mock dependencies
vi.mock('../services/api', () => ({
  apiClient: {
    getAuthToken: vi.fn(() => 'mock-token'),
  },
}));

vi.mock('@/store/authStore', () => ({
  useAuthStore: {
    getState: vi.fn(() => ({
      updateBalance: vi.fn(),
    })),
  },
}));

vi.mock('@/utils/subscriptionManager', () => ({
  subscriptionManager: {
    handleDisconnect: vi.fn(),
  },
}));

describe('SocketService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockSocket.connected = false;
  });

  afterEach(() => {
    socketService.disconnect();
  });

  describe('Connection Management', () => {
    it('should connect successfully', async () => {
      mockSocket.connected = true;
      
      // Mock successful connection
      mockSocket.on.mockImplementation((event, callback) => {
        if (event === 'connect') {
          setTimeout(() => callback(), 0);
        }
      });

      await expect(socketService.connect()).resolves.toBeUndefined();
      expect(socketService.isConnected).toBe(true);
    });

    it('should handle connection timeout', async () => {
      mockSocket.connected = false;
      
      // Don't trigger any events to simulate timeout
      mockSocket.on.mockImplementation(() => {});

      await expect(socketService.connect()).rejects.toThrow('Connection timeout');
    });

    it('should disconnect properly', () => {
      mockSocket.connected = true;
      socketService.disconnect();
      
      expect(mockSocket.disconnect).toHaveBeenCalled();
      expect(socketService.isConnected).toBe(false);
    });
  });

  describe('Lobby Management', () => {
    beforeEach(() => {
      mockSocket.connected = true;
    });

    it('should subscribe to lobby successfully', async () => {
      mockSocket.emit.mockImplementation((event, callback) => {
        if (event === 'subscribe_lobby') {
          callback({ success: true });
        }
      });

      const response = await socketService.subscribeLobby();
      expect(response.success).toBe(true);
      expect(mockSocket.emit).toHaveBeenCalledWith('subscribe_lobby', expect.any(Function));
    });

    it('should handle lobby subscription failure', async () => {
      mockSocket.emit.mockImplementation((event, callback) => {
        if (event === 'subscribe_lobby') {
          callback({ success: false, error: 'Subscription failed' });
        }
      });

      await expect(socketService.subscribeLobby()).rejects.toThrow('Subscription failed');
    });
  });

  describe('Room Management', () => {
    beforeEach(() => {
      mockSocket.connected = true;
    });

    it('should join room successfully', async () => {
      const mockRoomData = {
        id: 'room-123',
        name: 'Test Room',
        gameType: 'PRIZEWHEEL',
        status: 'WAITING',
        players: [],
        playerCount: 1,
        maxPlayers: 8,
        betAmount: 100,
        prizePool: 0,
      };

      mockSocket.emit.mockImplementation((event, data, callback) => {
        if (event === 'join_room') {
          callback({ 
            success: true, 
            room: mockRoomData,
            player: { balance: 1000 }
          });
        }
      });

      const response = await socketService.joinRoom('room-123');
      expect(response.success).toBe(true);
      expect(socketService.currentRoom).toBeTruthy();
      expect(socketService.currentRoom?.id).toBe('room-123');
    });

    it('should handle room join failure', async () => {
      mockSocket.emit.mockImplementation((event, data, callback) => {
        if (event === 'join_room') {
          callback({ 
            success: false, 
            error: 'Room is full',
            code: 'ROOM_FULL'
          });
        }
      });

      await expect(socketService.joinRoom('room-123')).rejects.toThrow();
    });
  });

  describe('Player Actions', () => {
    beforeEach(() => {
      mockSocket.connected = true;
    });

    it('should set player ready successfully', async () => {
      mockSocket.emit.mockImplementation((event, data, callback) => {
        if (event === 'player_ready_spec') {
          callback({ success: true });
        }
      });

      const response = await socketService.setPlayerReady('room-123', true);
      expect(response.success).toBe(true);
    });

    it('should select wheel color successfully', async () => {
      mockSocket.emit.mockImplementation((event, data, callback) => {
        if (event === 'select_color_spec') {
          callback({ success: true });
        }
      });

      const response = await socketService.selectWheelColor('room-123', 'red');
      expect(response.success).toBe(true);
    });
  });

  describe('Event System', () => {
    it('should register and emit events correctly', () => {
      const mockCallback = vi.fn();

      socketService.on('test_event', mockCallback);
      (socketService as any).emit('test_event', { data: 'test' });

      expect(mockCallback).toHaveBeenCalledWith({ data: 'test' });
    });

    it('should remove event listeners correctly', () => {
      const mockCallback = vi.fn();

      socketService.on('test_event', mockCallback);
      socketService.off('test_event', mockCallback);
      (socketService as any).emit('test_event', { data: 'test' });

      expect(mockCallback).not.toHaveBeenCalled();
    });

    it('should handle color_selection_updated event', () => {
      const mockCallback = vi.fn();
      const mockColorSelectionData = {
        action: 'color_selected',
        colorState: {
          assignments: {
            'user123': {
              color: { hex: '#FFC0CB', id: 'pink', name: 'Pink' },
              selectedAt: '2025-06-17T17:34:45+07:00',
              userId: 'user123',
              username: 'testuser'
            }
          },
          availableColors: [
            { hex: '#FF0000', id: 'red', name: 'Red' },
            { hex: '#0000FF', id: 'blue', name: 'Blue' }
          ],
          statistics: {
            availableCount: 7,
            selectionRate: 12.5,
            takenCount: 1,
            totalColors: 8
          }
        },
        roomId: 'room123',
        player: {
          color: { hex: '#FFC0CB', id: 'pink', name: 'Pink' },
          userId: 'user123',
          username: 'testuser'
        }
      };

      socketService.on('color_selection_updated', mockCallback);
      (socketService as any).emit('color_selection_updated', mockColorSelectionData);

      expect(mockCallback).toHaveBeenCalledWith(mockColorSelectionData);
    });
  });

  describe('Utility Methods', () => {
    beforeEach(() => {
      mockSocket.connected = true;
    });

    it('should ping successfully', () => {
      mockSocket.emit.mockImplementation((event, callback) => {
        if (event === 'ping') {
          callback({ timestamp: Date.now() });
        }
      });

      socketService.ping();
      expect(mockSocket.emit).toHaveBeenCalledWith('ping', expect.any(Function));
    });

    it('should return correct connection state', () => {
      const connectionState = socketService.connectionState;
      expect(connectionState).toHaveProperty('state');
      expect(connectionState).toHaveProperty('reconnectAttempts');
    });
  });
});
