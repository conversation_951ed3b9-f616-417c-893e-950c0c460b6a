/**
 * Test setup file for vitest
 */

import { vi } from 'vitest';

// Mock window object
Object.defineProperty(window, 'dispatchEvent', {
  value: vi.fn(),
  writable: true,
});

Object.defineProperty(window, 'addEventListener', {
  value: vi.fn(),
  writable: true,
});

Object.defineProperty(window, 'removeEventListener', {
  value: vi.fn(),
  writable: true,
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};
