import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import RoomPage from '../pages/RoomPage';
import { useGameStore } from '../store/gameStore';
import { useSocketStore } from '../store/socketStore';
import { useAuthStore } from '../store/authStore';

// Mock the stores
vi.mock('../store/gameStore');
vi.mock('../store/socketStore');
vi.mock('../store/authStore');
vi.mock('../hooks/useRealTimeGameData');
vi.mock('../hooks/useRoomSubscription');

// Mock the components to avoid complex rendering
vi.mock('../components/Game/PlayerSlots', () => ({
  default: () => <div data-testid="player-slots">Player Slots</div>
}));

vi.mock('../components/Game/PrizeWheelReadySection', () => ({
  PrizeWheelReadySection: () => <div data-testid="prize-wheel">Prize Wheel Game</div>
}));

vi.mock('../components/Debug/RoomStateDebug', () => ({
  default: () => <div data-testid="room-debug">Debug Info</div>
}));

vi.mock('../components/Debug/RoomPlayerDebugPanel', () => ({
  default: () => <div data-testid="player-debug">Player Debug</div>
}));

describe('Game Type Updates', () => {
  const mockUser = { id: 'user1', username: 'testuser' };
  
  beforeEach(() => {
    // Mock auth store
    (useAuthStore as any).mockReturnValue({
      user: mockUser
    });
    
    // Mock game store
    (useGameStore as any).mockReturnValue({
      currentRoom: null,
      loadRoomDetails: vi.fn()
    });
    
    // Mock socket store base
    (useSocketStore as any).mockReturnValue({
      currentRoom: null,
      currentGame: null,
      leaveRoom: vi.fn(),
      isLeavingRoom: vi.fn(() => false),
      waitingForRoomInfo: false
    });
  });

  it('should show prize wheel for GAME_TYPE_PRIZE_WHEEL and ROOM_STATUS_WAITING', () => {
    const mockRoom = {
      id: 'room1',
      name: 'Test Room',
      gameType: 'GAME_TYPE_PRIZE_WHEEL',
      status: 'ROOM_STATUS_WAITING',
      maxPlayers: 3,
      playerCount: 1,
      players: [
        {
          userId: 'user1',
          username: 'testuser',
          position: 1,
          isReady: false
        }
      ]
    };

    (useSocketStore as any).mockReturnValue({
      currentRoom: mockRoom,
      currentGame: null,
      leaveRoom: vi.fn(),
      isLeavingRoom: vi.fn(() => false),
      waitingForRoomInfo: false
    });

    render(
      <BrowserRouter>
        <RoomPage />
      </BrowserRouter>
    );

    // Should show prize wheel game
    expect(screen.getByTestId('prize-wheel')).toBeInTheDocument();
    expect(screen.getByTestId('player-slots')).toBeInTheDocument();
  });

  it('should show prize wheel for legacy PRIZEWHEEL and WAITING', () => {
    const mockRoom = {
      id: 'room1',
      name: 'Test Room',
      gameType: 'PRIZEWHEEL',
      status: 'WAITING',
      maxPlayers: 3,
      playerCount: 1,
      players: [
        {
          userId: 'user1',
          username: 'testuser',
          position: 1,
          isReady: false
        }
      ]
    };

    (useSocketStore as any).mockReturnValue({
      currentRoom: mockRoom,
      currentGame: null,
      leaveRoom: vi.fn(),
      isLeavingRoom: vi.fn(() => false),
      waitingForRoomInfo: false
    });

    render(
      <BrowserRouter>
        <RoomPage />
      </BrowserRouter>
    );

    // Should show prize wheel game
    expect(screen.getByTestId('prize-wheel')).toBeInTheDocument();
    expect(screen.getByTestId('player-slots')).toBeInTheDocument();
  });

  it('should not show prize wheel for non-waiting status', () => {
    const mockRoom = {
      id: 'room1',
      name: 'Test Room',
      gameType: 'GAME_TYPE_PRIZE_WHEEL',
      status: 'ROOM_STATUS_PLAYING',
      maxPlayers: 3,
      playerCount: 1,
      players: []
    };

    (useSocketStore as any).mockReturnValue({
      currentRoom: mockRoom,
      currentGame: null,
      leaveRoom: vi.fn(),
      isLeavingRoom: vi.fn(() => false),
      waitingForRoomInfo: false
    });

    render(
      <BrowserRouter>
        <RoomPage />
      </BrowserRouter>
    );

    // Should not show prize wheel game (falls back to default layout)
    expect(screen.queryByTestId('prize-wheel')).not.toBeInTheDocument();
  });
});
