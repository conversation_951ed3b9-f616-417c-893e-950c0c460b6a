import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '@/services/api';
import { socketService } from '@/services/socket';
import type { User, LoginRequest, RegisterRequest } from '@/types/api';

interface AuthState {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  clearError: () => void;
  setUser: (user: User) => void;
  updateBalance: (balance: number) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });

        try {
          const authData = await apiClient.login(credentials);

          set({
            user: authData.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          // Connect to socket after successful login
          try {
            await socketService.connect();
          } catch (socketError) {
            console.warn('Failed to connect to socket:', socketError);
            // Don't fail login if socket connection fails
          }
        } catch (error) {
          console.error('Login failed:', error);
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      register: async (userData: RegisterRequest) => {
        set({ isLoading: true, error: null });

        try {
          const authData = await apiClient.register(userData);

          set({
            user: authData.user,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          // Connect to socket after successful registration
          try {
            await socketService.connect();
          } catch (socketError) {
            console.warn('Failed to connect to socket:', socketError);
            // Don't fail registration if socket connection fails
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Registration failed';
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });

        try {
          // Disconnect socket first
          socketService.disconnect();

          // Then logout from API
          await apiClient.logout();
        } catch (error) {
          console.warn('Logout API call failed:', error);
          // Continue with logout even if API call fails
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      refreshUser: async () => {
        if (!get().isAuthenticated) return;

        try {
          const user = await apiClient.getProfile();
          set({ user, error: null });
        } catch (error) {
          console.error('Failed to refresh user:', error);
          // If refresh fails, user might need to re-login
          const errorMessage = error instanceof Error ? error.message : 'Failed to refresh user data';
          set({ error: errorMessage });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setUser: (user: User) => {
        set({ user });
      },

      updateBalance: (balance: number) => {
        const { user } = get();
        if (user) {
          set({
            user: {
              ...user,
              balance,
            },
          });
        }
      },
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
      onRehydrateStorage: () => (state) => {
        // After rehydration, if user is authenticated, initialize the connection
        if (state?.isAuthenticated && state?.user) {
          // Use setTimeout to ensure this runs after the store is fully initialized
          setTimeout(() => {
            initializeAuthAfterRehydration().catch(console.error);
          }, 0);
        }
      },
    }
  )
);

// Initialize auth after store rehydration
const initializeAuthAfterRehydration = async () => {
  const { isAuthenticated } = useAuthStore.getState();

  if (isAuthenticated && apiClient.isAuthenticated()) {
    try {
      console.log('Initializing auth after rehydration...');

      // Refresh user data
      await useAuthStore.getState().refreshUser();

      // Connect to socket
      console.log('Connecting to socket...');
      await socketService.connect();
      console.log('Socket connected successfully');
    } catch (error) {
      console.error('Failed to initialize auth after rehydration:', error);
      // If initialization fails, clear auth state
      useAuthStore.getState().logout();
    }
  }
};

// Initialize auth state on app start (simplified - rehydration will handle the rest)
export const initializeAuth = async () => {
  // This function now just ensures the store is set up
  // The actual initialization happens in onRehydrateStorage callback
  console.log('Auth store initialized, waiting for rehydration...');
};
