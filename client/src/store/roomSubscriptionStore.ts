/**
 * Room Subscription Store
 * Manages real-time room subscription state and prevents duplicate subscriptions
 */

import { create } from 'zustand';
import { socketService } from '@/services/socket';

interface RoomSubscriptionState {
  // Subscription state
  isSubscribed: boolean;
  subscribedRoomId: string | null;
  subscriptionLoading: boolean;
  subscriptionError: string | null;

  // Actions
  subscribeRoom: (roomId: string) => Promise<void>;
  unsubscribeRoom: (roomId?: string) => Promise<void>;
  markAsSubscribed: (roomId: string) => void;
  clearError: () => void;
  reset: () => void;
}

export const useRoomSubscriptionStore = create<RoomSubscriptionState>((set, get) => ({
  // Initial state
  isSubscribed: false,
  subscribedRoomId: null,
  subscriptionLoading: false,
  subscriptionError: null,

  // Actions
  subscribeRoom: async (roomId: string) => {
    const { isSubscribed, subscribedRoomId, subscriptionLoading } = get();

    // Prevent duplicate subscriptions
    if (isSubscribed && subscribedRoomId === roomId) {
      console.log(`Already subscribed to room ${roomId}, skipping duplicate subscription`);
      return;
    }

    if (subscriptionLoading) {
      console.log('Room subscription already in progress, skipping duplicate request');
      return;
    }

    if (!roomId) {
      throw new Error('Room ID is required for subscription');
    }

    console.log(`Starting room subscription for room: ${roomId}`);
    set({ subscriptionLoading: true, subscriptionError: null });

    try {
      // If already subscribed to a different room, unsubscribe first
      if (isSubscribed && subscribedRoomId && subscribedRoomId !== roomId) {
        console.log(`Unsubscribing from previous room ${subscribedRoomId} before subscribing to ${roomId}`);
        try {
          await socketService.unsubscribeRoom(subscribedRoomId);
        } catch (error) {
          console.warn(`Failed to unsubscribe from previous room ${subscribedRoomId}:`, error);
          // Continue with new subscription attempt
        }
      }

      // Wait for socket connection if not already connected
      if (!socketService.isConnected) {
        console.log('Socket not connected, waiting for connection...');

        // Wait for socket connection with timeout
        await new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Socket connection timeout'));
          }, 10000); // 10 second timeout

          const checkConnection = () => {
            if (socketService.isConnected) {
              clearTimeout(timeout);
              resolve();
            } else {
              setTimeout(checkConnection, 100); // Check every 100ms
            }
          };

          checkConnection();
        });
      }

      await socketService.subscribeRoom(roomId);
      set({
        isSubscribed: true,
        subscribedRoomId: roomId,
        subscriptionLoading: false,
        subscriptionError: null,
      });
      console.log(`Successfully subscribed to room: ${roomId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to subscribe to room';
      console.error(`Failed to subscribe to room ${roomId}:`, errorMessage);
      set({
        isSubscribed: false,
        subscribedRoomId: null,
        subscriptionLoading: false,
        subscriptionError: errorMessage,
      });
      throw error;
    }
  },

  unsubscribeRoom: async (roomId?: string) => {
    const { isSubscribed, subscribedRoomId, subscriptionLoading } = get();

    if (!isSubscribed || subscriptionLoading) {
      console.log('Not subscribed to any room or subscription in progress, skipping unsubscription');
      return;
    }

    const targetRoomId = roomId || subscribedRoomId;
    if (!targetRoomId) {
      console.warn('No room ID available for unsubscription');
      return;
    }

    console.log(`Starting room unsubscription for room: ${targetRoomId}`);
    set({ subscriptionLoading: true, subscriptionError: null });

    try {
      await socketService.unsubscribeRoom(targetRoomId);
      set({
        isSubscribed: false,
        subscribedRoomId: null,
        subscriptionLoading: false,
        subscriptionError: null,
      });
      console.log(`Successfully unsubscribed from room: ${targetRoomId}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to unsubscribe from room';
      console.error(`Failed to unsubscribe from room ${targetRoomId}:`, errorMessage);
      set({
        subscriptionLoading: false,
        subscriptionError: errorMessage,
      });
      throw error;
    }
  },

  markAsSubscribed: (roomId: string) => {
    console.log(`Marking room as subscribed (server-side subscription): ${roomId}`);
    set({
      isSubscribed: true,
      subscribedRoomId: roomId,
      subscriptionLoading: false,
      subscriptionError: null,
    });
  },

  clearError: () => {
    set({ subscriptionError: null });
  },

  reset: () => {
    set({
      isSubscribed: false,
      subscribedRoomId: null,
      subscriptionLoading: false,
      subscriptionError: null,
    });
  },
}));

// Helper hook for debugging room subscription state
export const useRoomSubscriptionDebug = () => {
  const state = useRoomSubscriptionStore();
  
  const logState = (context: string) => {
    console.log(`[${context}] Room Subscription State:`, {
      isSubscribed: state.isSubscribed,
      subscribedRoomId: state.subscribedRoomId,
      subscriptionLoading: state.subscriptionLoading,
      subscriptionError: state.subscriptionError,
      timestamp: new Date().toISOString(),
    });
  };

  return { ...state, logState };
};
