import React from 'react';
import { create } from 'zustand';
import { socketService } from '@/services/socket';
import { env } from '@/config/env';
import type { RoomListUpdateData, RoomUpdateData } from '@/types/socket';
import type { Room } from '@/types/api';

interface LobbyState {
  // Subscription state
  isSubscribed: boolean;
  subscriptionLoading: boolean;
  subscriptionError: string | null;

  // Real-time room updates
  lastUpdate: RoomListUpdateData | null;
  updateCount: number;

  // Actions
  subscribeLobby: () => Promise<void>;
  unsubscribeLobby: () => Promise<void>;
  handleRoomListUpdate: (data: RoomListUpdateData) => void;
  clearError: () => void;
  reset: () => void;
}

export const useLobbyStore = create<LobbyState>((set, get) => {
  // Set up socket event listener for room list updates
  const handleRoomListUpdate = (data: RoomListUpdateData) => {
    // Optimized logging for different action types
    if (data.action === 'subscribe' || data.action === 'unsubscribe') {
      if (env.DEBUG) {
        console.log(`📋 Lobby ${data.action}:`, {
          rooms: data.count || (data.rooms?.length || 0),
          user: data.username,
          source: data.source,
        });
      }
    }

    // Enhanced capacity change detection and logging (only for significant changes)
    if (data.action === 'player_count_changed' || data.action === 'updated') {
      const room = data.room;
      if (room && env.DEBUG) {
        const isFull = room.playerCount >= room.maxPlayers;
        const wasNearFull = room.playerCount >= Math.ceil(room.maxPlayers * 0.75);

        // Only log if room is full or near full
        if (isFull || wasNearFull) {
          console.log(`🏠 Room ${room.name}: ${room.playerCount}/${room.maxPlayers} players`);
        }
      }
    }

    set((state) => ({
      lastUpdate: data,
      updateCount: state.updateCount + 1,
    }));

    // Emit custom event for components to listen to
    window.dispatchEvent(new CustomEvent('roomListUpdate', { detail: data }));
  };

  // Initialize socket listener
  socketService.on('room_list_updated', handleRoomListUpdate);

  return {
    // Initial state
    isSubscribed: false,
    subscriptionLoading: false,
    subscriptionError: null,
    lastUpdate: null,
    updateCount: 0,

    // Actions
    subscribeLobby: async () => {
      const { isSubscribed, subscriptionLoading } = get();

      // Prevent duplicate subscriptions
      if (isSubscribed) {
        console.log('Already subscribed to lobby, skipping duplicate subscription');
        return;
      }

      if (subscriptionLoading) {
        console.log('Lobby subscription already in progress, skipping duplicate request');
        return;
      }

      console.log('Starting lobby subscription...');
      set({ subscriptionLoading: true, subscriptionError: null });

      try {
        // Wait for socket connection if not already connected
        if (!socketService.isConnected) {
          console.log('Socket not connected, waiting for connection...');

          // Wait for socket connection with timeout
          await new Promise<void>((resolve, reject) => {
            const timeout = setTimeout(() => {
              reject(new Error('Socket connection timeout'));
            }, 10000); // 10 second timeout

            const checkConnection = () => {
              if (socketService.isConnected) {
                clearTimeout(timeout);
                resolve();
              } else {
                setTimeout(checkConnection, 100); // Check every 100ms
              }
            };

            checkConnection();
          });
        }

        await socketService.subscribeLobby();
        set({
          isSubscribed: true,
          subscriptionLoading: false,
          subscriptionError: null,
        });
        console.log('Successfully subscribed to lobby');
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to subscribe to lobby';
        console.error('Failed to subscribe to lobby:', errorMessage);
        set({
          isSubscribed: false,
          subscriptionLoading: false,
          subscriptionError: errorMessage,
        });
        throw error;
      }
    },

    unsubscribeLobby: async () => {
      const { isSubscribed, subscriptionLoading } = get();

      if (!isSubscribed || subscriptionLoading) {
        return;
      }

      set({ subscriptionLoading: true, subscriptionError: null });

      try {
        await socketService.unsubscribeLobby();
        set({
          isSubscribed: false,
          subscriptionLoading: false,
          subscriptionError: null,
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to unsubscribe from lobby';
        set({
          subscriptionLoading: false,
          subscriptionError: errorMessage,
        });
        throw error;
      }
    },

    handleRoomListUpdate: (data: RoomListUpdateData) => {
      handleRoomListUpdate(data);
    },

    clearError: () => {
      set({ subscriptionError: null });
    },

    reset: () => {
      set({
        isSubscribed: false,
        subscriptionLoading: false,
        subscriptionError: null,
        lastUpdate: null,
        updateCount: 0,
      });
    },
  };
});

// Helper function to convert room update data to Room type
// Handles both camelCase and snake_case field names from server
export const convertUpdateDataToRoom = (updateData: RoomUpdateData): Partial<Room> => {
  // Handle both camelCase and snake_case field formats
  const roomData = updateData as any;

  const gameType = (roomData.gameType || roomData.game_type || 'prizewheel').toUpperCase();
  const playerCount = roomData.playerCount ?? roomData.current_players ?? 0;
  const maxPlayers = roomData.maxPlayers ?? roomData.max_players ?? 4;
  const betAmount = roomData.betAmount ?? roomData.bet_amount ?? 0;
  const isPrivate = roomData.isPrivate ?? roomData.is_private ?? false;
  const createdAt = roomData.createdAt || roomData.created_at;
  const updatedAt = roomData.updatedAt || roomData.updated_at;

  return {
    id: updateData.id,
    name: updateData.name,
    gameType: (gameType === 'GAME_TYPE_PRIZE_WHEEL' ? 'PRIZEWHEEL' :
               gameType === 'GAME_TYPE_AMIDAKUJI' ? 'AMIDAKUJI' :
               gameType) as 'PRIZEWHEEL' | 'AMIDAKUJI',
    currentPlayerCount: playerCount,
    maxPlayers: maxPlayers,
    status: (updateData.status?.toUpperCase() === 'ROOM_STATUS_WAITING' ? 'WAITING' :
             updateData.status?.toUpperCase() === 'ROOM_STATUS_STARTING' ? 'STARTING' :
             updateData.status?.toUpperCase() === 'ROOM_STATUS_PLAYING' ? 'PLAYING' :
             updateData.status?.toUpperCase() === 'ROOM_STATUS_FINISHED' ? 'FINISHED' :
             updateData.status?.toUpperCase()) as 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED',
    betAmount: betAmount,
    currency: updateData.currency || 'USD',
    isPrivate: isPrivate,
    players: [], // Default empty array for players
    createdAt: createdAt,
    updatedAt: updatedAt,
    // Also create the nested config for backward compatibility
    config: {
      maxPlayers: maxPlayers,
      betAmount: betAmount,
      currency: updateData.currency || 'USD',
      gameDuration: 1800000, // Default 30 minutes
      gameSpecificConfig: {},
    },
  };
};

// Hook for components to easily listen to room list updates
export const useRoomListUpdates = (callback: (data: RoomListUpdateData) => void) => {
  React.useEffect(() => {
    const handleUpdate = (event: CustomEvent<RoomListUpdateData>) => {
      callback(event.detail);
    };

    window.addEventListener('roomListUpdate', handleUpdate as EventListener);

    return () => {
      window.removeEventListener('roomListUpdate', handleUpdate as EventListener);
    };
  }, [callback]);
};

// Export singleton for direct access
export default useLobbyStore;
