import toast from 'react-hot-toast';

// Error codes specific to Prize Wheel backend features
export enum PrizeWheelErrorCode {
  // Balance and Entry Fee Errors
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  BALANCE_VALIDATION_FAILED = 'BALANC<PERSON>_VALIDATION_FAILED',
  ENTRY_FEE_PROCESSING_FAILED = 'ENTRY_FEE_PROCESSING_FAILED',
  ENTRY_FEE_ALREADY_PAID = 'ENTRY_FEE_ALREADY_PAID',
  REFUND_PROCESSING_FAILED = 'REFUND_PROCESSING_FAILED',
  PAYMENT_STATUS_CHECK_FAILED = 'PAYMENT_STATUS_CHECK_FAILED',

  // Prize Pool Errors
  PRIZE_POOL_NOT_FOUND = 'PRIZE_POOL_NOT_FOUND',
  PRIZE_POOL_LOCKED = 'PRIZE_POOL_LOCKED',
  PRIZE_POOL_ALREADY_DISTRIBUTED = 'PRIZE_POOL_ALREADY_DISTRIBUTED',
  PRIZE_DISTRIBUTION_FAILED = 'PRIZE_DISTRIBUTION_FAILED',
  POTENTIAL_WINNINGS_CALCULATION_FAILED = 'POTENTIAL_WINNINGS_CALCULATION_FAILED',

  // Transaction Errors
  TRANSACTION_CREATION_FAILED = 'TRANSACTION_CREATION_FAILED',
  TRANSACTION_UPDATE_FAILED = 'TRANSACTION_UPDATE_FAILED',
  TRANSACTION_NOT_FOUND = 'TRANSACTION_NOT_FOUND',
  TRANSACTION_ALREADY_PROCESSED = 'TRANSACTION_ALREADY_PROCESSED',

  // Room and Player Errors
  ROOM_NOT_FOUND = 'ROOM_NOT_FOUND',
  PLAYER_NOT_IN_ROOM = 'PLAYER_NOT_IN_ROOM',
  ROOM_FULL = 'ROOM_FULL',
  ROOM_NOT_ACCEPTING_PLAYERS = 'ROOM_NOT_ACCEPTING_PLAYERS',
  PLAYER_KICK_FAILED = 'PLAYER_KICK_FAILED',

  // API and Network Errors
  API_TIMEOUT = 'API_TIMEOUT',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SERVER_ERROR = 'SERVER_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',

  // Generic Errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export interface PrizeWheelError {
  code: PrizeWheelErrorCode;
  message: string;
  details?: {
    currentBalance?: number;
    requiredAmount?: number;
    shortfall?: number;
    transactionId?: string;
    roomId?: string;
    userId?: string;
    prizePoolId?: string;
    [key: string]: any;
  };
  timestamp: string;
  context?: string;
}

export class PrizeWheelErrorHandler {
  private static instance: PrizeWheelErrorHandler;

  public static getInstance(): PrizeWheelErrorHandler {
    if (!PrizeWheelErrorHandler.instance) {
      PrizeWheelErrorHandler.instance = new PrizeWheelErrorHandler();
    }
    return PrizeWheelErrorHandler.instance;
  }

  /**
   * Handle API errors and convert them to PrizeWheelError format
   */
  public handleApiError(error: any, context?: string): PrizeWheelError {
    const prizeWheelError: PrizeWheelError = {
      code: PrizeWheelErrorCode.UNKNOWN_ERROR,
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
      context,
    };

    // Handle different error formats
    if (error?.response?.data) {
      // Axios error format
      const responseData = error.response.data;
      prizeWheelError.code = this.mapErrorCode(responseData.error_code || responseData.code);
      prizeWheelError.message = responseData.error_message || responseData.message || prizeWheelError.message;
      prizeWheelError.details = responseData.details || responseData.data;
    } else if (error?.code) {
      // Direct error object
      prizeWheelError.code = this.mapErrorCode(error.code);
      prizeWheelError.message = error.message || prizeWheelError.message;
      prizeWheelError.details = error.details;
    } else if (error?.message) {
      // Generic error with message
      prizeWheelError.message = error.message;
      prizeWheelError.code = this.inferErrorCode(error.message);
    }

    return prizeWheelError;
  }

  /**
   * Map backend error codes to PrizeWheelErrorCode enum
   */
  private mapErrorCode(code: string): PrizeWheelErrorCode {
    const codeMap: Record<string, PrizeWheelErrorCode> = {
      'INSUFFICIENT_BALANCE': PrizeWheelErrorCode.INSUFFICIENT_BALANCE,
      'BALANCE_VALIDATION_FAILED': PrizeWheelErrorCode.BALANCE_VALIDATION_FAILED,
      'ENTRY_FEE_PROCESSING_FAILED': PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED,
      'ENTRY_FEE_ALREADY_PAID': PrizeWheelErrorCode.ENTRY_FEE_ALREADY_PAID,
      'REFUND_PROCESSING_FAILED': PrizeWheelErrorCode.REFUND_PROCESSING_FAILED,
      'PAYMENT_STATUS_CHECK_FAILED': PrizeWheelErrorCode.PAYMENT_STATUS_CHECK_FAILED,
      'PRIZE_POOL_NOT_FOUND': PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND,
      'PRIZE_POOL_LOCKED': PrizeWheelErrorCode.PRIZE_POOL_LOCKED,
      'PRIZE_POOL_ALREADY_DISTRIBUTED': PrizeWheelErrorCode.PRIZE_POOL_ALREADY_DISTRIBUTED,
      'PRIZE_DISTRIBUTION_FAILED': PrizeWheelErrorCode.PRIZE_DISTRIBUTION_FAILED,
      'POTENTIAL_WINNINGS_CALCULATION_FAILED': PrizeWheelErrorCode.POTENTIAL_WINNINGS_CALCULATION_FAILED,
      'TRANSACTION_CREATION_FAILED': PrizeWheelErrorCode.TRANSACTION_CREATION_FAILED,
      'TRANSACTION_UPDATE_FAILED': PrizeWheelErrorCode.TRANSACTION_UPDATE_FAILED,
      'TRANSACTION_NOT_FOUND': PrizeWheelErrorCode.TRANSACTION_NOT_FOUND,
      'TRANSACTION_ALREADY_PROCESSED': PrizeWheelErrorCode.TRANSACTION_ALREADY_PROCESSED,
      'ROOM_NOT_FOUND': PrizeWheelErrorCode.ROOM_NOT_FOUND,
      'PLAYER_NOT_IN_ROOM': PrizeWheelErrorCode.PLAYER_NOT_IN_ROOM,
      'ROOM_FULL': PrizeWheelErrorCode.ROOM_FULL,
      'ROOM_NOT_ACCEPTING_PLAYERS': PrizeWheelErrorCode.ROOM_NOT_ACCEPTING_PLAYERS,
      'PLAYER_KICK_FAILED': PrizeWheelErrorCode.PLAYER_KICK_FAILED,
      'API_TIMEOUT': PrizeWheelErrorCode.API_TIMEOUT,
      'NETWORK_ERROR': PrizeWheelErrorCode.NETWORK_ERROR,
      'SERVER_ERROR': PrizeWheelErrorCode.SERVER_ERROR,
      'VALIDATION_ERROR': PrizeWheelErrorCode.VALIDATION_ERROR,
      'UNAUTHORIZED': PrizeWheelErrorCode.UNAUTHORIZED,
      'FORBIDDEN': PrizeWheelErrorCode.FORBIDDEN,
    };

    return codeMap[code] || PrizeWheelErrorCode.UNKNOWN_ERROR;
  }

  /**
   * Infer error code from error message
   */
  private inferErrorCode(message: string): PrizeWheelErrorCode {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('insufficient balance') || lowerMessage.includes('not enough')) {
      return PrizeWheelErrorCode.INSUFFICIENT_BALANCE;
    }
    if (lowerMessage.includes('timeout') || lowerMessage.includes('timed out')) {
      return PrizeWheelErrorCode.API_TIMEOUT;
    }
    if (lowerMessage.includes('network') || lowerMessage.includes('connection')) {
      return PrizeWheelErrorCode.NETWORK_ERROR;
    }
    if (lowerMessage.includes('unauthorized') || lowerMessage.includes('not authorized')) {
      return PrizeWheelErrorCode.UNAUTHORIZED;
    }
    if (lowerMessage.includes('forbidden') || lowerMessage.includes('access denied')) {
      return PrizeWheelErrorCode.FORBIDDEN;
    }
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid')) {
      return PrizeWheelErrorCode.VALIDATION_ERROR;
    }
    if (lowerMessage.includes('room not found') || lowerMessage.includes('room does not exist')) {
      return PrizeWheelErrorCode.ROOM_NOT_FOUND;
    }
    if (lowerMessage.includes('prize pool') && lowerMessage.includes('not found')) {
      return PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND;
    }

    return PrizeWheelErrorCode.UNKNOWN_ERROR;
  }

  /**
   * Get user-friendly error message
   */
  public getUserFriendlyMessage(error: PrizeWheelError): string {
    switch (error.code) {
      case PrizeWheelErrorCode.INSUFFICIENT_BALANCE:
        if (error.details?.currentBalance !== undefined && error.details?.requiredAmount !== undefined) {
          const current = (error.details.currentBalance / 100).toFixed(2);
          const required = (error.details.requiredAmount / 100).toFixed(2);
          const shortfall = ((error.details.requiredAmount - error.details.currentBalance) / 100).toFixed(2);
          return `Insufficient balance. You need $${required} but only have $${current} (short by $${shortfall})`;
        }
        return 'You don\'t have enough balance to complete this action';

      case PrizeWheelErrorCode.BALANCE_VALIDATION_FAILED:
        return 'Unable to verify your balance. Please try again or contact support';

      case PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED:
        return 'Failed to process entry fee. Please check your balance and try again';

      case PrizeWheelErrorCode.ENTRY_FEE_ALREADY_PAID:
        return 'Entry fee has already been paid for this room';

      case PrizeWheelErrorCode.REFUND_PROCESSING_FAILED:
        return 'Failed to process refund. Please contact support if this persists';

      case PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND:
        return 'Prize pool not found. The room may have been closed';

      case PrizeWheelErrorCode.PRIZE_POOL_LOCKED:
        return 'Prize pool is locked and cannot be modified';

      case PrizeWheelErrorCode.PRIZE_POOL_ALREADY_DISTRIBUTED:
        return 'Prizes have already been distributed for this game';

      case PrizeWheelErrorCode.PRIZE_DISTRIBUTION_FAILED:
        return 'Failed to distribute prizes. Please contact support';

      case PrizeWheelErrorCode.ROOM_NOT_FOUND:
        return 'Room not found. It may have been closed or deleted';

      case PrizeWheelErrorCode.PLAYER_NOT_IN_ROOM:
        return 'You are not in this room. Please join the room first';

      case PrizeWheelErrorCode.ROOM_FULL:
        return 'Room is full. Please try another room';

      case PrizeWheelErrorCode.ROOM_NOT_ACCEPTING_PLAYERS:
        return 'Room is not accepting new players at this time';

      case PrizeWheelErrorCode.API_TIMEOUT:
        return 'Request timed out. Please check your connection and try again';

      case PrizeWheelErrorCode.NETWORK_ERROR:
        return 'Network error. Please check your internet connection';

      case PrizeWheelErrorCode.UNAUTHORIZED:
        return 'You are not authorized to perform this action. Please log in again';

      case PrizeWheelErrorCode.FORBIDDEN:
        return 'Access denied. You don\'t have permission to perform this action';

      case PrizeWheelErrorCode.VALIDATION_ERROR:
        return error.message || 'Invalid input. Please check your data and try again';

      default:
        return error.message || 'An unexpected error occurred. Please try again';
    }
  }

  /**
   * Show error toast with appropriate styling
   */
  public showErrorToast(error: PrizeWheelError): void {
    const message = this.getUserFriendlyMessage(error);
    const duration = this.getToastDuration(error.code);
    const icon = this.getErrorIcon(error.code);

    toast.error(message, {
      duration,
      icon,
      style: {
        maxWidth: '500px',
      },
    });
  }

  /**
   * Get appropriate toast duration based on error severity
   */
  private getToastDuration(code: PrizeWheelErrorCode): number {
    const longDuration = 8000;
    const mediumDuration = 5000;
    const shortDuration = 3000;

    switch (code) {
      case PrizeWheelErrorCode.INSUFFICIENT_BALANCE:
      case PrizeWheelErrorCode.BALANCE_VALIDATION_FAILED:
      case PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED:
      case PrizeWheelErrorCode.PRIZE_DISTRIBUTION_FAILED:
        return longDuration;

      case PrizeWheelErrorCode.UNAUTHORIZED:
      case PrizeWheelErrorCode.FORBIDDEN:
      case PrizeWheelErrorCode.ROOM_NOT_FOUND:
      case PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND:
        return mediumDuration;

      default:
        return shortDuration;
    }
  }

  /**
   * Get appropriate icon for error type
   */
  private getErrorIcon(code: PrizeWheelErrorCode): string {
    switch (code) {
      case PrizeWheelErrorCode.INSUFFICIENT_BALANCE:
        return '💰';
      case PrizeWheelErrorCode.NETWORK_ERROR:
      case PrizeWheelErrorCode.API_TIMEOUT:
        return '🌐';
      case PrizeWheelErrorCode.UNAUTHORIZED:
      case PrizeWheelErrorCode.FORBIDDEN:
        return '🔒';
      case PrizeWheelErrorCode.VALIDATION_ERROR:
        return '⚠️';
      case PrizeWheelErrorCode.PRIZE_DISTRIBUTION_FAILED:
        return '🏆';
      default:
        return '❌';
    }
  }

  /**
   * Log error for debugging and monitoring
   */
  public logError(error: PrizeWheelError): void {
    console.error('Prize Wheel Error:', {
      code: error.code,
      message: error.message,
      details: error.details,
      timestamp: error.timestamp,
      context: error.context,
    });

    // In production, you might want to send this to an error tracking service
    // like Sentry, LogRocket, or your own logging service
  }
}

// Export singleton instance
export const prizeWheelErrorHandler = PrizeWheelErrorHandler.getInstance();
