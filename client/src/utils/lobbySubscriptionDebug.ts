/**
 * Debug utilities for lobby subscription management
 */

import { useLobbyStore } from '@/store/lobbyStore';

export const logLobbySubscriptionState = (context: string) => {
  const state = useLobbyStore.getState();
  console.log(`[${context}] Lobby Subscription State:`, {
    isSubscribed: state.isSubscribed,
    subscriptionLoading: state.subscriptionLoading,
    subscriptionError: state.subscriptionError,
    updateCount: state.updateCount,
    timestamp: new Date().toISOString(),
  });
};

export const logLobbySubscriptionAttempt = (context: string, action: 'subscribe' | 'unsubscribe') => {
  const state = useLobbyStore.getState();
  console.log(`[${context}] Attempting to ${action} lobby:`, {
    currentlySubscribed: state.isSubscribed,
    loading: state.subscriptionLoading,
    action,
    timestamp: new Date().toISOString(),
  });
};

export const createLobbySubscriptionGuard = (context: string) => {
  return {
    canSubscribe: () => {
      const state = useLobbyStore.getState();
      const canSubscribe = !state.isSubscribed && !state.subscriptionLoading;
      
      if (!canSubscribe) {
        console.log(`[${context}] Subscription blocked:`, {
          isSubscribed: state.isSubscribed,
          subscriptionLoading: state.subscriptionLoading,
          reason: state.isSubscribed ? 'already_subscribed' : 'subscription_in_progress',
        });
      }
      
      return canSubscribe;
    },
    
    canUnsubscribe: () => {
      const state = useLobbyStore.getState();
      const canUnsubscribe = state.isSubscribed && !state.subscriptionLoading;
      
      if (!canUnsubscribe) {
        console.log(`[${context}] Unsubscription blocked:`, {
          isSubscribed: state.isSubscribed,
          subscriptionLoading: state.subscriptionLoading,
          reason: !state.isSubscribed ? 'not_subscribed' : 'subscription_in_progress',
        });
      }
      
      return canUnsubscribe;
    },
  };
};
