/**
 * Comprehensive Room Subscription Management System
 * Handles lobby/room subscription transitions and prevents duplicate operations
 */

interface SubscriptionState {
  currentChannel: 'lobby' | 'room' | null;
  roomId: string | null;
  isTransitioning: boolean;
  lastTransition: number;
}

interface SubscriptionOperation {
  type: 'subscribe_lobby' | 'unsubscribe_lobby' | 'subscribe_room' | 'unsubscribe_room' | 'switch_to_room' | 'switch_to_lobby';
  roomId?: string;
  timestamp: number;
  promise: Promise<void>;
}

class SubscriptionManager {
  private state: SubscriptionState = {
    currentChannel: null,
    roomId: null,
    isTransitioning: false,
    lastTransition: 0,
  };

  private activeOperations = new Map<string, SubscriptionOperation>();
  // private readonly DEBOUNCE_TIME = 1000; // 1 second debounce
  // private readonly TRANSITION_TIMEOUT = 5000; // 5 second timeout

  /**
   * Handles room join subscription flow:
   * 1. Unsubscribe from lobby
   * 2. Subscribe to room channel
   */
  async handleRoomJoin(
    roomId: string,
    joinFunction: () => Promise<void>,
    socketService: any
  ): Promise<void> {
    const operationKey = `join_${roomId}`;
    
    // Check for duplicate operations
    if (this.activeOperations.has(operationKey)) {
      console.warn(`Duplicate room join operation for ${roomId} ignored`);
      return this.activeOperations.get(operationKey)!.promise;
    }

    // Check if already in this room
    if (this.state.currentChannel === 'room' && this.state.roomId === roomId) {
      console.log(`Already subscribed to room ${roomId}`);
      return Promise.resolve();
    }

    const joinPromise = this.executeRoomJoin(roomId, joinFunction, socketService);
    
    this.activeOperations.set(operationKey, {
      type: 'switch_to_room',
      roomId,
      timestamp: Date.now(),
      promise: joinPromise,
    });

    try {
      await joinPromise;
    } finally {
      this.activeOperations.delete(operationKey);
    }
  }

  /**
   * Handles room leave subscription flow:
   * 1. Unsubscribe from room channel
   * 2. Subscribe to lobby
   */
  async handleRoomLeave(
    roomId: string,
    leaveFunction: () => Promise<void>,
    socketService: any
  ): Promise<void> {
    const operationKey = `leave_${roomId}`;
    
    // Check for duplicate operations
    if (this.activeOperations.has(operationKey)) {
      console.warn(`Duplicate room leave operation for ${roomId} ignored`);
      return this.activeOperations.get(operationKey)!.promise;
    }

    const leavePromise = this.executeRoomLeave(roomId, leaveFunction, socketService);
    
    this.activeOperations.set(operationKey, {
      type: 'switch_to_lobby',
      roomId,
      timestamp: Date.now(),
      promise: leavePromise,
    });

    try {
      await leavePromise;
    } finally {
      this.activeOperations.delete(operationKey);
    }
  }

  /**
   * Execute room join with proper subscription management
   */
  private async executeRoomJoin(
    roomId: string,
    joinFunction: () => Promise<void>,
    socketService: any
  ): Promise<void> {
    this.state.isTransitioning = true;
    this.state.lastTransition = Date.now();

    try {
      console.log(`Starting room join subscription flow for room: ${roomId}`);

      // Step 1: Unsubscribe from lobby if currently subscribed
      if (this.state.currentChannel === 'lobby') {
        console.log('Unsubscribing from lobby before room join');
        try {
          await socketService.unsubscribeLobby();
          this.state.currentChannel = null;
        } catch (error) {
          console.warn('Failed to unsubscribe from lobby:', error);
          // Continue with join attempt
        }
      }

      // Step 2: Execute the actual room join
      console.log(`Executing room join for: ${roomId}`);
      await joinFunction();

      // Step 3: Update subscription state on successful join
      this.state.currentChannel = 'room';
      this.state.roomId = roomId;
      
      console.log(`Successfully joined room and updated subscription state: ${roomId}`);

    } catch (error) {
      console.error(`Room join failed for ${roomId}:`, error);
      
      // Rollback: Re-subscribe to lobby on failure
      await this.rollbackToLobby(socketService);
      throw error;
    } finally {
      this.state.isTransitioning = false;
    }
  }

  /**
   * Execute room leave with proper subscription management
   */
  private async executeRoomLeave(
    roomId: string,
    leaveFunction: () => Promise<void>,
    socketService: any
  ): Promise<void> {
    this.state.isTransitioning = true;
    this.state.lastTransition = Date.now();

    try {
      console.log(`Starting room leave subscription flow for room: ${roomId}`);

      // Step 1: Execute the actual room leave
      await leaveFunction();

      // Step 2: Update subscription state
      this.state.currentChannel = null;
      this.state.roomId = null;

      // Step 3: Subscribe to lobby
      console.log('Subscribing to lobby after room leave');
      try {
        await socketService.subscribeLobby();
        this.state.currentChannel = 'lobby';
      } catch (error) {
        console.warn('Failed to subscribe to lobby after room leave:', error);
        // Don't throw - room leave was successful
      }

      console.log(`Successfully left room and updated subscription state: ${roomId}`);

    } catch (error) {
      console.error(`Room leave failed for ${roomId}:`, error);
      throw error;
    } finally {
      this.state.isTransitioning = false;
    }
  }

  /**
   * Rollback to lobby subscription on failed room operations
   */
  private async rollbackToLobby(socketService: any): Promise<void> {
    try {
      console.log('Rolling back to lobby subscription');
      await socketService.subscribeLobby();
      this.state.currentChannel = 'lobby';
      this.state.roomId = null;
    } catch (error) {
      console.error('Failed to rollback to lobby:', error);
      this.state.currentChannel = null;
      this.state.roomId = null;
    }
  }

  /**
   * Force subscribe to lobby (for initial connection)
   */
  async subscribeLobby(socketService: any): Promise<void> {
    if (this.state.isTransitioning) {
      console.warn('Cannot subscribe to lobby during transition');
      return;
    }

    if (this.state.currentChannel === 'lobby') {
      console.log('Already subscribed to lobby');
      return;
    }

    try {
      await socketService.subscribeLobby();
      this.state.currentChannel = 'lobby';
      this.state.roomId = null;
      console.log('Successfully subscribed to lobby');
    } catch (error) {
      console.error('Failed to subscribe to lobby:', error);
      throw error;
    }
  }

  /**
   * Handle disconnect - reset subscription state
   */
  handleDisconnect(): void {
    console.log('Handling disconnect - resetting subscription state');
    this.state = {
      currentChannel: null,
      roomId: null,
      isTransitioning: false,
      lastTransition: 0,
    };
    this.activeOperations.clear();
  }

  /**
   * Get current subscription state
   */
  getState(): Readonly<SubscriptionState> {
    return { ...this.state };
  }

  /**
   * Check if currently in a room
   */
  isInRoom(roomId?: string): boolean {
    if (roomId) {
      return this.state.currentChannel === 'room' && this.state.roomId === roomId;
    }
    return this.state.currentChannel === 'room';
  }

  /**
   * Check if currently in lobby
   */
  isInLobby(): boolean {
    return this.state.currentChannel === 'lobby';
  }

  /**
   * Check if transitioning between subscriptions
   */
  isTransitioning(): boolean {
    return this.state.isTransitioning;
  }
}

// Export singleton instance
export const subscriptionManager = new SubscriptionManager();
export default subscriptionManager;
