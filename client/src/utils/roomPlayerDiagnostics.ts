/**
 * Room Player Position Diagnostics
 * Helps debug player position and room state issues
 */

interface PlayerData {
  userId: string;
  username: string;
  position: number;
  isReady: boolean;
  betAmount: string;
  joinedAt: string;
  status: string;
}

interface RoomInfoData {
  roomId: string;
  name: string;
  gameType: string;
  status: string;
  currentPlayers: number;
  maxPlayers: number;
  betAmount: string;
  players: PlayerData[];
  isPrivate: boolean;
  autoStart: boolean;
  createdAt: string;
  lastActivity: string;
  canStart: boolean;
}

interface RoomInfoEvent {
  type: string;
  data: RoomInfoData;
  reason: string;
  timestamp: string;
}

class RoomPlayerDiagnostics {
  private roomHistory: Map<string, RoomInfoEvent[]> = new Map();
  private playerJoinHistory: Map<string, { username: string; joinedAt: string; position: number }[]> = new Map();

  /**
   * Track a room_info_updated event for diagnostics
   */
  trackRoomInfoEvent(event: RoomInfoEvent) {
    const roomId = event.data.roomId;
    
    // Initialize room history if not exists
    if (!this.roomHistory.has(roomId)) {
      this.roomHistory.set(roomId, []);
      this.playerJoinHistory.set(roomId, []);
    }

    // Add to room history
    const roomEvents = this.roomHistory.get(roomId)!;
    roomEvents.push(event);

    // Track player join sequence
    const playerHistory = this.playerJoinHistory.get(roomId)!;
    
    // Check for new players
    event.data.players.forEach(player => {
      const existingPlayer = playerHistory.find(p => p.username === player.username);
      if (!existingPlayer) {
        playerHistory.push({
          username: player.username,
          joinedAt: player.joinedAt,
          position: player.position
        });
      }
    });

    // Analyze for issues
    this.analyzeRoomState(event);
  }

  /**
   * Analyze room state for potential issues
   */
  private analyzeRoomState(event: RoomInfoEvent) {
    const { data } = event;
    const issues: string[] = [];

    // Check 1: Current players count vs actual players array length
    if (data.currentPlayers !== data.players.length) {
      issues.push(`❌ MISMATCH: currentPlayers (${data.currentPlayers}) != players.length (${data.players.length})`);
    }

    // Check 2: Position conflicts (multiple players with same position)
    const positions = data.players.map(p => p.position);
    const uniquePositions = new Set(positions);
    if (positions.length !== uniquePositions.size) {
      issues.push(`❌ POSITION CONFLICT: Multiple players have the same position`);
      const positionCounts = positions.reduce((acc, pos) => {
        acc[pos] = (acc[pos] || 0) + 1;
        return acc;
      }, {} as Record<number, number>);
      
      Object.entries(positionCounts).forEach(([pos, count]) => {
        if (count > 1) {
          const playersAtPosition = data.players.filter(p => p.position === parseInt(pos));
          issues.push(`  - Position ${pos}: ${count} players (${playersAtPosition.map(p => p.username).join(', ')})`);
        }
      });
    }

    // Check 3: Position sequence (should be 1, 2, 3, etc.)
    const sortedPositions = [...uniquePositions].sort((a, b) => a - b);
    const expectedPositions = Array.from({ length: data.players.length }, (_, i) => i + 1);
    if (JSON.stringify(sortedPositions) !== JSON.stringify(expectedPositions)) {
      issues.push(`❌ POSITION SEQUENCE: Expected [${expectedPositions.join(', ')}], got [${sortedPositions.join(', ')}]`);
    }

    // Check 4: Player replacement detection
    const roomHistory = this.roomHistory.get(data.roomId)!;
    if (roomHistory.length > 1) {
      const previousEvent = roomHistory[roomHistory.length - 2];
      const previousPlayers = previousEvent.data.players;
      const currentPlayers = data.players;

      // Check if any previous players are missing
      const missingPlayers = previousPlayers.filter(prevPlayer => 
        !currentPlayers.some(currPlayer => currPlayer.userId === prevPlayer.userId)
      );

      if (missingPlayers.length > 0 && event.reason === 'player_subscribed') {
        issues.push(`❌ PLAYER REPLACEMENT: Previous players missing when new player joined:`);
        missingPlayers.forEach(player => {
          issues.push(`  - Lost: ${player.username} (${player.userId}) at position ${player.position}`);
        });
      }
    }

    // Log issues if any found
    if (issues.length > 0) {
      console.error(`🚨 Room State Issues Detected (${event.reason}):`, {
        roomId: data.roomId,
        roomName: data.name,
        timestamp: event.timestamp,
        issues,
        currentState: {
          currentPlayers: data.currentPlayers,
          actualPlayers: data.players.length,
          players: data.players.map(p => ({
            username: p.username,
            position: p.position,
            userId: p.userId.slice(-8) // Last 8 chars for privacy
          }))
        }
      });
    } else {
      console.log(`✅ Room State OK (${event.reason}):`, {
        roomId: data.roomId,
        roomName: data.name,
        currentPlayers: data.currentPlayers,
        players: data.players.map(p => `${p.username}@pos${p.position}`)
      });
    }
  }

  /**
   * Get diagnostic report for a room
   */
  getDiagnosticReport(roomId: string) {
    const roomEvents = this.roomHistory.get(roomId) || [];
    const playerHistory = this.playerJoinHistory.get(roomId) || [];

    if (roomEvents.length === 0) {
      return { error: 'No events tracked for this room' };
    }

    const latestEvent = roomEvents[roomEvents.length - 1];
    
    return {
      roomId,
      roomName: latestEvent.data.name,
      totalEvents: roomEvents.length,
      currentState: {
        currentPlayers: latestEvent.data.currentPlayers,
        actualPlayers: latestEvent.data.players.length,
        maxPlayers: latestEvent.data.maxPlayers,
        players: latestEvent.data.players.map(p => ({
          username: p.username,
          position: p.position,
          isReady: p.isReady,
          joinedAt: p.joinedAt
        }))
      },
      playerJoinSequence: playerHistory.map((p, index) => ({
        order: index + 1,
        username: p.username,
        assignedPosition: p.position,
        joinedAt: p.joinedAt
      })),
      eventHistory: roomEvents.map(event => ({
        timestamp: event.timestamp,
        reason: event.reason,
        currentPlayers: event.data.currentPlayers,
        playersCount: event.data.players.length,
        players: event.data.players.map(p => `${p.username}@pos${p.position}`)
      }))
    };
  }

  /**
   * Clear diagnostics for a room
   */
  clearRoomDiagnostics(roomId: string) {
    this.roomHistory.delete(roomId);
    this.playerJoinHistory.delete(roomId);
  }

  /**
   * Get summary of all tracked rooms
   */
  getAllRoomsSummary() {
    const summary: any[] = [];
    
    this.roomHistory.forEach((events, roomId) => {
      const latestEvent = events[events.length - 1];
      const playerHistory = this.playerJoinHistory.get(roomId) || [];
      
      summary.push({
        roomId,
        roomName: latestEvent.data.name,
        totalEvents: events.length,
        totalPlayersJoined: playerHistory.length,
        currentPlayers: latestEvent.data.currentPlayers,
        actualPlayers: latestEvent.data.players.length,
        hasIssues: latestEvent.data.currentPlayers !== latestEvent.data.players.length
      });
    });

    return summary;
  }
}

// Create global instance
export const roomPlayerDiagnostics = new RoomPlayerDiagnostics();

/**
 * Helper function to track room_info_updated events
 */
export function trackRoomInfoUpdate(event: any) {
  if (event.type === 'room_info_updated' && event.data) {
    roomPlayerDiagnostics.trackRoomInfoEvent(event);
  }
}

/**
 * Helper function to log diagnostic report
 */
export function logRoomDiagnostics(roomId: string) {
  const report = roomPlayerDiagnostics.getDiagnosticReport(roomId);
  console.log('🔍 Room Diagnostics Report:', report);
  return report;
}

/**
 * Helper function to analyze the specific issue you mentioned
 */
export function analyzePlayerReplacementIssue() {
  console.log('🔍 Analyzing Player Replacement Issue...\n');
  
  // Simulate the events you provided
  const res2JoinEvent = {
    type: "room_info_updated",
    data: {
      roomId: "68412c9af494b684c1c18ecf",
      name: "czxcwee2",
      gameType: "prizewheel",
      status: "waiting",
      currentPlayers: 1,
      maxPlayers: 2,
      betAmount: "10.0",
      players: [{
        userId: "683b07882d7dbd11e92bf29d",
        username: "res2",
        position: 1,
        isReady: false,
        betAmount: "10.0",
        joinedAt: "2025-06-13T06:52:55.209Z",
        status: "active"
      }],
      isPrivate: false,
      autoStart: true,
      createdAt: "2025-06-05T05:35:22.488Z",
      lastActivity: "2025-06-11T18:22:12.456Z",
      canStart: false
    },
    reason: "player_subscribed",
    timestamp: "2025-06-13T06:52:55.210Z"
  };

  const resJoinEvent = {
    type: "room_info_updated",
    data: {
      roomId: "68412c9af494b684c1c18ecf",
      name: "czxcwee2",
      gameType: "prizewheel",
      status: "waiting",
      currentPlayers: 1, // ❌ Should be 2
      maxPlayers: 2,
      betAmount: "10.0",
      players: [{
        userId: "68334427b8ef34dc195f27bd",
        username: "res",
        position: 1, // ❌ Should be 2, and res2 should still be here
        isReady: false,
        betAmount: "10.0",
        joinedAt: "2025-06-13T06:55:09.916Z",
        status: "active"
      }],
      isPrivate: false,
      autoStart: true,
      createdAt: "2025-06-05T05:35:22.488Z",
      lastActivity: "2025-06-11T18:22:12.456Z",
      canStart: false
    },
    reason: "player_subscribed",
    timestamp: "2025-06-13T06:55:09.917Z"
  };

  // Track both events
  roomPlayerDiagnostics.trackRoomInfoEvent(res2JoinEvent);
  roomPlayerDiagnostics.trackRoomInfoEvent(resJoinEvent);

  // Get diagnostic report
  const report = roomPlayerDiagnostics.getDiagnosticReport("68412c9af494b684c1c18ecf");
  
  console.log('📊 Analysis Results:');
  console.log('==================');
  console.log('Expected after res joins:');
  console.log('  - currentPlayers: 2');
  console.log('  - players: [res2@pos1, res@pos2]');
  console.log('\nActual after res joins:');
  console.log('  - currentPlayers: 1 ❌');
  console.log('  - players: [res@pos1] ❌ (res2 missing)');
  console.log('\n🚨 Backend Issues Identified:');
  console.log('1. Player count not incrementing correctly');
  console.log('2. Previous player (res2) being replaced instead of adding new player');
  console.log('3. Position assignment not finding next available slot');
  console.log('4. Room state not maintaining existing players when new player joins');
  
  return report;
}

export default roomPlayerDiagnostics;
