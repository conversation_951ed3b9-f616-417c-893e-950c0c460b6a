import { describe, it, expect, vi, beforeEach } from 'vitest';
import { PrizeWheelError<PERSON><PERSON><PERSON>, PrizeWheelErrorCode } from '../prizeWheelErrorHandler';
import toast from 'react-hot-toast';

// Mock react-hot-toast
vi.mock('react-hot-toast', () => ({
  default: {
    error: vi.fn(),
  },
}));

describe('PrizeWheelErrorHandler', () => {
  let errorHandler: PrizeWheelErrorHandler;

  beforeEach(() => {
    vi.clearAllMocks();
    errorHandler = PrizeWheelErrorHandler.getInstance();
  });

  describe('handleApiError', () => {
    it('handles axios error format', () => {
      const axiosError = {
        response: {
          data: {
            error_code: 'INSUFFICIENT_BALANCE',
            error_message: 'Not enough balance',
            details: {
              currentBalance: 1000,
              requiredAmount: 2000,
              shortfall: 1000,
            },
          },
        },
      };

      const result = errorHandler.handleApiError(axiosError, 'test_context');

      expect(result.code).toBe(PrizeWheelErrorCode.INSUFFICIENT_BALANCE);
      expect(result.message).toBe('Not enough balance');
      expect(result.details).toEqual({
        currentBalance: 1000,
        requiredAmount: 2000,
        shortfall: 1000,
      });
      expect(result.context).toBe('test_context');
    });

    it('handles direct error object', () => {
      const directError = {
        code: 'PRIZE_POOL_NOT_FOUND',
        message: 'Prize pool does not exist',
        details: { prizePoolId: 'pool-123' },
      };

      const result = errorHandler.handleApiError(directError);

      expect(result.code).toBe(PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND);
      expect(result.message).toBe('Prize pool does not exist');
      expect(result.details).toEqual({ prizePoolId: 'pool-123' });
    });

    it('handles generic error with message', () => {
      const genericError = new Error('Network connection failed');

      const result = errorHandler.handleApiError(genericError);

      expect(result.code).toBe(PrizeWheelErrorCode.NETWORK_ERROR);
      expect(result.message).toBe('Network connection failed');
    });

    it('handles unknown error format', () => {
      const unknownError = { someProperty: 'someValue' };

      const result = errorHandler.handleApiError(unknownError);

      expect(result.code).toBe(PrizeWheelErrorCode.UNKNOWN_ERROR);
      expect(result.message).toBe('An unexpected error occurred');
    });
  });

  describe('getUserFriendlyMessage', () => {
    it('returns specific message for insufficient balance with details', () => {
      const error = {
        code: PrizeWheelErrorCode.INSUFFICIENT_BALANCE,
        message: 'Insufficient balance',
        details: {
          currentBalance: 1000, // $10.00 in cents
          requiredAmount: 2000, // $20.00 in cents
        },
        timestamp: new Date().toISOString(),
      };

      const message = errorHandler.getUserFriendlyMessage(error);

      expect(message).toBe('Insufficient balance. You need $20.00 but only have $10.00 (short by $10.00)');
    });

    it('returns generic message for insufficient balance without details', () => {
      const error = {
        code: PrizeWheelErrorCode.INSUFFICIENT_BALANCE,
        message: 'Insufficient balance',
        timestamp: new Date().toISOString(),
      };

      const message = errorHandler.getUserFriendlyMessage(error);

      expect(message).toBe('You don\'t have enough balance to complete this action');
    });

    it('returns appropriate message for entry fee errors', () => {
      const error = {
        code: PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED,
        message: 'Entry fee failed',
        timestamp: new Date().toISOString(),
      };

      const message = errorHandler.getUserFriendlyMessage(error);

      expect(message).toBe('Failed to process entry fee. Please check your balance and try again');
    });

    it('returns appropriate message for room errors', () => {
      const error = {
        code: PrizeWheelErrorCode.ROOM_NOT_FOUND,
        message: 'Room not found',
        timestamp: new Date().toISOString(),
      };

      const message = errorHandler.getUserFriendlyMessage(error);

      expect(message).toBe('Room not found. It may have been closed or deleted');
    });

    it('returns original message for validation errors', () => {
      const error = {
        code: PrizeWheelErrorCode.VALIDATION_ERROR,
        message: 'Invalid input data',
        timestamp: new Date().toISOString(),
      };

      const message = errorHandler.getUserFriendlyMessage(error);

      expect(message).toBe('Invalid input data');
    });

    it('returns default message for unknown errors', () => {
      const error = {
        code: PrizeWheelErrorCode.UNKNOWN_ERROR,
        message: 'Something went wrong',
        timestamp: new Date().toISOString(),
      };

      const message = errorHandler.getUserFriendlyMessage(error);

      expect(message).toBe('Something went wrong');
    });
  });

  describe('showErrorToast', () => {
    it('shows error toast with correct message and duration', () => {
      const error = {
        code: PrizeWheelErrorCode.INSUFFICIENT_BALANCE,
        message: 'Insufficient balance',
        timestamp: new Date().toISOString(),
      };

      errorHandler.showErrorToast(error);

      expect(toast.error).toHaveBeenCalledWith(
        'You don\'t have enough balance to complete this action',
        expect.objectContaining({
          duration: 8000,
          icon: '💰',
          style: { maxWidth: '500px' },
        })
      );
    });

    it('uses shorter duration for less critical errors', () => {
      const error = {
        code: PrizeWheelErrorCode.ROOM_FULL,
        message: 'Room is full',
        timestamp: new Date().toISOString(),
      };

      errorHandler.showErrorToast(error);

      expect(toast.error).toHaveBeenCalledWith(
        'Room is full. Please try another room',
        expect.objectContaining({
          duration: 3000,
        })
      );
    });
  });

  describe('error code mapping', () => {
    it('maps backend error codes correctly', () => {
      const testCases = [
        { input: 'INSUFFICIENT_BALANCE', expected: PrizeWheelErrorCode.INSUFFICIENT_BALANCE },
        { input: 'PRIZE_POOL_NOT_FOUND', expected: PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND },
        { input: 'ENTRY_FEE_PROCESSING_FAILED', expected: PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED },
        { input: 'ROOM_NOT_FOUND', expected: PrizeWheelErrorCode.ROOM_NOT_FOUND },
        { input: 'UNAUTHORIZED', expected: PrizeWheelErrorCode.UNAUTHORIZED },
        { input: 'UNKNOWN_CODE', expected: PrizeWheelErrorCode.UNKNOWN_ERROR },
      ];

      testCases.forEach(({ input, expected }) => {
        const error = { code: input, message: 'Test message' };
        const result = errorHandler.handleApiError(error);
        expect(result.code).toBe(expected);
      });
    });
  });

  describe('error inference from message', () => {
    it('infers error codes from message content', () => {
      const testCases = [
        { message: 'insufficient balance detected', expected: PrizeWheelErrorCode.INSUFFICIENT_BALANCE },
        { message: 'request timed out', expected: PrizeWheelErrorCode.API_TIMEOUT },
        { message: 'network connection failed', expected: PrizeWheelErrorCode.NETWORK_ERROR },
        { message: 'user not authorized', expected: PrizeWheelErrorCode.UNAUTHORIZED },
        { message: 'access forbidden', expected: PrizeWheelErrorCode.FORBIDDEN },
        { message: 'validation failed', expected: PrizeWheelErrorCode.VALIDATION_ERROR },
        { message: 'room not found', expected: PrizeWheelErrorCode.ROOM_NOT_FOUND },
        { message: 'prize pool not found', expected: PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND },
        { message: 'random error message', expected: PrizeWheelErrorCode.UNKNOWN_ERROR },
      ];

      testCases.forEach(({ message, expected }) => {
        const error = new Error(message);
        const result = errorHandler.handleApiError(error);
        expect(result.code).toBe(expected);
      });
    });
  });

  describe('singleton pattern', () => {
    it('returns the same instance', () => {
      const instance1 = PrizeWheelErrorHandler.getInstance();
      const instance2 = PrizeWheelErrorHandler.getInstance();

      expect(instance1).toBe(instance2);
    });
  });

  describe('logError', () => {
    it('logs error to console', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      const error = {
        code: PrizeWheelErrorCode.INSUFFICIENT_BALANCE,
        message: 'Test error',
        timestamp: new Date().toISOString(),
      };

      errorHandler.logError(error);

      expect(consoleSpy).toHaveBeenCalledWith('Prize Wheel Error:', {
        code: error.code,
        message: error.message,
        details: undefined,
        timestamp: error.timestamp,
        context: undefined,
      });

      consoleSpy.mockRestore();
    });
  });
});
