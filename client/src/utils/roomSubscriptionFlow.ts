/**
 * Enhanced Room Subscription Management System
 * Handles client-side room subscription management with automatic lobby fallback
 *
 * Key Features:
 * - Automatic lobby re-subscription on room join failure
 * - Room-specific event subscription on join success
 * - Real-time room info updates for all room participants
 * - Clean subscription state management
 */

import { useLobbyStore } from '@/store/lobbyStore';
import { useRoomSubscriptionStore } from '@/store/roomSubscriptionStore';
import { useSocketStore } from '@/store/socketStore';
import { env } from '@/config/env';
import toast from 'react-hot-toast';

interface SubscriptionFlowState {
  wasSubscribedToLobby: boolean;
  roomId: string | null;
  operationInProgress: boolean;
  joinStartTime: number;
  retryCount: number;
  subscriptionType: 'lobby' | 'room' | null;
}

interface RoomSubscriptionOptions {
  roomId: string;
  autoLobbyFallback?: boolean;
  maxRetries?: number;
  onSubscriptionChange?: (type: 'lobby' | 'room' | null, roomId?: string) => void;
}

interface JoinRoomFlowOptions {
  roomId: string;
  password?: string;
  betAmount?: number;
  joinFunction: () => Promise<void>;
  onPreJoin?: () => void;
  onSuccess?: () => void;
  onFailure?: (error: Error) => void;
  onLoadingStateChange?: (isLoading: boolean) => void;
}

interface LeaveRoomFlowOptions {
  roomId: string;
  reason?: 'voluntary' | 'disconnected' | 'kicked';
  leaveFunction: () => Promise<void>;
  onSuccess?: () => void;
  onFailure?: (error: Error) => void;
  preserveStateForReconnection?: boolean;
}

class RoomSubscriptionFlowManager {
  private currentFlow: SubscriptionFlowState | null = null;
  private readonly MAX_RETRIES = 0; // No retries for faster UX
  private readonly RETRY_DELAYS = [1000]; // Single retry delay if needed
  private readonly NON_RETRYABLE_ERRORS = [
    'ROOM_FULL',
    'INSUFFICIENT_BALANCE',
    'INVALID_ROOM_PASSWORD',
    'PLAYER_ALREADY_IN_ROOM',
    'ROOM_NOT_FOUND',
    'AUTHENTICATION_FAILED',
    'ROOM_CLOSED',
    'GAME_IN_PROGRESS'
  ];

  /**
   * Enhanced room join with automatic subscription management
   *
   * Behavior:
   * 1. On Join Failure: Automatically re-subscribe to lobby for fresh room listings
   * 2. On Join Success: Subscribe to room-specific events, unsubscribe from lobby
   * 3. Room Info Updates: Handle real-time room state updates for all participants
   */
  async executeEnhancedRoomJoin(options: RoomSubscriptionOptions & {
    joinFunction: () => Promise<any>;
    onSuccess?: (roomData: any) => void;
    onFailure?: (error: Error) => void;
  }): Promise<void> {
    const { roomId, joinFunction, onSuccess, onFailure, autoLobbyFallback = true } = options;

    if (env.DEBUG) {
      console.log('🚀 Starting enhanced room join with subscription management:', {
        roomId,
        autoLobbyFallback,
        timestamp: new Date().toISOString(),
      });
    }

    // Prevent concurrent operations
    if (this.currentFlow?.operationInProgress) {
      const error = new Error('Room subscription operation already in progress');
      onFailure?.(error);
      throw error;
    }

    const lobbyStore = useLobbyStore.getState();

    // Initialize flow state
    this.currentFlow = {
      wasSubscribedToLobby: lobbyStore.isSubscribed,
      roomId,
      operationInProgress: true,
      joinStartTime: Date.now(),
      retryCount: 0,
      subscriptionType: lobbyStore.isSubscribed ? 'lobby' : null,
    };

    try {
      // Step 1: Unsubscribe from lobby if currently subscribed
      if (this.currentFlow.wasSubscribedToLobby) {
        await this.unsubscribeFromLobby();
      }

      // Step 2: Attempt to join room
      const joinResult = await joinFunction();

      // Step 3: On successful join, subscribe to room events
      await this.handleJoinSuccess(roomId, joinResult, onSuccess);

    } catch (error) {
      // Step 4: On join failure, re-subscribe to lobby for fresh data
      await this.handleJoinFailure(error as Error, autoLobbyFallback, onFailure);
      throw error;
    } finally {
      // Clean up flow state
      this.currentFlow = null;
    }
  }

  /**
   * Execute comprehensive room join flow with subscription management
   * Handles pre-join phase, join attempt, success/failure paths with automatic fallback
   */
  async executeJoinRoomFlow(options: JoinRoomFlowOptions): Promise<void> {
    const {
      roomId,
      joinFunction,
      onPreJoin,
      onSuccess,
      onFailure,
      onLoadingStateChange
    } = options;

    // Prevent concurrent operations
    if (this.currentFlow?.operationInProgress) {
      const error = new Error('Room subscription operation already in progress');
      onFailure?.(error);
      throw error;
    }

    const lobbyStore = useLobbyStore.getState();

    // Initialize flow state
    this.currentFlow = {
      wasSubscribedToLobby: lobbyStore.isSubscribed,
      roomId,
      operationInProgress: true,
      joinStartTime: Date.now(),
      retryCount: 0,
      subscriptionType: lobbyStore.isSubscribed ? 'lobby' : null,
    };

    if (env.DEBUG) {
      console.log(`🚀 Joining room: ${roomId}`);
    }

    try {
      // Pre-Join Phase: Unsubscribe from lobby and prepare for join
      await this.executePreJoinPhase(onPreJoin, onLoadingStateChange);

      // Join Attempt Phase: Execute join with retry logic
      await this.executeJoinAttemptPhase(joinFunction);

      // Success Path: Handle successful join
      await this.executeSuccessPath(roomId, onSuccess);

    } catch (error) {
      // Failure Path: Handle join failure with automatic fallback
      await this.executeFailurePath(error as Error, onFailure, onLoadingStateChange);
      throw error;
    } finally {
      // Clean up flow state
      this.currentFlow = null;
    }
  }

  /**
   * Pre-Join Phase: Unsubscribe from lobby and prepare for join
   */
  private async executePreJoinPhase(
    onPreJoin?: () => void,
    onLoadingStateChange?: (isLoading: boolean) => void
  ): Promise<void> {
    // Notify about loading state
    onLoadingStateChange?.(true);
    onPreJoin?.();

    // Unsubscribe from lobby if currently subscribed
    if (this.currentFlow?.wasSubscribedToLobby) {
      try {
        const lobbyStore = useLobbyStore.getState();
        await lobbyStore.unsubscribeLobby();
      } catch (error) {
        if (env.DEBUG) {
          console.warn('⚠️ Failed to unsubscribe from lobby, continuing with join');
        }
        // Continue with join attempt even if lobby unsubscription fails
      }
    }
  }

  /**
   * Join Attempt Phase: Execute join with retry logic
   */
  private async executeJoinAttemptPhase(joinFunction: () => Promise<void>): Promise<void> {
    if (!this.currentFlow) {
      throw new Error('No active flow state');
    }

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.MAX_RETRIES; attempt++) {
      try {
        this.currentFlow.retryCount = attempt;
        await joinFunction();
        return; // Success!

      } catch (error) {
        lastError = error as Error;
        const errorCode = (error as any).code;

        // Check if this is a non-retryable error
        if (this.NON_RETRYABLE_ERRORS.includes(errorCode)) {
          throw error;
        }

        // For retryable errors, wait before retrying (except on last attempt)
        if (attempt < this.MAX_RETRIES) {
          const delay = this.RETRY_DELAYS[attempt] || 1000;
          if (env.DEBUG) {
            console.warn(`⏳ Retrying join in ${delay}ms...`);
          }
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // If we get here, all retries failed
    console.error('❌ All join attempts failed:', lastError);
    throw lastError || new Error('Failed to join room after multiple attempts');
  }

  /**
   * Success Path: Handle successful join
   */
  private async executeSuccessPath(roomId: string, onSuccess?: () => void): Promise<void> {
    if (env.DEBUG) {
      console.log('🎉 Success Path: Room join completed successfully');
    }

    // The room_joined event will automatically trigger handlePostJoinRoomSubscription
    // which will send the subscribe_room event and handle room-specific real-time updates

    onSuccess?.();

    if (env.DEBUG) {
      console.log('✅ Room join flow completed successfully:', {
        roomId,
        duration: Date.now() - (this.currentFlow?.joinStartTime || 0),
      });
    }
  }

  /**
   * Failure Path: Handle join failure with automatic fallback
   */
  private async executeFailurePath(
    error: Error,
    onFailure?: (error: Error) => void,
    onLoadingStateChange?: (isLoading: boolean) => void
  ): Promise<void> {
    if (env.DEBUG) {
      console.error('❌ Failure Path: Room join failed:', error);
    }

    // Reset loading state
    onLoadingStateChange?.(false);

    // Automatic fallback: Re-subscribe to lobby if we were previously subscribed
    if (this.currentFlow?.wasSubscribedToLobby) {
      await this.handleLobbyResubscriptionFallback();
    }

    // Show user-friendly error message
    this.showUserFriendlyError(error);

    onFailure?.(error);
  }

  /**
   * Execute comprehensive room leave flow with subscription management
   * Handles leave initiation, post-leave cleanup, and lobby re-integration
   */
  async executeLeaveRoomFlow(options: LeaveRoomFlowOptions): Promise<void> {
    const {
      roomId,
      reason = 'voluntary',
      leaveFunction,
      onSuccess,
      onFailure,
      preserveStateForReconnection = false
    } = options;

    if (env.DEBUG) {
      console.log('🚪 Starting comprehensive room leave flow:', {
        roomId,
        reason,
        preserveStateForReconnection,
        timestamp: new Date().toISOString(),
      });
    }

    try {
      // Leave Initiation: Execute the actual leave
      await this.executeLeaveInitiation(leaveFunction, reason);

      // Post-Leave Cleanup: Handle room state cleanup (unless preserving for reconnection)
      if (!preserveStateForReconnection) {
        await this.executePostLeaveCleanup(roomId);
      }

      // Lobby Re-integration: Re-subscribe to lobby (unless auto-leave due to disconnection)
      if (reason !== 'disconnected' && !preserveStateForReconnection) {
        await this.executeLobbyReintegration();
      }

      // Success callback
      onSuccess?.();

      if (env.DEBUG) {
        console.log('✅ Room leave flow completed successfully:', {
          roomId,
          reason,
          preserveStateForReconnection,
        });
      }

    } catch (error) {
      console.error('❌ Room leave flow failed:', error);
      onFailure?.(error as Error);
      throw error;
    }
  }

  /**
   * Leave Initiation: Execute the actual leave
   */
  private async executeLeaveInitiation(leaveFunction: () => Promise<void>, _reason: string): Promise<void> {
    if (env.DEBUG) {
      console.log('🚪 Leave Initiation: Executing room leave');
    }

    await leaveFunction();

    if (env.DEBUG) {
      console.log('✅ Room leave executed successfully');
    }
  }

  /**
   * Post-Leave Cleanup: Handle room state cleanup
   */
  private async executePostLeaveCleanup(_roomId: string): Promise<void> {
    if (env.DEBUG) {
      console.log('🧹 Post-Leave Cleanup: Clearing room state');
    }

    // Clear room state - this will be handled by the room_left event
    // No need to manually clear state here as the event handler will do it

    // The room_left event will handle additional cleanup
  }

  /**
   * Lobby Re-integration: Re-subscribe to lobby
   */
  private async executeLobbyReintegration(): Promise<void> {
    if (env.DEBUG) {
      console.log('🏠 Lobby Re-integration: Re-subscribing to lobby');
    }

    try {
      const lobbyStore = useLobbyStore.getState();
      if (!lobbyStore.isSubscribed) {
        await lobbyStore.subscribeLobby();

        if (env.DEBUG) {
          console.log('✅ Successfully re-subscribed to lobby');
        }
      }
    } catch (error) {
      console.error('❌ Failed to re-subscribe to lobby:', error);
      toast.error('Failed to return to lobby updates. Please refresh the page.', {
        duration: 5000,
        icon: '🔄',
      });
    }
  }

  /**
   * Handle lobby re-subscription fallback after join failure
   */
  private async handleLobbyResubscriptionFallback(): Promise<void> {
    if (env.DEBUG) {
      console.log('🔄 Fallback: Re-subscribing to lobby after join failure');
    }

    try {
      const lobbyStore = useLobbyStore.getState();
      if (!lobbyStore.isSubscribed) {
        await lobbyStore.subscribeLobby();

        if (env.DEBUG) {
          console.log('✅ Successfully re-subscribed to lobby after failure');
        }
      }
    } catch (error) {
      console.error('❌ Failed to re-subscribe to lobby after failure:', error);
      toast.error('Failed to return to lobby. Please refresh the page.', {
        duration: 5000,
        icon: '🔄',
      });
    }
  }

  /**
   * Show user-friendly error messages based on error type
   */
  private showUserFriendlyError(error: Error): void {
    const errorCode = (error as any).code;
    let message = error.message;
    let icon = '❌';

    switch (errorCode) {
      case 'ROOM_FULL':
        message = 'This room is currently full. Please try another room.';
        icon = '🚫';
        break;
      case 'INSUFFICIENT_BALANCE':
        message = 'Insufficient balance to join this room. Please add funds to your account.';
        icon = '💰';
        break;
      case 'INVALID_ROOM_PASSWORD':
        message = 'Incorrect room password. Please check and try again.';
        icon = '🔐';
        break;
      case 'PLAYER_ALREADY_IN_ROOM':
        message = 'You are already in this room.';
        icon = '⚠️';
        break;
      case 'ROOM_NOT_FOUND':
        message = 'This room no longer exists.';
        icon = '🔍';
        break;
      case 'AUTHENTICATION_FAILED':
        message = 'Authentication failed. Please log in again.';
        icon = '🔑';
        break;
      case 'GAME_IN_PROGRESS':
        message = 'Cannot join room while game is in progress.';
        icon = '🎮';
        break;
      default:
        if (message.includes('timeout') || message.includes('network')) {
          message = 'Connection timeout. Please check your internet and try again.';
          icon = '🌐';
        }
        break;
    }

    toast.error(message, {
      duration: 5000,
      icon,
    });
  }

  /**
   * Handle room subscription after successful join
   * Explicitly sends subscribe_room event with room ID to ensure proper subscription
   */
  async handlePostJoinRoomSubscription(roomId: string): Promise<void> {
    if (env.DEBUG) {
      console.log('🔔 Post-Join: Checking room subscription for room:', roomId);
    }

    // Check if already subscribed to prevent duplicate subscriptions
    const roomSubscriptionStore = useRoomSubscriptionStore.getState();
    if (roomSubscriptionStore.isSubscribed && roomSubscriptionStore.subscribedRoomId === roomId) {
      if (env.DEBUG) {
        console.log('✅ Already subscribed to room:', roomId);
      }
      return;
    }

    try {
      // Import socket service dynamically to avoid circular dependencies
      const { socketService } = await import('@/services/socket');

      if (env.DEBUG) {
        console.log('🔔 Sending subscribe_room event for room:', roomId);
      }

      // Send subscribe_room event with room ID
      await socketService.subscribeRoom(roomId);

      // Update local subscription state to reflect successful subscription
      roomSubscriptionStore.markAsSubscribed(roomId);

      if (env.DEBUG) {
        console.log('✅ Successfully subscribed to room events:', roomId);
      }
    } catch (error) {
      console.error('❌ Failed to subscribe to room events:', error);

      // Show user-friendly error message
      toast.error('Failed to subscribe to room updates. Some features may not work properly.', {
        duration: 5000,
        icon: '⚠️',
      });

      // Don't throw the error as this shouldn't prevent the user from being in the room
      // The room join was successful, subscription is just for real-time updates
    }
  }

  /**
   * Handle room unsubscription and lobby re-subscription after leave
   */
  async handlePostLeaveSubscriptionCleanup(roomId: string, autoLeave: boolean = false): Promise<void> {
    if (env.DEBUG) {
      console.log('🧹 Handling post-leave subscription cleanup:', {
        roomId,
        autoLeave,
      });
    }

    const roomSubscriptionStore = useRoomSubscriptionStore.getState();

    // Step 1: Unsubscribe from room
    if (roomSubscriptionStore.isSubscribed && roomSubscriptionStore.subscribedRoomId === roomId) {
      try {
        await roomSubscriptionStore.unsubscribeRoom(roomId);

        if (env.DEBUG) {
          console.log('✅ Successfully unsubscribed from room after leave:', roomId);
        }
      } catch (error) {
        console.warn('⚠️ Failed to unsubscribe from room after leave:', error);
        // Continue with lobby re-subscription
      }
    }

    // Step 2: Re-subscribe to lobby (unless it was an auto-leave due to disconnect)
    if (!autoLeave) {
      try {
        const lobbyStore = useLobbyStore.getState();
        if (!lobbyStore.isSubscribed) {
          await lobbyStore.subscribeLobby();

          if (env.DEBUG) {
            console.log('✅ Successfully re-subscribed to lobby after leave');
          }
        }
      } catch (error) {
        console.error('❌ Failed to re-subscribe to lobby after leave:', error);
        toast.error('Failed to return to lobby updates. Please refresh the page.', {
          duration: 5000,
          icon: '🔄',
        });
      }
    }
  }

  /**
   * Get current flow state for debugging
   */
  getCurrentFlowState(): SubscriptionFlowState | null {
    return this.currentFlow;
  }

  /**
   * Check if a join operation is currently in progress
   */
  isJoinInProgress(): boolean {
    return this.currentFlow?.operationInProgress || false;
  }

  /**
   * Get join operation statistics
   */
  getJoinStats(): {
    isInProgress: boolean;
    roomId: string | null;
    retryCount: number;
    duration: number;
  } {
    if (!this.currentFlow) {
      return {
        isInProgress: false,
        roomId: null,
        retryCount: 0,
        duration: 0,
      };
    }

    return {
      isInProgress: this.currentFlow.operationInProgress,
      roomId: this.currentFlow.roomId,
      retryCount: this.currentFlow.retryCount,
      duration: Date.now() - this.currentFlow.joinStartTime,
    };
  }

  // Enhanced subscription management methods

  /**
   * Unsubscribe from lobby with proper error handling
   */
  private async unsubscribeFromLobby(): Promise<void> {
    try {
      const lobbyStore = useLobbyStore.getState();
      if (lobbyStore.isSubscribed) {
        await lobbyStore.unsubscribeLobby();

        if (this.currentFlow) {
          this.currentFlow.subscriptionType = null;
        }

        if (env.DEBUG) {
          console.log('✅ Successfully unsubscribed from lobby');
        }
      }
    } catch (error) {
      console.warn('⚠️ Failed to unsubscribe from lobby, continuing with join:', error);
      // Continue with join attempt even if lobby unsubscription fails
    }
  }

  /**
   * Handle successful room join and subscription
   */
  private async handleJoinSuccess(roomId: string, joinResult: any, onSuccess?: (roomData: any) => void): Promise<void> {
    if (env.DEBUG) {
      console.log('🎉 Join Success: Room join completed successfully');
    }

    // Update flow state - room subscription will be handled by the room_joined event
    if (this.currentFlow) {
      this.currentFlow.subscriptionType = 'room';
    }

    // Call success callback with room data
    onSuccess?.(joinResult);

    if (env.DEBUG) {
      console.log('✅ Room join completed successfully (subscription will be handled by room_joined event):', {
        roomId,
        duration: Date.now() - (this.currentFlow?.joinStartTime || 0),
      });
    }
  }

  /**
   * Handle join failure with automatic lobby re-subscription
   */
  private async handleJoinFailure(
    error: Error,
    autoLobbyFallback: boolean,
    onFailure?: (error: Error) => void
  ): Promise<void> {
    if (env.DEBUG) {
      console.error('❌ Join Failure: Handling error and fallback:', error);
    }

    // Automatic fallback: Re-subscribe to lobby for fresh room listings
    if (autoLobbyFallback && this.currentFlow?.wasSubscribedToLobby) {
      await this.resubscribeToLobbyAfterFailure();
    }

    // Show user-friendly error message
    this.showUserFriendlyError(error);

    // Call failure callback
    onFailure?.(error);
  }

  /**
   * Re-subscribe to lobby after join failure to get fresh room listings
   */
  private async resubscribeToLobbyAfterFailure(): Promise<void> {
    if (env.DEBUG) {
      console.log('🔄 Fallback: Re-subscribing to lobby for fresh room listings');
    }

    try {
      const lobbyStore = useLobbyStore.getState();
      if (!lobbyStore.isSubscribed) {
        await lobbyStore.subscribeLobby();

        if (this.currentFlow) {
          this.currentFlow.subscriptionType = 'lobby';
        }

        if (env.DEBUG) {
          console.log('✅ Successfully re-subscribed to lobby after join failure');
        }

        // Show success message to user
        toast.success('Returned to lobby - room listings updated', {
          duration: 3000,
          icon: '🔄',
        });
      }
    } catch (error) {
      console.error('❌ Failed to re-subscribe to lobby after join failure:', error);
      toast.error('Failed to return to lobby. Please refresh the page.', {
        duration: 5000,
        icon: '🔄',
      });
    }
  }

  /**
   * Handle room info updates for real-time room state changes
   */
  handleRoomInfoUpdate(roomId: string, roomInfoData: any): void {
    if (env.DEBUG) {
      console.log('📡 Room Info Update received:', {
        roomId,
        playerCount: roomInfoData.roomState?.playerCount,
        maxPlayers: roomInfoData.room?.maxPlayers,
        status: roomInfoData.room?.status,
        playersLength: roomInfoData.players?.length,
        timestamp: roomInfoData.timestamp,
      });
    }

    // Update socket store with new room info
    const socketStore = useSocketStore.getState();
    if (socketStore.currentRoom?.id === roomId) {
      // Update current room state with new info
      // This will be handled by the socket event listeners
    }

    // Emit custom event for components to listen to with complete room data structure
    window.dispatchEvent(new CustomEvent('roomInfoUpdate', {
      detail: {
        roomId,
        roomInfo: {
          // Pass the complete room_info_updated data structure
          room: roomInfoData.room,
          roomState: roomInfoData.roomState,
          players: roomInfoData.players,
          gameConfig: roomInfoData.gameConfig,
          gameSpecificData: roomInfoData.gameSpecificData,
          timestamp: roomInfoData.timestamp,
          // Legacy compatibility fields
          playerCount: roomInfoData.roomState?.playerCount,
          maxPlayers: roomInfoData.room?.maxPlayers,
          status: roomInfoData.room?.status,
        }
      }
    }));
  }
}

// Export singleton instance
export const roomSubscriptionFlowManager = new RoomSubscriptionFlowManager();
