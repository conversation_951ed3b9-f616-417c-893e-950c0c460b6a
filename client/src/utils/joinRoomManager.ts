/**
 * Global join room manager to prevent duplicate join requests
 * This singleton ensures only one join request per room can be active at a time
 */

interface JoinAttempt {
  roomId: string;
  timestamp: number;
  promise: Promise<void>;
}

class JoinRoomManager {
  private activeJoins = new Map<string, JoinAttempt>();
  private activeLeaves = new Map<string, JoinAttempt>();
  private readonly DEBOUNCE_TIME = 2000; // 2 seconds

  /**
   * Attempts to join a room, preventing duplicate requests
   * @param roomId - The room ID to join
   * @param joinFunction - The actual join function to execute
   * @returns Promise that resolves when join is complete or rejects if duplicate
   */
  async joinRoom(
    roomId: string,
    joinFunction: () => Promise<void>
  ): Promise<void> {
    const now = Date.now();
    const existingAttempt = this.activeJoins.get(roomId);

    // Check if there's an active join for this room
    if (existingAttempt) {
      const timeSinceLastAttempt = now - existingAttempt.timestamp;

      if (timeSinceLastAttempt < this.DEBOUNCE_TIME) {
        console.warn(`Duplicate join attempt for room ${roomId} ignored (${timeSinceLastAttempt}ms ago)`);
        // Return the existing promise to avoid duplicate requests
        return existingAttempt.promise;
      }
    }

    // Create new join attempt
    const joinPromise = this.executeJoin(roomId, joinFunction);

    this.activeJoins.set(roomId, {
      roomId,
      timestamp: now,
      promise: joinPromise
    });

    try {
      await joinPromise;
    } finally {
      // Clean up after join completes (success or failure)
      this.activeJoins.delete(roomId);
    }
  }

  private async executeJoin(roomId: string, joinFunction: () => Promise<void>): Promise<void> {
    try {
      console.log(`Starting join attempt for room: ${roomId}`);
      await joinFunction();
      console.log(`Successfully joined room: ${roomId}`);
    } catch (error) {
      console.error(`Failed to join room ${roomId}:`, error);
      throw error;
    }
  }

  /**
   * Attempts to leave a room, preventing duplicate requests
   * @param roomId - The room ID to leave
   * @param leaveFunction - The actual leave function to execute
   * @returns Promise that resolves when leave is complete or rejects if duplicate
   */
  async leaveRoom(
    roomId: string,
    leaveFunction: () => Promise<void>
  ): Promise<void> {
    const now = Date.now();
    const existingAttempt = this.activeLeaves.get(roomId);

    // Check if there's an active leave for this room
    if (existingAttempt) {
      const timeSinceLastAttempt = now - existingAttempt.timestamp;

      if (timeSinceLastAttempt < this.DEBOUNCE_TIME) {
        console.warn(`Duplicate leave attempt for room ${roomId} ignored (${timeSinceLastAttempt}ms ago)`);
        // Return the existing promise to avoid duplicate requests
        return existingAttempt.promise;
      }
    }

    // Create new leave attempt
    const leavePromise = this.executeLeave(roomId, leaveFunction);

    this.activeLeaves.set(roomId, {
      roomId,
      timestamp: now,
      promise: leavePromise
    });

    try {
      await leavePromise;
    } finally {
      // Clean up after leave completes (success or failure)
      this.activeLeaves.delete(roomId);
    }
  }

  private async executeLeave(roomId: string, leaveFunction: () => Promise<void>): Promise<void> {
    try {
      console.log(`Starting leave attempt for room: ${roomId}`);
      await leaveFunction();
      console.log(`Successfully left room: ${roomId}`);
    } catch (error) {
      console.error(`Failed to leave room ${roomId}:`, error);
      throw error;
    }
  }

  /**
   * Checks if a room join is currently in progress
   * @param roomId - The room ID to check
   * @returns true if join is in progress
   */
  isJoining(roomId: string): boolean {
    const attempt = this.activeJoins.get(roomId);
    if (!attempt) return false;

    const timeSinceAttempt = Date.now() - attempt.timestamp;
    return timeSinceAttempt < this.DEBOUNCE_TIME;
  }

  /**
   * Checks if a room leave is currently in progress
   * @param roomId - The room ID to check
   * @returns true if leave is in progress
   */
  isLeaving(roomId: string): boolean {
    const attempt = this.activeLeaves.get(roomId);
    if (!attempt) return false;

    const timeSinceAttempt = Date.now() - attempt.timestamp;
    return timeSinceAttempt < this.DEBOUNCE_TIME;
  }

  /**
   * Cancels a join attempt (if possible)
   * @param roomId - The room ID to cancel
   */
  cancelJoin(roomId: string): void {
    this.activeJoins.delete(roomId);
    console.log(`Cancelled join attempt for room: ${roomId}`);
  }

  /**
   * Gets the status of all active joins (for debugging)
   */
  getActiveJoins(): Array<{ roomId: string; timestamp: number; age: number }> {
    const now = Date.now();
    return Array.from(this.activeJoins.values()).map(attempt => ({
      roomId: attempt.roomId,
      timestamp: attempt.timestamp,
      age: now - attempt.timestamp
    }));
  }

  /**
   * Clears all active joins (use with caution)
   */
  clearAll(): void {
    console.warn('Clearing all active join attempts');
    this.activeJoins.clear();
  }
}

// Export singleton instance
export const joinRoomManager = new JoinRoomManager();

// Export for testing
export { JoinRoomManager };
