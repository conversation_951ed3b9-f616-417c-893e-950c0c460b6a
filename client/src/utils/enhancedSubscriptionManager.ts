/**
 * Enhanced Subscription Manager
 * Manages client-side room subscription state with automatic lobby fallback
 * 
 * Key Features:
 * - Automatic lobby re-subscription on room join failure
 * - Room-specific event subscription on join success  
 * - Real-time room info updates for all room participants
 * - Clean subscription state management
 * - Prevention of duplicate subscriptions
 */

import { useLobbyStore } from '@/store/lobbyStore';
import { useRoomSubscriptionStore } from '@/store/roomSubscriptionStore';
import { env } from '@/config/env';
import toast from 'react-hot-toast';

interface SubscriptionState {
  currentSubscription: 'lobby' | 'room' | null;
  roomId: string | null;
  isTransitioning: boolean;
  lastTransition: number;
}

interface SubscriptionTransition {
  from: 'lobby' | 'room' | null;
  to: 'lobby' | 'room' | null;
  roomId?: string;
  timestamp: number;
  reason: 'join_success' | 'join_failure' | 'leave_room' | 'disconnect' | 'manual';
}

class EnhancedSubscriptionManager {
  private state: SubscriptionState = {
    currentSubscription: null,
    roomId: null,
    isTransitioning: false,
    lastTransition: 0,
  };

  private transitionHistory: SubscriptionTransition[] = [];
  private readonly MAX_HISTORY = 10;

  /**
   * Handle room join attempt with automatic subscription management
   */
  async handleRoomJoinAttempt(
    roomId: string,
    joinFunction: () => Promise<any>
  ): Promise<{ success: boolean; data?: any; error?: Error }> {
    // Optimized logging - removed verbose output

    // Prevent concurrent transitions
    if (this.state.isTransitioning) {
      throw new Error('Subscription transition already in progress');
    }

    this.state.isTransitioning = true;
    const wasSubscribedToLobby = this.state.currentSubscription === 'lobby';

    try {
      // Step 1: Unsubscribe from lobby if currently subscribed
      if (wasSubscribedToLobby) {
        await this.unsubscribeFromLobby('join_attempt');
      }

      // Step 2: Attempt to join room
      const joinResult = await joinFunction();

      // Step 3: On success, mark as subscribed to room
      await this.handleJoinSuccess(roomId, joinResult);

      return { success: true, data: joinResult };

    } catch (error) {
      // Step 4: On failure, re-subscribe to lobby for fresh room listings
      await this.handleJoinFailure(error as Error, wasSubscribedToLobby);
      return { success: false, error: error as Error };

    } finally {
      this.state.isTransitioning = false;
    }
  }

  /**
   * Handle successful room join
   */
  private async handleJoinSuccess(roomId: string, _joinResult: any): Promise<void> {
    if (env.DEBUG) {
      console.log('✅ Enhanced Subscription: Room join successful, updating state');
    }

    // Update subscription state
    this.state.currentSubscription = 'room';
    this.state.roomId = roomId;

    // Note: Room subscription will be handled by roomSubscriptionFlowManager
    // via the room_joined event handler, so we don't call markAsSubscribed here

    // Record transition
    this.recordTransition({
      from: 'lobby',
      to: 'room',
      roomId,
      timestamp: Date.now(),
      reason: 'join_success',
    });

    if (env.DEBUG) {
      console.log('✅ Enhanced Subscription: Room join completed (subscription will be handled by room_joined event):', roomId);
    }
  }

  /**
   * Handle failed room join with automatic lobby fallback
   */
  private async handleJoinFailure(error: Error, wasSubscribedToLobby: boolean): Promise<void> {
    // Room join failed, handling fallback

    // If we were previously subscribed to lobby, re-subscribe for fresh room listings
    if (wasSubscribedToLobby) {
      await this.resubscribeToLobbyAfterFailure();
    }

    // Record transition
    this.recordTransition({
      from: 'lobby',
      to: 'lobby',
      timestamp: Date.now(),
      reason: 'join_failure',
    });
  }

  /**
   * Re-subscribe to lobby after join failure to get fresh room listings
   */
  private async resubscribeToLobbyAfterFailure(): Promise<void> {
    if (env.DEBUG) {
      console.log('🔄 Enhanced Subscription: Re-subscribing to lobby for fresh room listings');
    }

    try {
      const lobbyStore = useLobbyStore.getState();
      if (!lobbyStore.isSubscribed) {
        await lobbyStore.subscribeLobby();

        this.state.currentSubscription = 'lobby';
        this.state.roomId = null;

        if (env.DEBUG) {
          console.log('✅ Enhanced Subscription: Successfully re-subscribed to lobby');
        }

        // Show success message to user
        toast.success('Returned to lobby - room listings updated', {
          duration: 3000,
          icon: '🔄',
        });
      }
    } catch (error) {
      console.error('❌ Enhanced Subscription: Failed to re-subscribe to lobby:', error);
      toast.error('Failed to return to lobby. Please refresh the page.', {
        duration: 5000,
        icon: '🔄',
      });
    }
  }

  /**
   * Handle room leave with lobby re-subscription
   */
  async handleRoomLeave(roomId: string, reason: 'voluntary' | 'kicked' | 'disconnected' = 'voluntary'): Promise<void> {
    if (env.DEBUG) {
      console.log('🚪 Enhanced Subscription: Handling room leave:', { roomId, reason });
    }

    // Prevent concurrent transitions
    if (this.state.isTransitioning) {
      console.warn('Subscription transition already in progress, skipping room leave handling');
      return;
    }

    this.state.isTransitioning = true;

    try {
      // Step 1: Unsubscribe from room
      await this.unsubscribeFromRoom(roomId);

      // Step 2: Re-subscribe to lobby (unless disconnected)
      if (reason !== 'disconnected') {
        await this.subscribeToLobby('room_leave');
      } else {
        // For disconnected leave, just clear the state without subscribing to lobby
        this.state.currentSubscription = null;
        this.state.roomId = null;
      }

      // Record transition
      this.recordTransition({
        from: 'room',
        to: reason === 'disconnected' ? null : 'lobby',
        roomId,
        timestamp: Date.now(),
        reason: 'leave_room',
      });

    } finally {
      this.state.isTransitioning = false;
    }
  }

  /**
   * Unsubscribe from lobby
   */
  private async unsubscribeFromLobby(reason: string): Promise<void> {
    try {
      const lobbyStore = useLobbyStore.getState();
      if (lobbyStore.isSubscribed) {
        await lobbyStore.unsubscribeLobby();
        
        if (env.DEBUG) {
          console.log(`✅ Enhanced Subscription: Unsubscribed from lobby (${reason})`);
        }
      }
    } catch (error) {
      console.warn('⚠️ Enhanced Subscription: Failed to unsubscribe from lobby:', error);
      // Continue with operation even if unsubscription fails
    }
  }

  /**
   * Unsubscribe from room
   */
  private async unsubscribeFromRoom(roomId: string): Promise<void> {
    try {
      const roomSubscriptionStore = useRoomSubscriptionStore.getState();
      if (roomSubscriptionStore.isSubscribed && roomSubscriptionStore.subscribedRoomId === roomId) {
        await roomSubscriptionStore.unsubscribeRoom(roomId);
        
        this.state.currentSubscription = null;
        this.state.roomId = null;

        if (env.DEBUG) {
          console.log('✅ Enhanced Subscription: Unsubscribed from room:', roomId);
        }
      }
    } catch (error) {
      console.warn('⚠️ Enhanced Subscription: Failed to unsubscribe from room:', error);
      // Continue with operation even if unsubscription fails
    }
  }

  /**
   * Subscribe to lobby
   */
  private async subscribeToLobby(reason: string): Promise<void> {
    try {
      const lobbyStore = useLobbyStore.getState();
      if (!lobbyStore.isSubscribed) {
        await lobbyStore.subscribeLobby();
        
        this.state.currentSubscription = 'lobby';
        this.state.roomId = null;

        if (env.DEBUG) {
          console.log(`✅ Enhanced Subscription: Subscribed to lobby (${reason})`);
        }
      }
    } catch (error) {
      console.error('❌ Enhanced Subscription: Failed to subscribe to lobby:', error);
      toast.error('Failed to return to lobby. Please refresh the page.', {
        duration: 5000,
        icon: '🔄',
      });
    }
  }

  /**
   * Record subscription transition for debugging
   */
  private recordTransition(transition: SubscriptionTransition): void {
    this.transitionHistory.push(transition);
    
    // Keep only recent transitions
    if (this.transitionHistory.length > this.MAX_HISTORY) {
      this.transitionHistory.shift();
    }

    this.state.lastTransition = transition.timestamp;

    if (env.DEBUG) {
      console.log('📝 Enhanced Subscription: Recorded transition:', transition);
    }
  }

  /**
   * Get current subscription state
   */
  getCurrentState(): SubscriptionState {
    return { ...this.state };
  }

  /**
   * Get transition history for debugging
   */
  getTransitionHistory(): SubscriptionTransition[] {
    return [...this.transitionHistory];
  }

  /**
   * Reset subscription state (for disconnect/reconnect scenarios)
   */
  reset(): void {
    this.state = {
      currentSubscription: null,
      roomId: null,
      isTransitioning: false,
      lastTransition: 0,
    };

    if (env.DEBUG) {
      console.log('🔄 Enhanced Subscription: State reset');
    }
  }
}

// Export singleton instance
export const enhancedSubscriptionManager = new EnhancedSubscriptionManager();
