/**
 * Enhanced Lobby View Component
 * Demonstrates automatic lobby subscription management and real-time room updates
 */

import { useState, useEffect } from 'react';
import { useRoomSubscription } from '@/hooks/useRoomSubscription';
import { useLobbyStore, useRoomListUpdates } from '@/store/lobbyStore';
import { EnhancedRoomJoinButton } from '@/components/Room/EnhancedRoomJoinButton';
import { Button } from '@/components/UI/Button';
import toast from 'react-hot-toast';

interface RoomData {
  id: string;
  name: string;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI';
  playerCount: number;
  maxPlayers: number;
  betAmount: number;
  status: 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED';
  isPrivate: boolean;
  createdAt: string;
}

export function EnhancedLobbyView() {
  const [rooms, setRooms] = useState<RoomData[]>([]);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
  const [updateCount, setUpdateCount] = useState(0);

  const lobbyStore = useLobbyStore();
  
  const {
    currentSubscription,
    currentRoom,
    subscribeToLobby,
    unsubscribeFromLobby,
    isConnected,
    getSubscriptionState,
  } = useRoomSubscription({
    // Handle real-time room info updates
    onRoomInfoUpdate: (update) => {
      console.log('Room info update in lobby:', update);
      
      // Update the specific room in our list
      setRooms(prevRooms => 
        prevRooms.map(room => 
          room.id === update.roomId 
            ? {
                ...room,
                playerCount: update.playerCount || room.playerCount,
                maxPlayers: update.maxPlayers || room.maxPlayers,
                status: update.status as any || room.status,
              }
            : room
        )
      );
      
      setLastUpdateTime(new Date());
      setUpdateCount(prev => prev + 1);
    },
    
    // Handle subscription changes
    onSubscriptionChange: (type) => {
      if (type === 'lobby') {
        toast.success('Connected to lobby - receiving live room updates');
      } else if (type === 'room') {
        toast.success('Joined room - receiving room-specific updates');
      } else {
        toast('Disconnected from live updates');
      }
    },
    
    // Enable automatic lobby fallback
    autoLobbyFallback: true,
  });

  // Listen for room list updates from the lobby store
  useRoomListUpdates((data) => {
    console.log('Room list update received:', data);
    
    if (data.action === 'room_list' && data.rooms) {
      // Full room list update
      setRooms(data.rooms.map(room => ({
        id: room.id,
        name: room.name,
        gameType: room.gameType as 'PRIZEWHEEL' | 'AMIDAKUJI',
        playerCount: room.playerCount,
        maxPlayers: room.maxPlayers,
        betAmount: room.betAmount,
        status: room.status as 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED',
        isPrivate: room.isPrivate || false,
        createdAt: room.createdAt,
      })));
    } else if (data.action === 'room_added' && data.room) {
      // Single room added
      const newRoom = data.room;
      setRooms(prevRooms => [...prevRooms, {
        id: newRoom.id,
        name: newRoom.name,
        gameType: newRoom.gameType as 'PRIZEWHEEL' | 'AMIDAKUJI',
        playerCount: newRoom.playerCount,
        maxPlayers: newRoom.maxPlayers,
        betAmount: newRoom.betAmount,
        status: newRoom.status as 'WAITING' | 'STARTING' | 'PLAYING' | 'FINISHED',
        isPrivate: newRoom.isPrivate || false,
        createdAt: newRoom.createdAt,
      }]);
      
      toast.success(`New room available: ${newRoom.name}`);
    } else if (data.action === 'room_removed' && data.room) {
      // Room removed
      setRooms(prevRooms => prevRooms.filter(room => room.id !== data.room!.id));
      toast(`Room ${data.room.name} is no longer available`);
    } else if (data.action === 'updated' && data.room) {
      // Room updated
      const updatedRoom = data.room;
      setRooms(prevRooms => 
        prevRooms.map(room => 
          room.id === updatedRoom.id 
            ? {
                ...room,
                playerCount: updatedRoom.playerCount,
                maxPlayers: updatedRoom.maxPlayers,
                status: updatedRoom.status as any,
              }
            : room
        )
      );
    }
    
    setLastUpdateTime(new Date());
    setUpdateCount(prev => prev + 1);
  });

  // Auto-subscribe to lobby when component mounts (if not in a room)
  useEffect(() => {
    if (isConnected && !currentRoom && currentSubscription !== 'lobby') {
      subscribeToLobby().catch(error => {
        console.error('Failed to auto-subscribe to lobby:', error);
      });
    }
  }, [isConnected, currentRoom, currentSubscription, subscribeToLobby]);

  const handleManualSubscribe = async () => {
    try {
      await subscribeToLobby();
    } catch (error) {
      toast.error('Failed to subscribe to lobby');
      console.error('Manual lobby subscription failed:', error);
    }
  };

  const handleManualUnsubscribe = async () => {
    try {
      await unsubscribeFromLobby();
    } catch (error) {
      toast.error('Failed to unsubscribe from lobby');
      console.error('Manual lobby unsubscription failed:', error);
    }
  };

  const availableRooms = rooms.filter(room => 
    room.status === 'WAITING' && room.playerCount < room.maxPlayers
  );
  
  const fullRooms = rooms.filter(room => 
    room.playerCount >= room.maxPlayers || room.status !== 'WAITING'
  );

  if (currentRoom) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <h2 className="text-xl font-bold text-blue-800 mb-2">
            Currently in Room: {currentRoom.name}
          </h2>
          <p className="text-blue-600">
            You are receiving real-time room updates. Leave the room to return to the lobby.
          </p>
          <div className="mt-2 text-sm text-blue-500">
            Subscription: {currentSubscription} | Room ID: {currentRoom.id}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header with subscription status */}
      <div className="bg-white rounded-lg shadow-md p-4">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Game Lobby</h1>
          <div className="flex items-center gap-4">
            <div className={`px-3 py-1 rounded-full text-sm ${
              currentSubscription === 'lobby' 
                ? 'bg-green-100 text-green-800' 
                : 'bg-gray-100 text-gray-600'
            }`}>
              {currentSubscription === 'lobby' ? '📡 Live Updates' : '⚫ No Updates'}
            </div>
            
            {currentSubscription === 'lobby' ? (
              <Button 
                onClick={handleManualUnsubscribe}
                className="text-sm bg-gray-200 hover:bg-gray-300 text-gray-700"
              >
                Disconnect
              </Button>
            ) : (
              <Button 
                onClick={handleManualSubscribe}
                className="text-sm bg-blue-500 hover:bg-blue-600 text-white"
                disabled={!isConnected}
              >
                Connect to Lobby
              </Button>
            )}
          </div>
        </div>

        {/* Status information */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Connection:</span>
            <span className={`ml-2 ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Total Rooms:</span>
            <span className="ml-2 font-medium">{rooms.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Available:</span>
            <span className="ml-2 font-medium text-green-600">{availableRooms.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Updates:</span>
            <span className="ml-2 font-medium">{updateCount}</span>
          </div>
        </div>

        {lastUpdateTime && (
          <div className="mt-2 text-xs text-gray-500">
            Last update: {lastUpdateTime.toLocaleTimeString()}
          </div>
        )}
      </div>

      {/* Available Rooms */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 text-green-700">
          Available Rooms ({availableRooms.length})
        </h2>
        
        {availableRooms.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No rooms available at the moment.</p>
            {currentSubscription === 'lobby' && (
              <p className="text-sm mt-2">You'll be notified when new rooms become available.</p>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableRooms.map(room => (
              <div key={room.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="font-semibold text-lg">{room.name}</h3>
                  <span className={`px-2 py-1 rounded text-xs ${
                    room.gameType === 'PRIZEWHEEL' 
                      ? 'bg-purple-100 text-purple-800' 
                      : 'bg-orange-100 text-orange-800'
                  }`}>
                    {room.gameType}
                  </span>
                </div>
                
                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <div>Players: {room.playerCount}/{room.maxPlayers}</div>
                  <div>Bet: {room.betAmount} coins</div>
                  <div>Status: {room.status}</div>
                  {room.isPrivate && <div className="text-yellow-600">🔒 Private</div>}
                </div>

                <EnhancedRoomJoinButton
                  roomId={room.id}
                  roomName={room.name}
                  playerCount={room.playerCount}
                  maxPlayers={room.maxPlayers}
                  betAmount={room.betAmount}
                  isPrivate={room.isPrivate}
                  onJoinSuccess={(roomData) => {
                    console.log('Successfully joined room:', roomData);
                  }}
                  onJoinFailure={(error) => {
                    console.error('Failed to join room:', error);
                  }}
                />
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Full/Unavailable Rooms */}
      {fullRooms.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4 text-gray-600">
            Full/Unavailable Rooms ({fullRooms.length})
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {fullRooms.map(room => (
              <div key={room.id} className="border rounded-lg p-4 bg-gray-50">
                <div className="flex justify-between items-start mb-3">
                  <h3 className="font-semibold text-lg text-gray-700">{room.name}</h3>
                  <span className={`px-2 py-1 rounded text-xs ${
                    room.gameType === 'PRIZEWHEEL' 
                      ? 'bg-purple-100 text-purple-800' 
                      : 'bg-orange-100 text-orange-800'
                  }`}>
                    {room.gameType}
                  </span>
                </div>
                
                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <div>Players: {room.playerCount}/{room.maxPlayers}</div>
                  <div>Bet: {room.betAmount} coins</div>
                  <div>Status: {room.status}</div>
                </div>

                <div className="text-center py-2 bg-gray-200 rounded text-gray-600 text-sm">
                  {room.playerCount >= room.maxPlayers ? 'Room Full' : 'Game In Progress'}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Debug Information */}
      <details className="bg-gray-50 rounded-lg p-4">
        <summary className="cursor-pointer font-medium text-gray-700">
          Debug Information
        </summary>
        <div className="mt-4 space-y-2 text-sm">
          <div>
            <strong>Subscription State:</strong>
            <pre className="bg-white p-2 rounded mt-1 overflow-x-auto">
              {JSON.stringify(getSubscriptionState(), null, 2)}
            </pre>
          </div>
          <div>
            <strong>Lobby Store State:</strong>
            <pre className="bg-white p-2 rounded mt-1 overflow-x-auto">
              {JSON.stringify({
                isSubscribed: lobbyStore.isSubscribed,
                subscriptionLoading: lobbyStore.subscriptionLoading,
                subscriptionError: lobbyStore.subscriptionError,
                updateCount: lobbyStore.updateCount,
              }, null, 2)}
            </pre>
          </div>
        </div>
      </details>
    </div>
  );
}

export default EnhancedLobbyView;
