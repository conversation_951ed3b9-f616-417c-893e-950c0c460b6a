import React from 'react';
import { useSocketStore } from '@/store/socketStore';
import { useGameStore } from '@/store/gameStore';

interface RoomStateDebugProps {
  roomId?: string;
}

export const RoomStateDebug: React.FC<RoomStateDebugProps> = ({ roomId }) => {
  const { currentRoom: socketRoom } = useSocketStore();
  const { currentRoom: gameRoom } = useGameStore();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const room = socketRoom || gameRoom;

  if (!room || (roomId && room.id !== roomId)) {
    return (
      <div className="bg-red-50 border border-red-200 rounded p-3 text-xs">
        <h4 className="font-bold text-red-800">🐛 Room State Debug</h4>
        <p className="text-red-600">No room data available</p>
        <p className="text-red-600">Expected Room ID: {roomId}</p>
        <p className="text-red-600">Socket Room: {socketRoom ? socketRoom.id : 'null'}</p>
        <p className="text-red-600">Game Room: {gameRoom ? gameRoom.id : 'null'}</p>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded p-3 text-xs space-y-2">
      <h4 className="font-bold text-blue-800">🐛 Room State Debug</h4>
      
      <div className="grid grid-cols-2 gap-2">
        <div>
          <strong>Room ID:</strong> {room.id}
        </div>
        <div>
          <strong>Source:</strong> {socketRoom ? 'Socket' : 'Game Store'}
        </div>
        <div>
          <strong>Player Count:</strong> {room.playerCount}
        </div>
        <div>
          <strong>Players Length:</strong> {room.players?.length || 0}
        </div>
        <div>
          <strong>Ready Count:</strong> {(room as any).readyCount || 0}
        </div>
        <div>
          <strong>Max Players:</strong> {room.maxPlayers}
        </div>
        <div>
          <strong>Status:</strong> {room.status}
        </div>
        <div>
          <strong>Game Type:</strong> {room.gameType}
        </div>
      </div>

      <div>
        <strong>Players Array:</strong>
        <div className="bg-white border rounded p-2 mt-1 max-h-32 overflow-y-auto">
          {room.players && room.players.length > 0 ? (
            <pre className="text-xs">
              {JSON.stringify(room.players.map(p => ({
                userId: p.userId,
                username: p.username,
                position: p.position,
                isReady: p.isReady,
                betAmount: (p as any).betAmount,
              })), null, 2)}
            </pre>
          ) : (
            <span className="text-gray-500">No players</span>
          )}
        </div>
      </div>

      <div>
        <strong>Raw Room Object Keys:</strong>
        <div className="bg-white border rounded p-2 mt-1">
          <span className="text-xs">{Object.keys(room).join(', ')}</span>
        </div>
      </div>
    </div>
  );
};

export default RoomStateDebug;
