import React, { useState, useEffect } from 'react';
import { useSocketStore } from '../../store/socketStore';
import { roomPlayerDiagnostics, logRoomDiagnostics } from '../../utils/roomPlayerDiagnostics';
import { Bug, Users, AlertTriangle, CheckCircle, RefreshCw } from 'lucide-react';

interface RoomPlayerDebugPanelProps {
  className?: string;
}

export const RoomPlayerDebugPanel: React.FC<RoomPlayerDebugPanelProps> = ({ 
  className = '' 
}) => {
  const { currentRoom } = useSocketStore();
  const [isExpanded, setIsExpanded] = useState(false);
  const [diagnosticReport, setDiagnosticReport] = useState<any>(null);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Auto-refresh diagnostic report when room changes
  useEffect(() => {
    if (currentRoom?.id) {
      refreshDiagnostics();
    }
  }, [currentRoom?.id, currentRoom?.players]);

  const refreshDiagnostics = () => {
    if (currentRoom?.id) {
      const report = logRoomDiagnostics(currentRoom.id);
      setDiagnosticReport(report);
      setLastUpdate(new Date().toLocaleTimeString());
    }
  };

  const hasIssues = currentRoom && (
    currentRoom.currentPlayers !== currentRoom.players?.length ||
    hasDuplicatePositions()
  );

  function hasDuplicatePositions(): boolean {
    if (!currentRoom?.players) return false;
    const positions = currentRoom.players.map(p => p.position);
    return positions.length !== new Set(positions).size;
  }

  const getPositionIssues = () => {
    if (!currentRoom?.players) return [];
    
    const issues: string[] = [];
    const positions = currentRoom.players.map(p => p.position);
    const positionCounts = positions.reduce((acc, pos) => {
      acc[pos] = (acc[pos] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    Object.entries(positionCounts).forEach(([pos, count]) => {
      if (count > 1) {
        const playersAtPosition = currentRoom.players.filter(p => p.position === parseInt(pos));
        issues.push(`Position ${pos}: ${playersAtPosition.map(p => p.username).join(', ')}`);
      }
    });

    return issues;
  };

  if (!currentRoom) {
    return (
      <div className={`bg-gray-100 border border-gray-300 rounded-lg p-3 ${className}`}>
        <div className="flex items-center gap-2 text-gray-600">
          <Bug className="w-4 h-4" />
          <span className="text-sm">Room Debug Panel - No room joined</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border rounded-lg shadow-sm ${className}`}>
      {/* Header */}
      <div 
        className={`flex items-center justify-between p-3 cursor-pointer border-b ${
          hasIssues ? 'bg-red-50 border-red-200' : 'bg-green-50 border-green-200'
        }`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-2">
          {hasIssues ? (
            <AlertTriangle className="w-4 h-4 text-red-600" />
          ) : (
            <CheckCircle className="w-4 h-4 text-green-600" />
          )}
          <span className={`text-sm font-medium ${
            hasIssues ? 'text-red-800' : 'text-green-800'
          }`}>
            Room Debug Panel {hasIssues ? '(Issues Detected)' : '(OK)'}
          </span>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={(e) => {
              e.stopPropagation();
              refreshDiagnostics();
            }}
            className="p-1 hover:bg-gray-200 rounded"
            title="Refresh diagnostics"
          >
            <RefreshCw className="w-3 h-3 text-gray-600" />
          </button>
          <span className="text-xs text-gray-500">
            {isExpanded ? '▼' : '▶'}
          </span>
        </div>
      </div>

      {/* Quick Status */}
      <div className="p-3 bg-gray-50 border-b">
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-gray-600">Current Players:</span>
            <span className={`ml-1 font-medium ${
              currentRoom.currentPlayers === currentRoom.players?.length 
                ? 'text-green-600' 
                : 'text-red-600'
            }`}>
              {currentRoom.currentPlayers} / {currentRoom.players?.length || 0}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Max Players:</span>
            <span className="ml-1 font-medium">{currentRoom.maxPlayers}</span>
          </div>
        </div>
        
        {/* Position Issues */}
        {getPositionIssues().length > 0 && (
          <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-xs">
            <div className="font-medium text-red-800 mb-1">Position Conflicts:</div>
            {getPositionIssues().map((issue, index) => (
              <div key={index} className="text-red-700">• {issue}</div>
            ))}
          </div>
        )}
      </div>

      {/* Expanded Details */}
      {isExpanded && (
        <div className="p-3 space-y-3">
          {/* Current Players */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-gray-800">Current Players</span>
            </div>
            <div className="space-y-1">
              {currentRoom.players?.map((player, index) => (
                <div key={player.userId} className="flex items-center justify-between text-xs bg-gray-50 p-2 rounded">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{player.username}</span>
                    <span className="text-gray-500">({player.userId.slice(-8)})</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded ${
                      getPositionIssues().some(issue => issue.includes(`Position ${player.position}`))
                        ? 'bg-red-100 text-red-700'
                        : 'bg-blue-100 text-blue-700'
                    }`}>
                      Pos {player.position}
                    </span>
                    {player.isReady && (
                      <span className="px-2 py-1 bg-green-100 text-green-700 rounded">Ready</span>
                    )}
                  </div>
                </div>
              )) || (
                <div className="text-xs text-gray-500 italic">No players in room</div>
              )}
            </div>
          </div>

          {/* Diagnostic Report */}
          {diagnosticReport && (
            <div>
              <div className="text-sm font-medium text-gray-800 mb-2">
                Diagnostic Report (Updated: {lastUpdate})
              </div>
              <div className="bg-gray-50 p-2 rounded text-xs">
                <div className="grid grid-cols-2 gap-2 mb-2">
                  <div>Total Events: {diagnosticReport.totalEvents}</div>
                  <div>Players Joined: {diagnosticReport.playerJoinSequence?.length || 0}</div>
                </div>
                
                {diagnosticReport.playerJoinSequence && diagnosticReport.playerJoinSequence.length > 0 && (
                  <div>
                    <div className="font-medium mb-1">Join Sequence:</div>
                    {diagnosticReport.playerJoinSequence.map((join: any, index: number) => (
                      <div key={index} className="text-gray-600">
                        {join.order}. {join.username} → Position {join.assignedPosition}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Backend Issue Detection */}
          {hasIssues && (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <div className="text-sm font-medium text-red-800 mb-2">
                🚨 Backend Issues Detected
              </div>
              <div className="text-xs text-red-700 space-y-1">
                {currentRoom.currentPlayers !== currentRoom.players?.length && (
                  <div>• Player count mismatch: currentPlayers ({currentRoom.currentPlayers}) ≠ actual players ({currentRoom.players?.length})</div>
                )}
                {hasDuplicatePositions() && (
                  <div>• Duplicate positions detected - players are overwriting each other</div>
                )}
                <div className="mt-2 pt-2 border-t border-red-300">
                  <div className="font-medium">Likely Backend Issues:</div>
                  <div>• Player position assignment not finding next available slot</div>
                  <div>• Room state not maintaining existing players on new joins</div>
                  <div>• Database/memory inconsistency in player management</div>
                </div>
              </div>
            </div>
          )}

          {/* Expected vs Actual */}
          <div className="bg-blue-50 border border-blue-200 rounded p-3">
            <div className="text-sm font-medium text-blue-800 mb-2">
              Expected vs Actual
            </div>
            <div className="text-xs space-y-1">
              <div>
                <span className="text-blue-700">Expected positions:</span>
                <span className="ml-1">
                  {Array.from({ length: currentRoom.players?.length || 0 }, (_, i) => i + 1).join(', ')}
                </span>
              </div>
              <div>
                <span className="text-blue-700">Actual positions:</span>
                <span className="ml-1">
                  {currentRoom.players?.map(p => p.position).sort((a, b) => a - b).join(', ') || 'None'}
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoomPlayerDebugPanel;
