import React, { useState, useEffect } from 'react';
import { Activity, Clock, Palette, TrendingUp, Users } from 'lucide-react';
import { socketService } from '../../services/socket';

interface ColorSelectionActivityProps {
  className?: string;
  maxItems?: number;
}

interface ActivityItem {
  id: string;
  type: 'color_selected' | 'color_unselected';
  player: {
    userId: string;
    username: string;
    color: {
      hex: string;
      id: string;
      name: string;
    };
  };
  timestamp: string;
  roomId: string;
}

export const ColorSelectionActivity: React.FC<ColorSelectionActivityProps> = ({
  className = '',
  maxItems = 10,
}) => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);

  useEffect(() => {
    const handleColorSelectionUpdated = (event: any) => {
      const newActivity: ActivityItem = {
        id: `${event.player.userId}-${event.timestamp}`,
        type: event.action,
        player: event.player,
        timestamp: event.timestamp,
        roomId: event.roomId,
      };

      setActivities(prev => {
        const updated = [newActivity, ...prev];
        return updated.slice(0, maxItems);
      });
    };

    socketService.on('color_selection_updated', handleColorSelectionUpdated);

    return () => {
      socketService.off('color_selection_updated', handleColorSelectionUpdated);
    };
  }, [maxItems]);

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatRelativeTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffSecs = Math.floor(diffMs / 1000);

    if (diffSecs < 5) return 'just now';
    if (diffSecs < 60) return `${diffSecs}s ago`;
    const diffMins = Math.floor(diffSecs / 60);
    if (diffMins < 60) return `${diffMins}m ago`;
    return formatTime(timestamp);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'color_selected':
        return <Palette className="w-4 h-4 text-green-600" />;
      case 'color_unselected':
        return <Users className="w-4 h-4 text-red-600" />;
      default:
        return <Activity className="w-4 h-4 text-gray-600" />;
    }
  };

  const getActivityText = (activity: ActivityItem) => {
    switch (activity.type) {
      case 'color_selected':
        return (
          <span>
            <span className="font-medium">{activity.player.username}</span>
            <span className="text-gray-600"> selected </span>
            <span 
              className="font-medium"
              style={{ color: activity.player.color.hex }}
            >
              {activity.player.color.name}
            </span>
          </span>
        );
      case 'color_unselected':
        return (
          <span>
            <span className="font-medium">{activity.player.username}</span>
            <span className="text-gray-600"> unselected </span>
            <span 
              className="font-medium"
              style={{ color: activity.player.color.hex }}
            >
              {activity.player.color.name}
            </span>
          </span>
        );
      default:
        return <span className="text-gray-600">Unknown activity</span>;
    }
  };

  if (activities.length === 0) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center gap-2 mb-3">
          <Activity className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Live Activity</h3>
        </div>
        <div className="text-center py-8 text-gray-500">
          <Activity className="w-12 h-12 text-gray-300 mx-auto mb-2" />
          <p className="text-sm">No activity yet</p>
          <p className="text-xs text-gray-400">Color selections will appear here in real-time</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Activity className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Live Activity</h3>
        </div>
        <div className="flex items-center gap-1 text-xs text-gray-500">
          <TrendingUp className="w-3 h-3" />
          <span>{activities.length} recent</span>
        </div>
      </div>

      {/* Activity Feed */}
      <div className="space-y-3 max-h-64 overflow-y-auto">
        {activities.map((activity, index) => (
          <div
            key={activity.id}
            className={`flex items-start gap-3 p-2 rounded-lg transition-all ${
              index === 0 ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
            }`}
          >
            {/* Color Circle & Icon */}
            <div className="relative flex-shrink-0">
              <div
                className="w-6 h-6 rounded-full border-2 border-gray-300"
                style={{ backgroundColor: activity.player.color.hex }}
              />
              <div className="absolute -bottom-1 -right-1 bg-white rounded-full p-0.5">
                {getActivityIcon(activity.type)}
              </div>
            </div>

            {/* Activity Content */}
            <div className="flex-1 min-w-0">
              <div className="text-sm">
                {getActivityText(activity)}
              </div>
              <div className="flex items-center gap-2 mt-1">
                <Clock className="w-3 h-3 text-gray-400" />
                <span className="text-xs text-gray-500">
                  {formatRelativeTime(activity.timestamp)}
                </span>
                {index === 0 && (
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">
                    Latest
                  </span>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      {activities.length >= maxItems && (
        <div className="mt-3 pt-3 border-t border-gray-200 text-center">
          <span className="text-xs text-gray-500">
            Showing latest {maxItems} activities
          </span>
        </div>
      )}
    </div>
  );
};
