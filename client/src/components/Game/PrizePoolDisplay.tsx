import React from 'react';
import { Trophy, Users, DollarSign, TrendingUp, Clock } from 'lucide-react';
import { useGameStore } from '../../store/gameStore';
import { useAuthStore } from '../../store/authStore';
import { usePrizeWheelErrorHandler } from '../../hooks/usePrizeWheelErrorHandler';

interface PrizePoolDisplayProps {
  roomId?: string;
  compact?: boolean;
  showPotentialWinnings?: boolean;
}

export const PrizePoolDisplay: React.FC<PrizePoolDisplayProps> = ({
  roomId,
  compact = false,
  showPotentialWinnings = true,
}) => {
  const { 
    prizePoolData, 
    getRealTimePrizePool, 
    getRealTimeParticipantCount,
    getPrizePoolData,
    getPotentialWinnings 
  } = useGameStore();
  const { user } = useAuthStore();

  const currentPrizePool = getPrizePoolData();
  const realTimePrizePool = getRealTimePrizePool();
  const participantCount = getRealTimeParticipantCount();
  const potentialWinnings = getPotentialWinnings();

  // Use real-time data if available, otherwise fall back to stored prize pool data
  const displayPrizePool = realTimePrizePool || currentPrizePool?.total_pool || 0;
  const displayParticipants = participantCount || currentPrizePool?.contributing_players?.length || 0;
  const maxPlayers = currentPrizePool?.max_players || 8;
  const houseEdge = currentPrizePool?.house_edge_percentage || 5;
  const netPrize = currentPrizePool?.net_prize_amount || (displayPrizePool * (1 - houseEdge / 100));

  // Calculate user's potential winnings
  const userPotentialWinnings = potentialWinnings?.potential_winnings?.find(
    (pw: any) => pw.user_id === user?.id
  );

  if (compact) {
    return (
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 border border-purple-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Trophy className="w-5 h-5 text-purple-600" />
            <span className="font-medium text-gray-900">Prize Pool</span>
          </div>
          <div className="text-right">
            <div className="text-lg font-bold text-purple-600">
              ${displayPrizePool.toFixed(2)}
            </div>
            <div className="text-sm text-gray-500">
              {displayParticipants}/{maxPlayers} players
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center gap-2 mb-4">
        <Trophy className="w-6 h-6 text-purple-600" />
        <h3 className="text-lg font-semibold text-gray-900">Prize Pool</h3>
        {currentPrizePool?.status && (
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
            currentPrizePool.status === 'accumulating' 
              ? 'bg-green-100 text-green-800'
              : currentPrizePool.status === 'locked'
              ? 'bg-yellow-100 text-yellow-800'
              : currentPrizePool.status === 'distributed'
              ? 'bg-blue-100 text-blue-800'
              : 'bg-gray-100 text-gray-800'
          }`}>
            {currentPrizePool.status}
          </span>
        )}
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        {/* Total Prize Pool */}
        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <DollarSign className="w-5 h-5 text-purple-600" />
            <span className="text-sm font-medium text-purple-800">Total Pool</span>
          </div>
          <div className="text-2xl font-bold text-purple-900">
            ${displayPrizePool.toFixed(2)}
          </div>
        </div>

        {/* Net Prize (after house edge) */}
        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="w-5 h-5 text-green-600" />
            <span className="text-sm font-medium text-green-800">Winner Takes</span>
          </div>
          <div className="text-2xl font-bold text-green-900">
            ${netPrize.toFixed(2)}
          </div>
          <div className="text-xs text-green-700 mt-1">
            After {houseEdge}% house edge
          </div>
        </div>
      </div>

      {/* Participants */}
      <div className="bg-gray-50 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5 text-gray-600" />
            <span className="text-sm font-medium text-gray-800">Participants</span>
          </div>
          <span className="text-lg font-semibold text-gray-900">
            {displayParticipants}/{maxPlayers}
          </span>
        </div>
        
        {/* Progress bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(displayParticipants / maxPlayers) * 100}%` }}
          />
        </div>
        
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>Entry Fee: ${currentPrizePool?.entry_fee_per_player?.toFixed(2) || '0.00'}</span>
          <span>{maxPlayers - displayParticipants} spots left</span>
        </div>
      </div>

      {/* User's Potential Winnings */}
      {showPotentialWinnings && userPotentialWinnings && (
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-4 border border-yellow-200">
          <div className="flex items-center gap-2 mb-2">
            <Trophy className="w-5 h-5 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">Your Potential Winnings</span>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <div className="text-xl font-bold text-yellow-900">
                ${userPotentialWinnings.potential_winnings.toFixed(2)}
              </div>
              <div className="text-xs text-yellow-700">
                Win probability: {(userPotentialWinnings.win_probability * 100).toFixed(1)}%
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-yellow-800">
                Pool share: {(userPotentialWinnings.current_pool_share * 100).toFixed(1)}%
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading/Error States */}
      {prizePoolData.prizePoolLoading && (
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
          <span className="ml-2 text-sm text-gray-600">Loading prize pool...</span>
        </div>
      )}

      {prizePoolData.prizePoolError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-red-500 rounded-full"></div>
            <span className="text-sm text-red-800">{prizePoolData.prizePoolError}</span>
          </div>
        </div>
      )}

      {/* Last Updated */}
      {currentPrizePool?.updated_at && (
        <div className="flex items-center gap-1 mt-4 pt-4 border-t border-gray-100">
          <Clock className="w-4 h-4 text-gray-400" />
          <span className="text-xs text-gray-500">
            Last updated: {new Date(currentPrizePool.updated_at).toLocaleTimeString()}
          </span>
        </div>
      )}
    </div>
  );
};

export default PrizePoolDisplay;
