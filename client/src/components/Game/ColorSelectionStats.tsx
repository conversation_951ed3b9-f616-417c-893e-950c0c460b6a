import React from 'react';
import { BarChart3, Users, Clock, TrendingUp, Palette, Target } from 'lucide-react';

interface ColorSelectionStatsProps {
  statistics: {
    availableCount: number;
    selectionRate: number;
    takenCount: number;
    totalColors: number;
  };
  assignments: Record<string, {
    color: { hex: string; id: string; name: string; };
    selectedAt: string;
    userId: string;
    username: string;
  }>;
  currentPlayerColor?: {
    hex: string;
    id: string;
    name: string;
  };
  lastUpdate?: string;
  className?: string;
}

export const ColorSelectionStats: React.FC<ColorSelectionStatsProps> = ({
  statistics,
  assignments,
  currentPlayerColor,
  lastUpdate,
  className = '',
}) => {
  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getSelectionRateColor = (rate: number) => {
    if (rate >= 75) return 'text-green-600';
    if (rate >= 50) return 'text-yellow-600';
    if (rate >= 25) return 'text-orange-600';
    return 'text-red-600';
  };

  const getProgressBarColor = (rate: number) => {
    if (rate >= 75) return 'bg-green-500';
    if (rate >= 50) return 'bg-yellow-500';
    if (rate >= 25) return 'bg-orange-500';
    return 'bg-red-500';
  };

  const recentSelections = Object.values(assignments)
    .sort((a, b) => new Date(b.selectedAt).getTime() - new Date(a.selectedAt).getTime())
    .slice(0, 3);

  return (
    <div className={`bg-gradient-to-br from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4 space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-2 mb-3">
        <BarChart3 className="w-5 h-5 text-purple-600" />
        <h3 className="text-lg font-semibold text-gray-900">Color Selection Analytics</h3>
        {lastUpdate && (
          <span className="ml-auto text-xs text-gray-500">
            Updated: {formatTime(lastUpdate)}
          </span>
        )}
      </div>

      {/* Main Statistics Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Available Colors */}
        <div className="bg-white rounded-lg p-3 border border-gray-200">
          <div className="flex items-center gap-2 mb-1">
            <Palette className="w-4 h-4 text-green-600" />
            <span className="text-sm font-medium text-gray-700">Available</span>
          </div>
          <div className="text-2xl font-bold text-green-600">{statistics.availableCount}</div>
          <div className="text-xs text-gray-500">of {statistics.totalColors} colors</div>
        </div>

        {/* Taken Colors */}
        <div className="bg-white rounded-lg p-3 border border-gray-200">
          <div className="flex items-center gap-2 mb-1">
            <Target className="w-4 h-4 text-red-600" />
            <span className="text-sm font-medium text-gray-700">Selected</span>
          </div>
          <div className="text-2xl font-bold text-red-600">{statistics.takenCount}</div>
          <div className="text-xs text-gray-500">colors chosen</div>
        </div>

        {/* Selection Rate */}
        <div className="bg-white rounded-lg p-3 border border-gray-200">
          <div className="flex items-center gap-2 mb-1">
            <TrendingUp className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700">Rate</span>
          </div>
          <div className={`text-2xl font-bold ${getSelectionRateColor(statistics.selectionRate)}`}>
            {statistics.selectionRate.toFixed(1)}%
          </div>
          <div className="text-xs text-gray-500">selection rate</div>
        </div>

        {/* Current Player */}
        <div className="bg-white rounded-lg p-3 border border-gray-200">
          <div className="flex items-center gap-2 mb-1">
            <Users className="w-4 h-4 text-purple-600" />
            <span className="text-sm font-medium text-gray-700">Your Color</span>
          </div>
          {currentPlayerColor ? (
            <div className="flex items-center gap-2">
              <div 
                className="w-6 h-6 rounded-full border-2 border-gray-300"
                style={{ backgroundColor: currentPlayerColor.hex }}
              />
              <div>
                <div className="text-sm font-bold text-purple-600">{currentPlayerColor.name}</div>
                <div className="text-xs text-gray-500">{currentPlayerColor.id}</div>
              </div>
            </div>
          ) : (
            <div className="text-sm text-gray-400">Not selected</div>
          )}
        </div>
      </div>

      {/* Selection Progress Bar */}
      <div className="bg-white rounded-lg p-3 border border-gray-200">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Selection Progress</span>
          <span className={`text-sm font-bold ${getSelectionRateColor(statistics.selectionRate)}`}>
            {statistics.selectionRate.toFixed(1)}%
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-500 ${getProgressBarColor(statistics.selectionRate)}`}
            style={{ width: `${Math.min(statistics.selectionRate, 100)}%` }}
          />
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-1">
          <span>{statistics.takenCount} selected</span>
          <span>{statistics.availableCount} available</span>
        </div>
      </div>

      {/* Recent Selections */}
      {recentSelections.length > 0 && (
        <div className="bg-white rounded-lg p-3 border border-gray-200">
          <div className="flex items-center gap-2 mb-2">
            <Clock className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700">Recent Selections</span>
          </div>
          <div className="space-y-2">
            {recentSelections.map((selection, index) => (
              <div key={selection.userId} className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div 
                    className="w-4 h-4 rounded-full border border-gray-300"
                    style={{ backgroundColor: selection.color.hex }}
                  />
                  <span className="text-sm text-gray-700">{selection.username}</span>
                  <span className="text-xs text-gray-500">→ {selection.color.name}</span>
                </div>
                <span className="text-xs text-gray-400">
                  {formatTime(selection.selectedAt)}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
