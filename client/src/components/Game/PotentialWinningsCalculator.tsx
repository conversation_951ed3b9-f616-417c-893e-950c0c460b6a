import React, { useState, useEffect } from 'react';
import { Calculator, TrendingUp, Target, DollarSign, Percent, Users, Zap } from 'lucide-react';

interface PotentialWinningsCalculatorProps {
  currentPool: number;
  netPrizeAmount: number;
  playerCount: number;
  maxPlayers: number;
  entryFee: number;
  userPotentialWinnings?: {
    potential_winnings: number;
    win_probability: number;
    current_pool_share: number;
  };
  className?: string;
}

export const PotentialWinningsCalculator: React.FC<PotentialWinningsCalculatorProps> = ({
  currentPool,
  netPrizeAmount,
  playerCount,
  maxPlayers,
  entryFee,
  userPotentialWinnings,
  className = '',
}) => {
  const [simulatedPlayers, setSimulatedPlayers] = useState(playerCount);
  const [animatedWinnings, setAnimatedWinnings] = useState(0);
  const [showAnimation, setShowAnimation] = useState(false);

  // Calculate potential scenarios
  const calculateScenario = (players: number) => {
    if (players === 0) return { pool: 0, netPrize: 0, probability: 0, multiplier: 0 };
    
    const totalPool = players * entryFee;
    const houseEdgePercentage = currentPool > 0 ? ((currentPool - netPrizeAmount) / currentPool) * 100 : 5;
    const netPrize = totalPool * (1 - houseEdgePercentage / 100);
    const probability = 1 / players;
    const multiplier = netPrize / entryFee;

    return {
      pool: totalPool,
      netPrize,
      probability,
      multiplier,
    };
  };

  const currentScenario = calculateScenario(playerCount);
  const simulatedScenario = calculateScenario(simulatedPlayers);
  const maxScenario = calculateScenario(maxPlayers);

  // Animate winnings when they change
  useEffect(() => {
    const targetWinnings = userPotentialWinnings?.potential_winnings || simulatedScenario.netPrize;
    
    if (targetWinnings !== animatedWinnings) {
      setShowAnimation(true);
      const duration = 1000;
      const steps = 30;
      const stepValue = (targetWinnings - animatedWinnings) / steps;
      const stepDuration = duration / steps;

      let currentStep = 0;
      const interval = setInterval(() => {
        currentStep++;
        setAnimatedWinnings(prev => prev + stepValue);

        if (currentStep >= steps) {
          setAnimatedWinnings(targetWinnings);
          setShowAnimation(false);
          clearInterval(interval);
        }
      }, stepDuration);

      return () => clearInterval(interval);
    }
  }, [userPotentialWinnings?.potential_winnings, simulatedScenario.netPrize, animatedWinnings]);

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="p-2 bg-green-100 rounded-lg">
          <Calculator className="w-5 h-5 text-green-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Potential Winnings</h3>
          <p className="text-sm text-gray-600">Calculate your potential returns</p>
        </div>
      </div>

      {/* Current Potential Winnings */}
      <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 mb-6 border border-green-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Target className="w-5 h-5 text-green-600" />
            <span className="font-medium text-green-800">Your Potential Prize</span>
          </div>
          {userPotentialWinnings && (
            <div className="text-xs text-green-700 bg-green-200 px-2 py-1 rounded-full">
              Live Data
            </div>
          )}
        </div>

        <div className={`text-3xl font-bold text-green-900 mb-2 transition-all duration-300 ${
          showAnimation ? 'scale-110' : 'scale-100'
        }`}>
          ${animatedWinnings.toFixed(2)}
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-green-700">Win Probability:</span>
            <div className="font-medium text-green-900">
              {userPotentialWinnings 
                ? (userPotentialWinnings.win_probability * 100).toFixed(1)
                : (currentScenario.probability * 100).toFixed(1)
              }%
            </div>
          </div>
          <div>
            <span className="text-green-700">Return Multiplier:</span>
            <div className="font-medium text-green-900">
              {(animatedWinnings / entryFee).toFixed(2)}x
            </div>
          </div>
        </div>
      </div>

      {/* Player Count Simulator */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <label className="text-sm font-medium text-gray-700">
            Simulate Different Player Counts
          </label>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <Users className="w-3 h-3" />
            Current: {playerCount}
          </div>
        </div>

        <div className="space-y-3">
          {/* Slider */}
          <div className="relative">
            <input
              type="range"
              min="1"
              max={maxPlayers}
              value={simulatedPlayers}
              onChange={(e) => setSimulatedPlayers(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>1 player</span>
              <span>{maxPlayers} players</span>
            </div>
          </div>

          {/* Simulated Results */}
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">
                With {simulatedPlayers} players:
              </span>
            </div>
            <div className="grid grid-cols-3 gap-3 text-sm">
              <div>
                <div className="text-blue-700">Prize Pool:</div>
                <div className="font-bold text-blue-900">
                  ${simulatedScenario.netPrize.toFixed(2)}
                </div>
              </div>
              <div>
                <div className="text-blue-700">Your Odds:</div>
                <div className="font-bold text-blue-900">
                  {(simulatedScenario.probability * 100).toFixed(1)}%
                </div>
              </div>
              <div>
                <div className="text-blue-700">Multiplier:</div>
                <div className="font-bold text-blue-900">
                  {simulatedScenario.multiplier.toFixed(2)}x
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scenario Comparison */}
      <div className="space-y-3">
        <h4 className="font-medium text-gray-900">Scenario Comparison</h4>
        
        {/* Current Scenario */}
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="text-sm font-medium text-gray-900">Current ({playerCount} players)</span>
          </div>
          <div className="text-right">
            <div className="text-sm font-bold text-gray-900">
              ${currentScenario.netPrize.toFixed(2)}
            </div>
            <div className="text-xs text-gray-600">
              {(currentScenario.probability * 100).toFixed(1)}% chance
            </div>
          </div>
        </div>

        {/* Best Case Scenario */}
        <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg border border-purple-200">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
            <span className="text-sm font-medium text-purple-900">Best Case (2 players)</span>
          </div>
          <div className="text-right">
            <div className="text-sm font-bold text-purple-900">
              ${calculateScenario(2).netPrize.toFixed(2)}
            </div>
            <div className="text-xs text-purple-700">
              50% chance
            </div>
          </div>
        </div>

        {/* Full Room Scenario */}
        <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
          <div className="flex items-center gap-3">
            <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
            <span className="text-sm font-medium text-orange-900">Full Room ({maxPlayers} players)</span>
          </div>
          <div className="text-right">
            <div className="text-sm font-bold text-orange-900">
              ${maxScenario.netPrize.toFixed(2)}
            </div>
            <div className="text-xs text-orange-700">
              {(maxScenario.probability * 100).toFixed(1)}% chance
            </div>
          </div>
        </div>
      </div>

      {/* Risk/Reward Analysis */}
      <div className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
        <div className="flex items-center gap-2 mb-2">
          <TrendingUp className="w-4 h-4 text-yellow-600" />
          <span className="text-sm font-medium text-yellow-800">Risk/Reward Analysis</span>
        </div>
        <div className="text-sm text-yellow-800 space-y-1">
          <div className="flex justify-between">
            <span>Entry Fee Investment:</span>
            <span className="font-medium">${entryFee.toFixed(2)}</span>
          </div>
          <div className="flex justify-between">
            <span>Maximum Possible Return:</span>
            <span className="font-medium">${calculateScenario(2).netPrize.toFixed(2)} (2 players)</span>
          </div>
          <div className="flex justify-between">
            <span>Expected Value (current):</span>
            <span className="font-medium">
              ${(currentScenario.netPrize * currentScenario.probability).toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .slider::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
      `}</style>
    </div>
  );
};

export default PotentialWinningsCalculator;
