import React from 'react';
import { Play, RotateCcw, Trophy } from 'lucide-react';
import { clsx } from 'clsx';

interface GameAreaProps {
  room: any;
  currentGame: any;
  gameType: 'PRIZEWHEEL' | 'AMIDAKUJI';
}

const GameArea: React.FC<GameAreaProps> = ({ currentGame, gameType }) => {
  const renderGameContent = () => {
    if (!currentGame) {
      return (
        <div className="text-center py-12">
          <Play className="mx-auto h-16 w-16 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-gray-900">
            Waiting for game to start
          </h3>
          <p className="mt-2 text-gray-600">
            All players need to be ready before the game can begin.
          </p>
        </div>
      );
    }

    switch (gameType) {
      case 'PRIZEWHEEL':
      case 'GAME_TYPE_PRIZE_WHEEL':
        return <PrizeWheelGame game={currentGame} />;
      case 'AMIDAKUJI':
      case 'GAME_TYPE_AMIDAKUJI':
        return <AmidakujiGame game={currentGame} />;
      default:
        return (
          <div className="text-center py-12">
            <p className="text-gray-600">Unknown game type: {gameType}</p>
          </div>
        );
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium text-gray-900">
            {gameType} Game
          </h3>
          {currentGame && (
            <div className={clsx(
              'status-indicator',
              currentGame.status === 'starting' && 'status-waiting',
              currentGame.status === 'playing' && 'status-playing',
              currentGame.status === 'finished' && 'status-finished'
            )}>
              {currentGame.status}
            </div>
          )}
        </div>
      </div>
      <div className="card-body">
        {renderGameContent()}
      </div>
    </div>
  );
};

const PrizeWheelGame: React.FC<{ game: any }> = ({ game }) => {
  const [isSpinning, setIsSpinning] = React.useState(false);

  const handleSpin = () => {
    setIsSpinning(true);
    // Simulate spinning animation
    setTimeout(() => {
      setIsSpinning(false);
    }, 3000);
  };

  return (
    <div className="text-center space-y-6">
      {/* Prize Wheel */}
      <div className="relative mx-auto w-64 h-64">
        <div
          className={clsx(
            'w-full h-full rounded-full border-8 border-primary-600 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center',
            isSpinning && 'animate-spin'
          )}
        >
          <div className="text-4xl font-bold text-primary-800">
            {isSpinning ? '🎰' : '🎯'}
          </div>
        </div>

        {/* Pointer */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2">
          <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-red-500"></div>
        </div>
      </div>

      {/* Game Status */}
      {game.status === 'starting' && (
        <div>
          <h4 className="text-lg font-medium text-gray-900">Game Starting...</h4>
          <p className="text-gray-600">Get ready to spin!</p>
        </div>
      )}

      {game.status === 'playing' && (
        <div>
          <h4 className="text-lg font-medium text-gray-900">Spin the Wheel!</h4>
          <button
            onClick={handleSpin}
            disabled={isSpinning}
            className="btn-primary btn-lg"
          >
            {isSpinning ? (
              <>
                <RotateCcw className="w-5 h-5 mr-2 animate-spin" />
                Spinning...
              </>
            ) : (
              <>
                <Play className="w-5 h-5 mr-2" />
                Spin Wheel
              </>
            )}
          </button>
        </div>
      )}

      {game.status === 'finished' && game.data?.result && (
        <div>
          <Trophy className="mx-auto h-12 w-12 text-yellow-500 mb-4" />
          <h4 className="text-lg font-medium text-gray-900">Game Finished!</h4>
          <p className="text-gray-600">
            Winner: <span className="font-medium">{game.data.result.winnerUsername}</span>
          </p>
          <p className="text-primary-600 font-medium">
            Prize: ${game.data.result.winAmount}
          </p>
        </div>
      )}
    </div>
  );
};

const AmidakujiGame: React.FC<{ game: any }> = ({ game }) => {
  const ladderHeight = 8;
  const ladderWidth = 6;

  return (
    <div className="text-center space-y-6">
      {/* Amidakuji Ladder */}
      <div className="mx-auto max-w-md">
        <svg
          viewBox={`0 0 ${ladderWidth * 40} ${ladderHeight * 30}`}
          className="w-full h-64 border border-gray-300 rounded-lg bg-white"
        >
          {/* Vertical lines */}
          {Array.from({ length: ladderWidth }).map((_, i) => (
            <line
              key={`vertical-${i}`}
              x1={20 + i * 40}
              y1={10}
              x2={20 + i * 40}
              y2={ladderHeight * 30 - 10}
              stroke="#374151"
              strokeWidth="2"
            />
          ))}

          {/* Horizontal lines (rungs) */}
          {Array.from({ length: ladderHeight - 2 }).map((_, row) =>
            Array.from({ length: ladderWidth - 1 }).map((_, col) => {
              // Randomly place some rungs
              const hasRung = Math.random() > 0.6;
              if (!hasRung) return null;

              return (
                <line
                  key={`horizontal-${row}-${col}`}
                  x1={20 + col * 40}
                  y1={30 + row * 30}
                  x2={20 + (col + 1) * 40}
                  y2={30 + row * 30}
                  stroke="#ef4444"
                  strokeWidth="3"
                />
              );
            })
          )}

          {/* Start positions */}
          {Array.from({ length: ladderWidth }).map((_, i) => (
            <circle
              key={`start-${i}`}
              cx={20 + i * 40}
              cy={15}
              r="6"
              fill="#3b82f6"
            />
          ))}

          {/* End positions */}
          {Array.from({ length: ladderWidth }).map((_, i) => (
            <circle
              key={`end-${i}`}
              cx={20 + i * 40}
              cy={ladderHeight * 30 - 15}
              r="6"
              fill="#10b981"
            />
          ))}
        </svg>
      </div>

      {/* Game Status */}
      {game.status === 'starting' && (
        <div>
          <h4 className="text-lg font-medium text-gray-900">Game Starting...</h4>
          <p className="text-gray-600">Choose your path!</p>
        </div>
      )}

      {game.status === 'playing' && (
        <div>
          <h4 className="text-lg font-medium text-gray-900">Follow the Path!</h4>
          <p className="text-gray-600">Watch as the paths are revealed...</p>
          <button className="btn-primary">
            <Play className="w-4 h-4 mr-2" />
            Start Reveal
          </button>
        </div>
      )}

      {game.status === 'finished' && game.data?.result && (
        <div>
          <Trophy className="mx-auto h-12 w-12 text-yellow-500 mb-4" />
          <h4 className="text-lg font-medium text-gray-900">Game Finished!</h4>
          <p className="text-gray-600">
            Winner: <span className="font-medium">{game.data.result.winnerUsername}</span>
          </p>
          <p className="text-primary-600 font-medium">
            Prize: ${game.data.result.winAmount}
          </p>
        </div>
      )}
    </div>
  );
};

export default GameArea;
