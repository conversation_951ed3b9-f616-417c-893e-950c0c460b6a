import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { PrizeWheelReadySection } from '../PrizeWheelReadySection';
import { useSocketStore } from '../../../store/socketStore';
import { useGameStore } from '../../../store/gameStore';
import { useAuthStore } from '../../../store/authStore';
import { apiClient } from '../../../services/api';
import { createMockUser, createMockRoom, createMockPrizePool } from '../../../test/setup';

// Mock all dependencies
vi.mock('../../../store/socketStore');
vi.mock('../../../store/gameStore');
vi.mock('../../../store/authStore');
vi.mock('../../../services/api');
vi.mock('../../../hooks/useRealTimeGameData');
vi.mock('../../../hooks/useEnhancedRoomData');
vi.mock('../../../hooks/useEnhancedColorSelection');
vi.mock('../../../hooks/usePrizeWheelIntegration');

const mockUseSocketStore = vi.mocked(useSocketStore);
const mockUseGameStore = vi.mocked(useGameStore);
const mockUseAuthStore = vi.mocked(useAuthStore);
const mockApiClient = vi.mocked(apiClient);

describe('Prize Wheel Integration', () => {
  const mockUser = createMockUser();
  const mockRoom = createMockRoom();
  const mockPrizePool = createMockPrizePool();

  const mockSocketStore = {
    setPlayerReady: vi.fn(),
    selectWheelColor: vi.fn(),
    currentRoom: mockRoom,
    currentGame: null,
  };

  const mockGameStore = {
    playerColorSelections: {},
    playerReadyStates: {},
    loadRoomPrizePool: vi.fn(),
    getPrizePoolData: vi.fn(() => mockPrizePool),
    getEntryFeeStatus: vi.fn(() => undefined),
    updateEntryFeeStatus: vi.fn(),
  };

  const mockPrizeWheelIntegration = {
    prizePool: {
      prizePool: mockPrizePool,
      loading: false,
      error: null,
      userPotentialWinnings: {
        user_id: mockUser.id,
        potential_winnings: 95.00,
        win_probability: 0.5,
        current_pool_share: 0.5,
      },
    },
    balance: {
      currentBalance: 50.00,
      hasInsufficientBalance: vi.fn(() => false),
      isValidating: false,
      validationError: null,
      actions: {
        validateBalance: vi.fn(() => Promise.resolve(true)),
        refreshBalance: vi.fn(),
      },
    },
    entryFee: {
      status: 'required',
      amount: 10.00,
      processing: false,
      error: null,
      isPaid: false,
      isRequired: true,
      canPay: true,
      actions: {
        processEntryFee: vi.fn(() => Promise.resolve(true)),
        processRefund: vi.fn(() => Promise.resolve(true)),
      },
    },
    canParticipate: true,
    participationBlockers: [],
    gamePhase: 'ready',
    actions: {
      prepareForGame: vi.fn(() => Promise.resolve(true)),
      exitGame: vi.fn(() => Promise.resolve(true)),
      validateParticipation: vi.fn(() => ({ canParticipate: true, blockers: [] })),
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();

    mockUseSocketStore.mockReturnValue(mockSocketStore as any);
    mockUseGameStore.mockReturnValue(mockGameStore as any);
    mockUseAuthStore.mockReturnValue({ user: mockUser } as any);

    // Mock the integration hook
    vi.doMock('../../../hooks/usePrizeWheelIntegration', () => ({
      usePrizeWheelIntegration: () => mockPrizeWheelIntegration,
    }));

    // Mock other hooks
    vi.doMock('../../../hooks/useRealTimeGameData', () => ({
      usePrizeWheelRealTimeData: () => ({
        getPlayerBalance: vi.fn(() => 50.00),
        getPlayerColors: vi.fn(() => ({})),
        getRealTimePrizePool: vi.fn(() => 100.00),
        getRealTimeParticipantCount: vi.fn(() => 2),
      }),
    }));

    vi.doMock('../../../hooks/useEnhancedRoomData', () => ({
      useEnhancedRoomData: () => ({
        enhancedRoomData: mockRoom,
        loading: false,
        error: null,
      }),
    }));

    vi.doMock('../../../hooks/useEnhancedColorSelection', () => ({
      useEnhancedColorSelection: () => ({
        enhancedSelectColor: vi.fn(() => Promise.resolve(true)),
        colorStatistics: { takenCount: 0, availableCount: 8 },
        colorAssignments: {},
        selectedColorsData: {},
        currentPlayerColorData: null,
        colorLastUpdate: null,
        isColorSelectionLoading: false,
      }),
    }));

    // Mock API responses
    mockApiClient.validateBalance.mockResolvedValue({
      success: true,
      has_sufficient_balance: true,
      current_balance: 50.00,
      required_amount: 10.00,
    });

    mockApiClient.processEntryFee.mockResolvedValue({
      success: true,
      transaction: {
        id: 'tx-123',
        user_id: mockUser.id,
        type: 'entry_fee',
        amount: 10.00,
        status: 'completed',
        description: 'Entry fee payment',
        metadata: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      prize_pool: {
        room_id: mockRoom.id,
        total_pool: 20.00,
        contributing_players: [mockUser.id],
        player_count: 1,
      },
      current_balance: 40.00,
    });
  });

  it('renders prize wheel interface with prize pool information', async () => {
    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    await waitFor(() => {
      expect(screen.getByText('Prize Pool')).toBeInTheDocument();
      expect(screen.getByText('$100.00')).toBeInTheDocument();
      expect(screen.getByText('Current Balance')).toBeInTheDocument();
    });
  });

  it('shows participation blockers when user cannot participate', async () => {
    const blockedIntegration = {
      ...mockPrizeWheelIntegration,
      canParticipate: false,
      participationBlockers: ['Insufficient balance', 'Entry fee must be paid'],
    };

    vi.doMock('../../../hooks/usePrizeWheelIntegration', () => ({
      usePrizeWheelIntegration: () => blockedIntegration,
    }));

    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    await waitFor(() => {
      expect(screen.getByText('Participation Requirements')).toBeInTheDocument();
      expect(screen.getByText('Insufficient balance')).toBeInTheDocument();
      expect(screen.getByText('Entry fee must be paid')).toBeInTheDocument();
    });
  });

  it('handles color selection with participation validation', async () => {
    const mockValidateParticipation = vi.fn(() => ({ canParticipate: true, blockers: [] }));
    const integrationWithValidation = {
      ...mockPrizeWheelIntegration,
      actions: {
        ...mockPrizeWheelIntegration.actions,
        validateParticipation: mockValidateParticipation,
      },
    };

    vi.doMock('../../../hooks/usePrizeWheelIntegration', () => ({
      usePrizeWheelIntegration: () => integrationWithValidation,
    }));

    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    // Find and click a color button
    const colorButton = screen.getByRole('button', { name: /red/i });
    fireEvent.click(colorButton);

    expect(mockValidateParticipation).toHaveBeenCalled();
  });

  it('integrates entry fee processing with ready state', async () => {
    const mockPrepareForGame = vi.fn(() => Promise.resolve(true));
    const integrationWithPrepare = {
      ...mockPrizeWheelIntegration,
      actions: {
        ...mockPrizeWheelIntegration.actions,
        prepareForGame: mockPrepareForGame,
      },
    };

    vi.doMock('../../../hooks/usePrizeWheelIntegration', () => ({
      usePrizeWheelIntegration: () => integrationWithPrepare,
    }));

    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    // First select a color
    const colorButton = screen.getByRole('button', { name: /red/i });
    fireEvent.click(colorButton);

    await waitFor(() => {
      const readyButton = screen.getByRole('button', { name: /ready/i });
      fireEvent.click(readyButton);
    });

    expect(mockPrepareForGame).toHaveBeenCalled();
    expect(mockSocketStore.setPlayerReady).toHaveBeenCalledWith(mockRoom.id, true);
  });

  it('handles exit game with refund processing', async () => {
    const mockExitGame = vi.fn(() => Promise.resolve(true));
    const readyIntegration = {
      ...mockPrizeWheelIntegration,
      isReady: true,
      actions: {
        ...mockPrizeWheelIntegration.actions,
        exitGame: mockExitGame,
      },
    };

    // Mock ready state
    mockGameStore.playerReadyStates = { [mockUser.id]: true };

    vi.doMock('../../../hooks/usePrizeWheelIntegration', () => ({
      usePrizeWheelIntegration: () => readyIntegration,
    }));

    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    const unreadyButton = screen.getByRole('button', { name: /not ready/i });
    fireEvent.click(unreadyButton);

    await waitFor(() => {
      expect(mockExitGame).toHaveBeenCalled();
    });
  });

  it('displays winner announcement when game completes', async () => {
    const gameWithResult = {
      id: 'game-123',
      status: 'results',
      data: {
        winner: {
          userId: mockUser.id,
          username: mockUser.username,
          prizeAmount: 95.00,
        },
        winningColor: 'red',
        participants: [
          {
            userId: mockUser.id,
            username: mockUser.username,
            selectedColor: 'red',
            isWinner: true,
            prizeAmount: 95.00,
          },
        ],
      },
    };

    mockSocketStore.currentGame = gameWithResult;

    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    await waitFor(() => {
      expect(screen.getByText('🎉 WINNER! 🎉')).toBeInTheDocument();
      expect(screen.getByText(mockUser.username)).toBeInTheDocument();
      expect(screen.getByText('$95.00')).toBeInTheDocument();
    });
  });

  it('shows prize pool growth chart when data is available', async () => {
    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    // Wait for component to mount and potentially show chart
    await waitFor(() => {
      // Chart should appear if there's growth data
      const chartElements = screen.queryAllByText('Prize Pool Growth');
      expect(chartElements.length).toBeGreaterThanOrEqual(0);
    });
  });

  it('displays potential winnings calculator', async () => {
    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    await waitFor(() => {
      expect(screen.getByText('Potential Winnings')).toBeInTheDocument();
      expect(screen.getByText('Calculate your potential returns')).toBeInTheDocument();
    });
  });

  it('shows house edge information', async () => {
    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    await waitFor(() => {
      expect(screen.getByText('House Edge Information')).toBeInTheDocument();
      expect(screen.getByText('Understanding the game economics')).toBeInTheDocument();
    });
  });

  it('handles error states gracefully', async () => {
    const errorIntegration = {
      ...mockPrizeWheelIntegration,
      prizePool: {
        ...mockPrizeWheelIntegration.prizePool,
        loading: false,
        error: 'Failed to load prize pool',
      },
    };

    vi.doMock('../../../hooks/usePrizeWheelIntegration', () => ({
      usePrizeWheelIntegration: () => errorIntegration,
    }));

    render(<PrizeWheelReadySection roomId={mockRoom.id} />);

    // Component should still render despite errors
    expect(screen.getByText('Prize Wheel')).toBeInTheDocument();
  });
});
