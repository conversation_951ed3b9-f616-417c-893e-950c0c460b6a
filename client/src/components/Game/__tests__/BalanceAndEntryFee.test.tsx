import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { BalanceAndEntryFee } from '../BalanceAndEntryFee';
import { useAuthStore } from '../../../store/authStore';
import { useGameStore } from '../../../store/gameStore';
import { apiClient } from '../../../services/api';

// Mock dependencies
vi.mock('../../../store/authStore');
vi.mock('../../../store/gameStore');
vi.mock('../../../services/api');
vi.mock('../../../hooks/usePrizeWheelErrorHandler', () => ({
  usePrizeWheelErrorHandler: () => ({
    handleBalanceError: vi.fn(),
    handleEntryFeeError: vi.fn(),
    handleApiErrorWithRetry: vi.fn(),
    isErrorType: vi.fn(),
  }),
}));

const mockUseAuthStore = vi.mocked(useAuthStore);
const mockUseGameStore = vi.mocked(useGameStore);
const mockApiClient = vi.mocked(apiClient);

describe('BalanceAndEntryFee', () => {
  const mockUser = {
    id: 'user1',
    username: 'testuser',
    email: '<EMAIL>',
    balance: 50.00,
  };

  const mockProps = {
    roomId: 'room-123',
    betAmount: 10.00,
    onBalanceValidated: vi.fn(),
    showEntryFeeStatus: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseAuthStore.mockReturnValue({
      user: mockUser,
      balance: 50.00,
      updateBalance: vi.fn(),
    } as any);

    mockUseGameStore.mockReturnValue({
      getEntryFeeStatus: vi.fn(() => undefined),
      updateEntryFeeStatus: vi.fn(),
    } as any);

    mockApiClient.validateBalance.mockResolvedValue({
      success: true,
      has_sufficient_balance: true,
      current_balance: 50.00,
      required_amount: 10.00,
    });

    mockApiClient.processEntryFee.mockResolvedValue({
      success: true,
      transaction: {
        id: 'tx-123',
        user_id: 'user1',
        type: 'entry_fee',
        amount: 10.00,
        status: 'completed',
        description: 'Entry fee payment',
        metadata: {},
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      },
      prize_pool: {
        room_id: 'room-123',
        total_pool: 20.00,
        contributing_players: ['user1'],
        player_count: 1,
      },
      current_balance: 40.00,
    });
  });

  it('renders current balance correctly', async () => {
    render(<BalanceAndEntryFee {...mockProps} />);

    expect(screen.getByText('Current Balance')).toBeInTheDocument();
    expect(screen.getByText('$50.00')).toBeInTheDocument();
    expect(screen.getByText('Required for entry:')).toBeInTheDocument();
    expect(screen.getByText('$10.00')).toBeInTheDocument();

    await waitFor(() => {
      expect(screen.getByText('Sufficient')).toBeInTheDocument();
    });
  });

  it('shows insufficient balance warning', async () => {
    mockUseAuthStore.mockReturnValue({
      user: mockUser,
      balance: 5.00, // Less than required
      updateBalance: vi.fn(),
    } as any);

    mockApiClient.validateBalance.mockResolvedValue({
      success: false,
      has_sufficient_balance: false,
      current_balance: 5.00,
      required_amount: 10.00,
      shortfall: 5.00,
      error_message: 'Insufficient balance',
    });

    render(<BalanceAndEntryFee {...mockProps} />);

    await waitFor(() => {
      expect(screen.getByText('Insufficient')).toBeInTheDocument();
      expect(screen.getByText('Shortfall:')).toBeInTheDocument();
      expect(screen.getByText('$5.00')).toBeInTheDocument();
    });
  });

  it('processes entry fee successfully', async () => {
    const mockUpdateEntryFeeStatus = vi.fn();
    mockUseGameStore.mockReturnValue({
      getEntryFeeStatus: vi.fn(() => undefined),
      updateEntryFeeStatus: mockUpdateEntryFeeStatus,
    } as any);

    render(<BalanceAndEntryFee {...mockProps} />);

    await waitFor(() => {
      expect(screen.getByText('Pay Entry Fee')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Pay Entry Fee'));

    await waitFor(() => {
      expect(mockUpdateEntryFeeStatus).toHaveBeenCalledWith('user1', 'pending');
      expect(mockApiClient.processEntryFee).toHaveBeenCalledWith({
        user_id: 'user1',
        room_id: 'room-123',
        bet_amount: 10.00,
        metadata: {
          game_type: 'prize_wheel',
          timestamp: expect.any(String),
        },
      });
    });
  });

  it('shows entry fee status correctly', () => {
    mockUseGameStore.mockReturnValue({
      getEntryFeeStatus: vi.fn(() => 'paid'),
      updateEntryFeeStatus: vi.fn(),
    } as any);

    render(<BalanceAndEntryFee {...mockProps} />);

    expect(screen.getByText('Entry fee paid')).toBeInTheDocument();
    expect(screen.getByText('Entry fee of $10.00 has been deducted from your balance.')).toBeInTheDocument();
  });

  it('shows processing state', () => {
    mockUseGameStore.mockReturnValue({
      getEntryFeeStatus: vi.fn(() => 'pending'),
      updateEntryFeeStatus: vi.fn(),
    } as any);

    render(<BalanceAndEntryFee {...mockProps} />);

    expect(screen.getByText('Processing...')).toBeInTheDocument();
  });

  it('shows failed state with retry option', () => {
    mockUseGameStore.mockReturnValue({
      getEntryFeeStatus: vi.fn(() => 'failed'),
      updateEntryFeeStatus: vi.fn(),
    } as any);

    render(<BalanceAndEntryFee {...mockProps} />);

    expect(screen.getByText('Payment failed')).toBeInTheDocument();
    expect(screen.getByText('Failed to process entry fee. Please try again.')).toBeInTheDocument();
    expect(screen.getByText('Retry Payment')).toBeInTheDocument();
  });

  it('calls onBalanceValidated callback', async () => {
    const mockOnBalanceValidated = vi.fn();
    
    render(<BalanceAndEntryFee {...mockProps} onBalanceValidated={mockOnBalanceValidated} />);

    await waitFor(() => {
      expect(mockOnBalanceValidated).toHaveBeenCalledWith(true);
    });
  });

  it('handles balance validation error', async () => {
    mockApiClient.validateBalance.mockRejectedValue(new Error('Network error'));

    render(<BalanceAndEntryFee {...mockProps} />);

    await waitFor(() => {
      expect(screen.getByText('Insufficient')).toBeInTheDocument();
    });
  });

  it('handles entry fee processing error', async () => {
    mockApiClient.processEntryFee.mockRejectedValue(new Error('Payment failed'));

    render(<BalanceAndEntryFee {...mockProps} />);

    await waitFor(() => {
      const payButton = screen.getByText('Pay Entry Fee');
      fireEvent.click(payButton);
    });

    await waitFor(() => {
      expect(mockUseGameStore().updateEntryFeeStatus).toHaveBeenCalledWith('user1', 'failed');
    });
  });

  it('refreshes balance on button click', async () => {
    render(<BalanceAndEntryFee {...mockProps} />);

    const refreshButton = screen.getByRole('button', { name: /refresh/i });
    fireEvent.click(refreshButton);

    await waitFor(() => {
      expect(mockApiClient.validateBalance).toHaveBeenCalled();
    });
  });

  it('disables pay button when balance is insufficient', async () => {
    mockApiClient.validateBalance.mockResolvedValue({
      success: false,
      has_sufficient_balance: false,
      current_balance: 5.00,
      required_amount: 10.00,
    });

    render(<BalanceAndEntryFee {...mockProps} />);

    await waitFor(() => {
      const payButton = screen.queryByText('Pay Entry Fee');
      expect(payButton).not.toBeInTheDocument();
    });
  });

  it('hides entry fee section when showEntryFeeStatus is false', () => {
    render(<BalanceAndEntryFee {...mockProps} showEntryFeeStatus={false} />);

    expect(screen.queryByText('Entry fee required')).not.toBeInTheDocument();
  });

  it('shows last checked timestamp', async () => {
    render(<BalanceAndEntryFee {...mockProps} />);

    await waitFor(() => {
      expect(screen.getByText(/Last checked:/)).toBeInTheDocument();
    });
  });
});
