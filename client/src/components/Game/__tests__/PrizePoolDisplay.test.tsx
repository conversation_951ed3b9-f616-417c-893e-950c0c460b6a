import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { PrizePoolDisplay } from '../PrizePoolDisplay';
import { useGameStore } from '../../../store/gameStore';
import { useAuthStore } from '../../../store/authStore';

// Mock the stores
vi.mock('../../../store/gameStore');
vi.mock('../../../store/authStore');
vi.mock('../../../hooks/usePrizeWheelErrorHandler');

const mockUseGameStore = vi.mocked(useGameStore);
const mockUseAuthStore = vi.mocked(useAuthStore);

describe('PrizePoolDisplay', () => {
  const mockPrizePool = {
    id: 'pool-123',
    room_id: 'room-123',
    total_pool: 100.00,
    entry_fee_per_player: 10.00,
    contributing_players: ['user1', 'user2'],
    house_edge_percentage: 5,
    house_edge_amount: 5.00,
    net_prize_amount: 95.00,
    status: 'accumulating' as const,
    game_type: 'prize_wheel' as const,
    room_name: 'Test Room',
    max_players: 8,
    player_count: 2,
    is_full: false,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    metadata: {},
  };

  const mockUser = {
    id: 'user1',
    username: 'testuser',
    email: '<EMAIL>',
    balance: 50.00,
  };

  const mockPotentialWinnings = {
    potential_winnings: [
      {
        user_id: 'user1',
        potential_winnings: 95.00,
        win_probability: 0.5,
        current_pool_share: 0.5,
      },
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseGameStore.mockReturnValue({
      prizePoolData: {
        currentPrizePool: mockPrizePool,
        prizePoolLoading: false,
        prizePoolError: null,
        entryFeeStatus: {},
        potentialWinnings: mockPotentialWinnings,
        transactionHistory: [],
      },
      getRealTimePrizePool: vi.fn(() => 100.00),
      getRealTimeParticipantCount: vi.fn(() => 2),
      getPrizePoolData: vi.fn(() => mockPrizePool),
      getPotentialWinnings: vi.fn(() => mockPotentialWinnings),
    } as any);

    mockUseAuthStore.mockReturnValue({
      user: mockUser,
    } as any);
  });

  it('renders prize pool information correctly', () => {
    render(<PrizePoolDisplay roomId="room-123" />);

    expect(screen.getByText('Prize Pool')).toBeInTheDocument();
    expect(screen.getByText('$100.00')).toBeInTheDocument();
    expect(screen.getByText('$95.00')).toBeInTheDocument();
    expect(screen.getByText('2/8 players')).toBeInTheDocument();
  });

  it('renders compact version correctly', () => {
    render(<PrizePoolDisplay roomId="room-123" compact={true} />);

    expect(screen.getByText('Prize Pool')).toBeInTheDocument();
    expect(screen.getByText('$100.00')).toBeInTheDocument();
    expect(screen.getByText('2/8 players')).toBeInTheDocument();
  });

  it('displays user potential winnings when available', () => {
    render(<PrizePoolDisplay roomId="room-123" showPotentialWinnings={true} />);

    expect(screen.getByText('Your Potential Winnings')).toBeInTheDocument();
    expect(screen.getByText('$95.00')).toBeInTheDocument();
    expect(screen.getByText('Win probability: 50.0%')).toBeInTheDocument();
  });

  it('shows loading state', () => {
    mockUseGameStore.mockReturnValue({
      prizePoolData: {
        currentPrizePool: null,
        prizePoolLoading: true,
        prizePoolError: null,
        entryFeeStatus: {},
        potentialWinnings: null,
        transactionHistory: [],
      },
      getRealTimePrizePool: vi.fn(() => 0),
      getRealTimeParticipantCount: vi.fn(() => 0),
      getPrizePoolData: vi.fn(() => null),
      getPotentialWinnings: vi.fn(() => null),
    } as any);

    render(<PrizePoolDisplay roomId="room-123" />);

    expect(screen.getByText('Loading prize pool...')).toBeInTheDocument();
  });

  it('shows error state', () => {
    mockUseGameStore.mockReturnValue({
      prizePoolData: {
        currentPrizePool: null,
        prizePoolLoading: false,
        prizePoolError: 'Failed to load prize pool',
        entryFeeStatus: {},
        potentialWinnings: null,
        transactionHistory: [],
      },
      getRealTimePrizePool: vi.fn(() => 0),
      getRealTimeParticipantCount: vi.fn(() => 0),
      getPrizePoolData: vi.fn(() => null),
      getPotentialWinnings: vi.fn(() => null),
    } as any);

    render(<PrizePoolDisplay roomId="room-123" />);

    expect(screen.getByText('Failed to load prize pool')).toBeInTheDocument();
  });

  it('displays correct status badge', () => {
    render(<PrizePoolDisplay roomId="room-123" />);

    expect(screen.getByText('accumulating')).toBeInTheDocument();
  });

  it('calculates house edge correctly', () => {
    render(<PrizePoolDisplay roomId="room-123" />);

    expect(screen.getByText('After 5% house edge')).toBeInTheDocument();
  });

  it('shows entry fee information', () => {
    render(<PrizePoolDisplay roomId="room-123" />);

    expect(screen.getByText('Entry Fee: $10.00')).toBeInTheDocument();
  });

  it('displays remaining spots', () => {
    render(<PrizePoolDisplay roomId="room-123" />);

    expect(screen.getByText('6 spots left')).toBeInTheDocument();
  });

  it('handles missing user gracefully', () => {
    mockUseAuthStore.mockReturnValue({
      user: null,
    } as any);

    render(<PrizePoolDisplay roomId="room-123" showPotentialWinnings={true} />);

    expect(screen.queryByText('Your Potential Winnings')).not.toBeInTheDocument();
  });

  it('uses real-time data when available', () => {
    mockUseGameStore.mockReturnValue({
      prizePoolData: {
        currentPrizePool: mockPrizePool,
        prizePoolLoading: false,
        prizePoolError: null,
        entryFeeStatus: {},
        potentialWinnings: mockPotentialWinnings,
        transactionHistory: [],
      },
      getRealTimePrizePool: vi.fn(() => 150.00), // Different from stored data
      getRealTimeParticipantCount: vi.fn(() => 3), // Different from stored data
      getPrizePoolData: vi.fn(() => mockPrizePool),
      getPotentialWinnings: vi.fn(() => mockPotentialWinnings),
    } as any);

    render(<PrizePoolDisplay roomId="room-123" />);

    // Should use real-time data
    expect(screen.getByText('$150.00')).toBeInTheDocument();
    expect(screen.getByText('3/8 players')).toBeInTheDocument();
  });
});
