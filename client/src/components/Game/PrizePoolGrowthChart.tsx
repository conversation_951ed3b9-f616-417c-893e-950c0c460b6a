import React, { useState, useEffect } from 'react';
import { TrendingUp, DollarSign, Users, Clock, Trophy } from 'lucide-react';

interface PrizePoolDataPoint {
  timestamp: string;
  totalPool: number;
  playerCount: number;
  entryFee: number;
}

interface PrizePoolGrowthChartProps {
  data: PrizePoolDataPoint[];
  currentPool: number;
  maxPlayers: number;
  className?: string;
}

export const PrizePoolGrowthChart: React.FC<PrizePoolGrowthChartProps> = ({
  data,
  currentPool,
  maxPlayers,
  className = '',
}) => {
  const [animatedPool, setAnimatedPool] = useState(0);
  const [showAnimation, setShowAnimation] = useState(false);

  // Animate pool value changes
  useEffect(() => {
    if (currentPool !== animatedPool) {
      setShowAnimation(true);
      const duration = 1000; // 1 second animation
      const steps = 30;
      const stepValue = (currentPool - animatedPool) / steps;
      const stepDuration = duration / steps;

      let currentStep = 0;
      const interval = setInterval(() => {
        currentStep++;
        setAnimatedPool(prev => prev + stepValue);

        if (currentStep >= steps) {
          setAnimatedPool(currentPool);
          setShowAnimation(false);
          clearInterval(interval);
        }
      }, stepDuration);

      return () => clearInterval(interval);
    }
  }, [currentPool, animatedPool]);

  // Calculate chart dimensions and scaling
  const chartWidth = 300;
  const chartHeight = 150;
  const maxPoolValue = Math.max(...data.map(d => d.totalPool), currentPool);
  const maxPlayerValue = maxPlayers;

  // Generate SVG path for pool growth
  const generatePoolPath = () => {
    if (data.length < 2) return '';

    const points = data.map((point, index) => {
      const x = (index / (data.length - 1)) * chartWidth;
      const y = chartHeight - (point.totalPool / maxPoolValue) * chartHeight;
      return `${x},${y}`;
    });

    return `M ${points.join(' L ')}`;
  };

  // Generate SVG path for player count
  const generatePlayerPath = () => {
    if (data.length < 2) return '';

    const points = data.map((point, index) => {
      const x = (index / (data.length - 1)) * chartWidth;
      const y = chartHeight - (point.playerCount / maxPlayerValue) * chartHeight;
      return `${x},${y}`;
    });

    return `M ${points.join(' L ')}`;
  };

  // Calculate growth rate
  const calculateGrowthRate = () => {
    if (data.length < 2) return 0;
    const firstValue = data[0].totalPool;
    const lastValue = data[data.length - 1].totalPool;
    return ((lastValue - firstValue) / firstValue) * 100;
  };

  const growthRate = calculateGrowthRate();
  const isPositiveGrowth = growthRate > 0;

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <TrendingUp className="w-5 h-5 text-purple-600" />
          <h3 className="text-lg font-semibold text-gray-900">Prize Pool Growth</h3>
        </div>
        <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
          isPositiveGrowth 
            ? 'bg-green-100 text-green-800' 
            : 'bg-gray-100 text-gray-800'
        }`}>
          <TrendingUp className={`w-3 h-3 ${isPositiveGrowth ? 'text-green-600' : 'text-gray-600'}`} />
          {growthRate > 0 ? '+' : ''}{growthRate.toFixed(1)}%
        </div>
      </div>

      {/* Current Pool Value - Animated */}
      <div className="mb-6">
        <div className="flex items-center gap-3">
          <div className="p-3 bg-gradient-to-br from-purple-100 to-blue-100 rounded-lg">
            <DollarSign className="w-8 h-8 text-purple-600" />
          </div>
          <div>
            <div className={`text-3xl font-bold text-purple-900 transition-all duration-300 ${
              showAnimation ? 'scale-110' : 'scale-100'
            }`}>
              ${animatedPool.toFixed(2)}
            </div>
            <div className="text-sm text-gray-600">Current Prize Pool</div>
          </div>
        </div>
      </div>

      {/* Chart */}
      {data.length > 1 && (
        <div className="mb-4">
          <div className="relative bg-gray-50 rounded-lg p-4">
            <svg width={chartWidth} height={chartHeight} className="w-full h-auto">
              {/* Grid lines */}
              <defs>
                <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                  <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" strokeWidth="0.5"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />

              {/* Prize pool line */}
              <path
                d={generatePoolPath()}
                fill="none"
                stroke="url(#poolGradient)"
                strokeWidth="3"
                strokeLinecap="round"
                strokeLinejoin="round"
              />

              {/* Player count line */}
              <path
                d={generatePlayerPath()}
                fill="none"
                stroke="url(#playerGradient)"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeDasharray="5,5"
              />

              {/* Data points */}
              {data.map((point, index) => {
                const x = (index / (data.length - 1)) * chartWidth;
                const y = chartHeight - (point.totalPool / maxPoolValue) * chartHeight;
                return (
                  <circle
                    key={index}
                    cx={x}
                    cy={y}
                    r="4"
                    fill="#8b5cf6"
                    stroke="white"
                    strokeWidth="2"
                    className="hover:r-6 transition-all duration-200"
                  />
                );
              })}

              {/* Gradients */}
              <defs>
                <linearGradient id="poolGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#8b5cf6" />
                  <stop offset="100%" stopColor="#3b82f6" />
                </linearGradient>
                <linearGradient id="playerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#10b981" />
                  <stop offset="100%" stopColor="#06b6d4" />
                </linearGradient>
              </defs>
            </svg>

            {/* Legend */}
            <div className="flex items-center justify-center gap-6 mt-3 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-3 h-0.5 bg-gradient-to-r from-purple-500 to-blue-500"></div>
                <span className="text-gray-600">Prize Pool</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-0.5 bg-gradient-to-r from-green-500 to-cyan-500 border-dashed"></div>
                <span className="text-gray-600">Players</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Statistics */}
      <div className="grid grid-cols-3 gap-4">
        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-1">
            <Users className="w-4 h-4 text-blue-600" />
            <span className="text-xs font-medium text-gray-600">Players</span>
          </div>
          <div className="text-lg font-bold text-blue-900">
            {data.length > 0 ? data[data.length - 1].playerCount : 0}/{maxPlayers}
          </div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-1">
            <Trophy className="w-4 h-4 text-yellow-600" />
            <span className="text-xs font-medium text-gray-600">Entry Fee</span>
          </div>
          <div className="text-lg font-bold text-yellow-900">
            ${data.length > 0 ? data[data.length - 1].entryFee.toFixed(2) : '0.00'}
          </div>
        </div>

        <div className="text-center">
          <div className="flex items-center justify-center gap-1 mb-1">
            <Clock className="w-4 h-4 text-green-600" />
            <span className="text-xs font-medium text-gray-600">Updates</span>
          </div>
          <div className="text-lg font-bold text-green-900">
            {data.length}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      {data.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="text-xs text-gray-500">
            Last updated: {new Date(data[data.length - 1].timestamp).toLocaleTimeString()}
          </div>
        </div>
      )}
    </div>
  );
};

export default PrizePoolGrowthChart;
