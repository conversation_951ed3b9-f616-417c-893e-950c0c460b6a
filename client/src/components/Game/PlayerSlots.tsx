import React from 'react';
import { User, Crown } from 'lucide-react';
import { clsx } from 'clsx';

import { usePrizeWheelRealTimeData } from '@/hooks/useRealTimeGameData';
import { getColorHexById } from '@/constants/wheelColors';

interface PlayerSlotData {
  userId: string;
  username: string;
  avatar?: string;
  position: number;
  isReady: boolean;
  isHost?: boolean;
  colorId?: string;
  colorName?: string;
  colorHex?: string;
}

interface PlayerSlotsProps {
  maxPlayers: number;
  currentUserId?: string;
  players: PlayerSlotData[];
  className?: string;
}

interface PlayerSlotProps {
  player?: PlayerSlotData;
  slotNumber: number;
  isCurrentUser: boolean;
  isEmpty: boolean;
}

const PlayerSlot: React.FC<PlayerSlotProps> = ({
  player,
  slotNumber,
  isCurrentUser,
  isEmpty,
}) => {
  if (isEmpty) {
    return (
      <div className="bg-gray-50 border-2 border-dashed border-gray-200 rounded-lg p-4 flex flex-col items-center justify-center min-h-[120px] transition-all duration-200 hover:border-gray-300">
        <User className="w-8 h-8 text-gray-300 mb-2" />
        <span className="text-sm text-gray-400 text-center">
          Waiting for player...
        </span>
        <span className="text-xs text-gray-300 mt-1">
          Slot {slotNumber}
        </span>
      </div>
    );
  }

  if (!player) return null;

  return (
    <div
      className={clsx(
        'bg-white border-2 rounded-lg p-4 transition-all duration-200 min-h-[100px] relative',
        isCurrentUser
          ? 'border-blue-300 bg-blue-50 shadow-md'
          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
      )}
      style={{
        // Use player's selected wheel color as border color (2-3px width for visibility)
        // Apply to all players including current user when they have a color selected
        ...(player.colorId && player.colorHex && {
          borderColor: player.colorHex,
          borderWidth: '3px',
        } as React.CSSProperties),
      }}
    >
      {/* Player Header */}
      <div className="flex items-center gap-2 mb-3">
        {/* Avatar or User Icon */}
        <div className="flex-shrink-0">
          {player.avatar ? (
            <img
              src={player.avatar}
              alt={player.username}
              className="w-8 h-8 rounded-full object-cover"
            />
          ) : (
            <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-gray-500" />
            </div>
          )}
        </div>

        {/* Username and Host Badge */}
        <div className="min-w-0 flex-1">
          <div className="flex items-center gap-1">
            <span
              className={clsx(
                'text-sm font-medium truncate',
                isCurrentUser ? 'text-blue-900' : 'text-gray-900'
              )}
              title={player.username}
            >
              {player.username}
            </span>
            {player.isHost && (
              <Crown className="w-3 h-3 text-yellow-500 flex-shrink-0" />
            )}
          </div>
          <span className="text-xs text-gray-500">Slot {slotNumber}</span>
        </div>
      </div>

      {/* Color Display */}
      <div className="flex items-center justify-center">
        {player.colorId && player.colorHex ? (
          <div className="flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-lg">
            <div
              className="w-6 h-6 rounded-full border-2 border-white shadow-sm ring-1 ring-gray-200"
              style={{ backgroundColor: player.colorHex }}
              title={player.colorName || player.colorId}
            />
            <span className="text-sm font-medium text-gray-800 capitalize">
              {player.colorName || player.colorId}
            </span>
          </div>
        ) : (
          <div className="px-3 py-2 bg-gray-50 rounded-lg">
            <span className="text-sm text-gray-400 italic">No color selected</span>
          </div>
        )}
      </div>
    </div>
  );
};

export const PlayerSlots: React.FC<PlayerSlotsProps> = ({
  maxPlayers,
  currentUserId,
  players,
  className = '',
}) => {
  // Debug logging for players prop changes
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🎮 PlayerSlots received players update:', {
        playersLength: players?.length,
        maxPlayers,
        players: players?.map(p => ({
          userId: p.userId,
          username: p.username,
          position: p.position,
          isReady: p.isReady,
        })),
      });

      // Additional debug for slot generation
      console.log('🎮 PlayerSlots slot generation debug:', {
        maxPlayers,
        playersCount: players?.length,
        slotsToGenerate: maxPlayers,
        playerPositions: players?.map(p => p.position),
      });
    }
  }, [players, maxPlayers]);

  // Use the real-time data hook for Prize Wheel
  const {
    getPlayerColors,
  } = usePrizeWheelRealTimeData();

  // Get real-time color data
  const playerColors = getPlayerColors();

  // Create enhanced player data with real-time color information
  const enhancedPlayers = players.map(player => {
    // playerColors is a Record<string, string> (playerId -> colorId)
    const playerColorId = playerColors[player.userId];

    // Get color details from the colorId
    const getColorDetails = (colorId: string) => {
      if (!colorId) return { colorName: undefined, colorHex: undefined };

      return {
        colorName: colorId.charAt(0).toUpperCase() + colorId.slice(1),
        colorHex: getColorHexById(colorId),
      };
    };

    const colorDetails = playerColorId ? getColorDetails(playerColorId) : { colorName: undefined, colorHex: undefined };

    return {
      ...player,
      colorId: playerColorId || player.colorId,
      colorName: colorDetails.colorName || player.colorName,
      colorHex: colorDetails.colorHex || player.colorHex,
    };
  });

  // Create slots array with empty slots
  const slots = Array.from({ length: maxPlayers }, (_, index) => {
    const slotNumber = index + 1;
    // Fix: Match player position with slotNumber (1-based) since server sends 1-based positions
    const player = enhancedPlayers.find(p => p.position === slotNumber);

    return {
      slotNumber,
      player,
      isEmpty: !player,
      isCurrentUser: player?.userId === currentUserId,
    };
  });

  // Calculate grid columns based on maxPlayers for optimal responsive layout
  const getGridCols = (maxPlayers: number) => {
    if (maxPlayers <= 2) return 'grid-cols-1 sm:grid-cols-2';
    if (maxPlayers <= 4) return 'grid-cols-2 lg:grid-cols-4';
    if (maxPlayers <= 6) return 'grid-cols-2 md:grid-cols-3 lg:grid-cols-6';
    if (maxPlayers <= 8) return 'grid-cols-2 md:grid-cols-4 lg:grid-cols-8';
    return 'grid-cols-2 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6';
  };

  const totalPlayers = enhancedPlayers.length;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-2">
        <User className="w-5 h-5 text-gray-600" />
        <h3 className="font-medium text-gray-900">
          Players ({totalPlayers}/{maxPlayers})
        </h3>
      </div>

      {/* Debug Info - Temporary (remove after testing) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-blue-50 border border-blue-200 rounded p-2 text-xs">
          <div><strong>Real-time Player Colors:</strong> {JSON.stringify(playerColors)}</div>
          <div><strong>Enhanced Players:</strong> {JSON.stringify(enhancedPlayers.map(p => ({
            username: p.username,
            position: p.position,
            colorId: p.colorId,
            colorHex: p.colorHex
          })))}</div>
          <div><strong>Slots:</strong> {JSON.stringify(slots.map(s => ({
            slotNumber: s.slotNumber,
            hasPlayer: !!s.player,
            playerName: s.player?.username,
            playerPosition: s.player?.position
          })))}</div>
        </div>
      )}

      {/* Player Slots Grid */}
      <div className={`grid gap-3 ${getGridCols(maxPlayers)}`}>
        {slots.map(({ slotNumber, player, isEmpty, isCurrentUser }) => (
          <PlayerSlot
            key={slotNumber}
            player={player}
            slotNumber={slotNumber}
            isCurrentUser={isCurrentUser}
            isEmpty={isEmpty}
          />
        ))}
      </div>


    </div>
  );
};

export default PlayerSlots;
