import React from 'react';
import { WheelColor } from '../../types/socket';
import { Check, User, Lock, Users } from 'lucide-react';
import { Button } from '../UI/Button';

interface ColorSelectorProps {
  availableColors: WheelColor[];
  selectedColor?: string;
  onColorSelect: (colorId: string) => void;
  disabled?: boolean;
  loading?: boolean;
  currentUserId?: string;
  className?: string;
  playerColors?: Record<string, {
    userId: string;
    username: string;
    isReady: boolean;
    colorId?: string;
    colorName?: string;
    colorHex?: string;
  }>;
  // Ready button props
  isReady?: boolean;
  onToggleReady?: () => void;
  canToggleReady?: boolean;
  readyLoading?: boolean;
}

export const ColorSelector: React.FC<ColorSelectorProps> = ({
  availableColors,
  selectedColor,
  onColorSelect,
  disabled = false,
  loading = false,
  currentUserId,
  className = '',
  playerColors = {},
  isReady = false,
  onToggleReady,
  canToggleReady = false,
  readyLoading = false,
}) => {
  const handleColorClick = (color: WheelColor) => {
    if (disabled || loading || !color.isAvailable) return;

    // Prevent selecting the same color again
    if (color.id === selectedColor) return;

    onColorSelect(color.id);
  };

  const getColorStatus = (color: WheelColor) => {
    if (color.id === selectedColor) return 'selected';
    if (!color.isAvailable) return 'unavailable';
    return 'available';
  };

  const getPlayerForColor = (colorId: string) => {
    return Object.values(playerColors).find(player => player.colorId === colorId);
  };

  const getColorTooltip = (color: WheelColor) => {
    const player = getPlayerForColor(color.id);

    if (color.id === selectedColor) {
      return `✅ ${color.name} - Your selection`;
    }

    if (player) {
      const readyStatus = player.isReady ? ' ✓ (Ready)' : ' ⏳ (Not Ready)';
      return `🔒 ${color.name} - Selected by ${player.username}${readyStatus}`;
    }

    return `🎨 Click to select ${color.name}`;
  };

  const getColorClasses = (color: WheelColor) => {
    const status = getColorStatus(color);
    const baseClasses = 'relative w-12 h-12 rounded-full border-2 transition-all duration-200 transform';

    if (loading) {
      return `${baseClasses} border-gray-300 opacity-60 cursor-wait`;
    }

    switch (status) {
      case 'selected':
        return `${baseClasses} border-blue-500 scale-110 shadow-lg cursor-pointer`;
      case 'unavailable':
        return `${baseClasses} border-gray-300 opacity-50 cursor-not-allowed`;
      case 'available':
        return `${baseClasses} border-gray-300 hover:border-gray-400 hover:scale-105 cursor-pointer shadow-md`;
      default:
        return baseClasses;
    }
  };

  const renderColorIndicator = (color: WheelColor) => {
    const status = getColorStatus(color);
    
    if (status === 'selected') {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-blue-500 rounded-full p-1">
            <Check className="w-4 h-4 text-white" />
          </div>
        </div>
      );
    }
    
    if (status === 'unavailable' && color.selectedBy) {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-gray-600 rounded-full p-1">
            <User className="w-4 h-4 text-white" />
          </div>
        </div>
      );
    }
    
    if (disabled) {
      return (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="bg-gray-500 rounded-full p-1">
            <Lock className="w-3 h-3 text-white" />
          </div>
        </div>
      );
    }
    
    return null;
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Color Grid - Compact */}
      <div className="grid grid-cols-4 gap-3 justify-items-center">
        {availableColors.map((color) => (
          <div key={color.id} className="flex flex-col items-center space-y-1">
            <button
              onClick={() => handleColorClick(color)}
              disabled={disabled || !color.isAvailable}
              className={getColorClasses(color)}
              style={{ backgroundColor: color.hex }}
              title={getColorTooltip(color)}
            >
              {renderColorIndicator(color)}
            </button>

            {/* Color Name - Compact */}
            <span className={`text-xs text-center ${
              getColorStatus(color) === 'selected'
                ? 'text-blue-600 font-bold'
                : getColorStatus(color) === 'unavailable'
                  ? 'text-gray-400'
                  : 'text-gray-600'
            }`}>
              {color.name}
            </span>
          </div>
        ))}
      </div>

      {/* Selection Summary - Compact */}
      {/* {selectedColor && (
        <div className="bg-blue-50 border border-blue-200 rounded p-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div
                className="w-4 h-4 rounded-full border border-white"
                style={{
                  backgroundColor: availableColors.find(c => c.id === selectedColor)?.hex
                }}
              />
              <span className="text-sm font-medium text-blue-800">
                {availableColors.find(c => c.id === selectedColor)?.name}
              </span>
            </div>

            {!disabled && (
              <button
                onClick={() => onColorSelect('')}
                className="text-xs text-blue-600 hover:text-blue-800 underline"
              >
                Clear
              </button>
            )}
          </div>
        </div>
      )} */}

      {/* Player Color Selections */}
      {Object.keys(playerColors).length > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded p-2">
          <h4 className="text-xs font-medium text-gray-700 mb-2">Player Selections:</h4>
          <div className="space-y-1">
            {Object.values(playerColors)
              .filter(player => player.colorId)
              .map(player => (
                <div key={player.userId} className="flex items-center justify-between text-xs">
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full border border-gray-300"
                      style={{ backgroundColor: player.colorHex || '#000000' }}
                    />
                    <span className={`${player.userId === currentUserId ? 'font-medium text-blue-700' : 'text-gray-600'}`}>
                      {player.username}{player.userId === currentUserId ? ' (You)' : ''}
                    </span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="text-gray-500">{player.colorName}</span>
                    {player.isReady && (
                      <Check className="w-3 h-3 text-green-500" />
                    )}
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Ready Button Section */}
      {onToggleReady && (
        <div className="space-y-3">
          <div className="border-t border-gray-200 pt-3">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-4 h-4 text-blue-600" />
              <h4 className="font-medium text-gray-900">Ready to Play</h4>
            </div>

            <Button
              onClick={onToggleReady}
              disabled={!canToggleReady || readyLoading}
              className={`w-full ${
                isReady
                  ? 'bg-green-600 hover:bg-green-700 text-white'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {readyLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>{isReady ? 'Updating...' : 'Marking Ready...'}</span>
                </div>
              ) : (
                isReady ? '✓ Ready!' : 'Mark Ready'
              )}
            </Button>

            {/* Status Messages */}
            {isReady && (
              <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-800">
                ✓ You are ready! Waiting for other players...
              </div>
            )}

            {!canToggleReady && !isReady && selectedColor && (
              <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-sm text-yellow-800">
                Select a color first to mark ready
              </div>
            )}
          </div>
        </div>
      )}

      {/* Status */}
      <div className="text-center text-xs text-gray-500">
        {availableColors.filter(c => c.isAvailable).length} of {availableColors.length} colors available
      </div>
    </div>
  );
};

export default ColorSelector;
