import React, { useState, useEffect } from 'react';
import { Wallet, AlertTriangle, CheckCircle, Clock, RefreshCw } from 'lucide-react';
import { useAuthStore } from '../../store/authStore';
import { useGameStore } from '../../store/gameStore';
import { apiClient } from '../../services/api';
import { usePrizeWheelErrorHandler } from '../../hooks/usePrizeWheelErrorHandler';
import { PrizeWheelErrorCode } from '../../utils/prizeWheelErrorHandler';
import toast from 'react-hot-toast';

interface BalanceAndEntryFeeProps {
  roomId: string;
  betAmount: number;
  onBalanceValidated?: (isValid: boolean) => void;
  showEntryFeeStatus?: boolean;
}

export const BalanceAndEntryFee: React.FC<BalanceAndEntryFeeProps> = ({
  roomId,
  betAmount,
  onBalanceValidated,
  showEntryFeeStatus = true,
}) => {
  const { user, balance } = useAuthStore();
  const { getEntryFeeStatus, updateEntryFeeStatus } = useGameStore();
  const {
    handleBalanceError,
    handleEntryFeeError,
    handleApiErrorWithRetry,
    isErrorType
  } = usePrizeWheelErrorHandler();
  
  const [balanceValidation, setBalanceValidation] = useState<{
    isValid: boolean;
    loading: boolean;
    error: string | null;
    lastChecked?: Date;
  }>({
    isValid: false,
    loading: false,
    error: null,
  });

  const entryFeeStatus = user ? getEntryFeeStatus(user.id) : undefined;

  // Validate balance when component mounts or bet amount changes
  useEffect(() => {
    if (user && betAmount > 0) {
      validateBalance();
    }
  }, [user, betAmount]);

  const validateBalance = async () => {
    if (!user) return;

    setBalanceValidation(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await handleApiErrorWithRetry(
        () => apiClient.validateBalance({
          user_id: user.id,
          bet_amount: betAmount,
        }),
        3, // max retries
        1000, // retry delay
        { context: 'balance_validation', showToast: false }
      );

      const isValid = result.has_sufficient_balance;
      setBalanceValidation({
        isValid,
        loading: false,
        error: isValid ? null : result.error_message || 'Insufficient balance',
        lastChecked: new Date(),
      });

      onBalanceValidated?.(isValid);
    } catch (error) {
      const prizeWheelError = handleBalanceError(error, balance, betAmount);

      setBalanceValidation({
        isValid: false,
        loading: false,
        error: prizeWheelError.message,
        lastChecked: new Date(),
      });
      onBalanceValidated?.(false);
    }
  };

  const processEntryFee = async () => {
    if (!user || !balanceValidation.isValid) return;

    updateEntryFeeStatus(user.id, 'pending');

    try {
      const result = await handleApiErrorWithRetry(
        () => apiClient.processEntryFee({
          user_id: user.id,
          room_id: roomId,
          bet_amount: betAmount,
          metadata: {
            game_type: 'prize_wheel',
            timestamp: new Date().toISOString(),
          },
        }),
        2, // max retries for entry fee processing
        2000, // retry delay
        { context: 'entry_fee_processing', showToast: false }
      );

      if (result.success) {
        updateEntryFeeStatus(user.id, 'paid');
        toast.success(`Entry fee processed: $${betAmount.toFixed(2)}`, {
          icon: '💰',
          duration: 4000,
        });

        // Update balance if provided in response
        if (result.current_balance !== undefined) {
          useAuthStore.getState().updateBalance(result.current_balance);
        }
      } else {
        updateEntryFeeStatus(user.id, 'failed');
        handleEntryFeeError(
          new Error(result.error_message || 'Entry fee processing failed'),
          user.id,
          roomId,
          betAmount
        );
      }
    } catch (error) {
      updateEntryFeeStatus(user.id, 'failed');

      // Handle specific entry fee errors
      const prizeWheelError = handleEntryFeeError(error, user.id, roomId, betAmount);

      // Show specific guidance for certain errors
      if (isErrorType(error, PrizeWheelErrorCode.INSUFFICIENT_BALANCE)) {
        // Balance validation should have caught this, but handle it anyway
        setBalanceValidation(prev => ({ ...prev, isValid: false }));
        onBalanceValidated?.(false);
      }
    }
  };

  const getBalanceStatusColor = () => {
    if (balanceValidation.loading) return 'text-yellow-600';
    if (balanceValidation.error) return 'text-red-600';
    if (balanceValidation.isValid) return 'text-green-600';
    return 'text-gray-600';
  };

  const getBalanceStatusIcon = () => {
    if (balanceValidation.loading) return <RefreshCw className="w-4 h-4 animate-spin" />;
    if (balanceValidation.error) return <AlertTriangle className="w-4 h-4" />;
    if (balanceValidation.isValid) return <CheckCircle className="w-4 h-4" />;
    return <Wallet className="w-4 h-4" />;
  };

  const getEntryFeeStatusDisplay = () => {
    switch (entryFeeStatus) {
      case 'pending':
        return {
          icon: <Clock className="w-4 h-4 text-yellow-600" />,
          text: 'Processing...',
          color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
        };
      case 'paid':
        return {
          icon: <CheckCircle className="w-4 h-4 text-green-600" />,
          text: 'Entry fee paid',
          color: 'text-green-600 bg-green-50 border-green-200',
        };
      case 'failed':
        return {
          icon: <AlertTriangle className="w-4 h-4 text-red-600" />,
          text: 'Payment failed',
          color: 'text-red-600 bg-red-50 border-red-200',
        };
      default:
        return {
          icon: <Wallet className="w-4 h-4 text-gray-600" />,
          text: 'Entry fee required',
          color: 'text-gray-600 bg-gray-50 border-gray-200',
        };
    }
  };

  const entryFeeDisplay = getEntryFeeStatusDisplay();

  return (
    <div className="space-y-4">
      {/* Current Balance */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Wallet className="w-5 h-5 text-gray-600" />
            <span className="font-medium text-gray-900">Current Balance</span>
          </div>
          <button
            onClick={validateBalance}
            disabled={balanceValidation.loading}
            className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${balanceValidation.loading ? 'animate-spin' : ''}`} />
          </button>
        </div>

        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold text-gray-900">
            ${balance?.toFixed(2) || '0.00'}
          </div>
          <div className={`flex items-center gap-2 ${getBalanceStatusColor()}`}>
            {getBalanceStatusIcon()}
            <span className="text-sm font-medium">
              {balanceValidation.loading 
                ? 'Checking...' 
                : balanceValidation.isValid 
                ? 'Sufficient' 
                : 'Insufficient'
              }
            </span>
          </div>
        </div>

        {/* Balance validation details */}
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Required for entry:</span>
            <span className="font-medium text-gray-900">${betAmount.toFixed(2)}</span>
          </div>
          {!balanceValidation.isValid && balance !== undefined && (
            <div className="flex justify-between text-sm mt-1">
              <span className="text-red-600">Shortfall:</span>
              <span className="font-medium text-red-600">
                ${Math.max(0, betAmount - balance).toFixed(2)}
              </span>
            </div>
          )}
        </div>

        {balanceValidation.error && (
          <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-red-600" />
              <span className="text-sm text-red-800">{balanceValidation.error}</span>
            </div>
          </div>
        )}

        {balanceValidation.lastChecked && (
          <div className="mt-2 text-xs text-gray-500">
            Last checked: {balanceValidation.lastChecked.toLocaleTimeString()}
          </div>
        )}
      </div>

      {/* Entry Fee Status */}
      {showEntryFeeStatus && (
        <div className={`rounded-lg border p-4 ${entryFeeDisplay.color}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {entryFeeDisplay.icon}
              <span className="font-medium">{entryFeeDisplay.text}</span>
            </div>
            
            {entryFeeStatus !== 'paid' && balanceValidation.isValid && (
              <button
                onClick={processEntryFee}
                disabled={entryFeeStatus === 'pending' || !balanceValidation.isValid}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {entryFeeStatus === 'pending' ? 'Processing...' : 'Pay Entry Fee'}
              </button>
            )}
          </div>

          {entryFeeStatus === 'paid' && (
            <div className="mt-2 text-sm">
              Entry fee of ${betAmount.toFixed(2)} has been deducted from your balance.
            </div>
          )}

          {entryFeeStatus === 'failed' && (
            <div className="mt-2">
              <div className="text-sm mb-2">
                Failed to process entry fee. Please try again.
              </div>
              <button
                onClick={processEntryFee}
                disabled={!balanceValidation.isValid}
                className="px-3 py-1 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                Retry Payment
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default BalanceAndEntryFee;
