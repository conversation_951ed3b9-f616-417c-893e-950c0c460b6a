import React from 'react';
import { Crown, Check, X, User, DollarSign, AlertTriangle } from 'lucide-react';
import { clsx } from 'clsx';
import { useGameStore } from '@/store/gameStore';
import { usePrizeWheelRealTimeData } from '@/hooks/useRealTimeGameData';
import { getColorHexById } from '@/constants/wheelColors';

interface Player {
  id: string;
  userId: string;
  username: string;
  avatar?: string;
  position: number;
  isReady: boolean;
  isHost?: boolean;
  balance?: number;
  insufficientBalance?: boolean;
  colorId?: string;
  colorHex?: string;
}

interface PlayerListProps {
  players: Player[];
  currentUserId?: string;
  showBalance?: boolean;
  showColorIndicator?: boolean;
}

const PlayerList: React.FC<PlayerListProps> = ({
  players,
  currentUserId,
  showBalance = false,
  showColorIndicator = false
}) => {
  const { getPlayerBalance, getPlayerInsufficientBalance } = useGameStore();
  const { getPlayerColors } = usePrizeWheelRealTimeData();
  const sortedPlayers = [...players].sort((a, b) => a.position - b.position);

  // Get real-time color data
  const playerColors = getPlayerColors();

  // Get real-time balance and color data for players
  const playersWithRealData = sortedPlayers.map(player => {
    const realBalance = getPlayerBalance(player.userId);
    const hasInsufficientBalance = getPlayerInsufficientBalance(player.userId);

    // Get real-time color data
    const playerColorId = playerColors[player.userId];
    const colorDetails = playerColorId ? {
      colorId: playerColorId,
      colorHex: getColorHexById(playerColorId),
    } : { colorId: player.colorId, colorHex: player.colorHex };

    return {
      ...player,
      balance: realBalance !== undefined ? realBalance : player.balance,
      insufficientBalance: hasInsufficientBalance || player.insufficientBalance,
      colorId: colorDetails.colorId || player.colorId,
      colorHex: colorDetails.colorHex || player.colorHex,
    };
  });

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center gap-2 mb-3">
        <User className="w-4 h-4 text-gray-600" />
        <h3 className="font-medium text-gray-900">Players ({players.length})</h3>
      </div>
      <div className="space-y-2">
        {playersWithRealData.map((player) => (
          <div
            key={player.id}
            className={clsx(
              'flex items-center justify-between p-2 rounded border-2',
              player.userId === currentUserId
                ? 'border-blue-200 bg-blue-50'
                : 'border-gray-200 bg-gray-50'
            )}
            style={{
              // Use player's selected wheel color as border color (2-3px width for visibility)
              // Apply to all players including current user when they have a color selected
              ...(player.colorId && player.colorHex && {
                borderColor: player.colorHex,
                borderWidth: '3px',
              } as React.CSSProperties),
            }}
          >
            <div className="flex items-center space-x-2">
              {/* Avatar - Compact */}
              <div className="relative">
                <div className="w-6 h-6 rounded-full bg-gray-300 flex items-center justify-center">
                  {player.avatar ? (
                    <img
                      src={player.avatar}
                      alt={player.username}
                      className="w-full h-full rounded-full"
                    />
                  ) : (
                    <User className="w-3 h-3 text-gray-600" />
                  )}
                </div>

                {/* Host Crown - Smaller */}
                {player.isHost && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full flex items-center justify-center">
                    <Crown className="w-2 h-2 text-yellow-800" />
                  </div>
                )}
              </div>

              {/* Player Info - Enhanced */}
              <div className="flex-1">
                <div className="flex items-center space-x-1">
                  <span className="text-sm font-medium text-gray-900">
                    {player.username}
                  </span>
                  {player.userId === currentUserId && (
                    <span className="text-xs text-blue-600 font-medium">
                      (You)
                    </span>
                  )}
                  {player.isHost && (
                    <Crown className="w-3 h-3 text-yellow-600" />
                  )}
                </div>

                {/* Additional player info */}
                <div className="flex items-center space-x-2 mt-1">
                  {/* Color indicator */}
                  {showColorIndicator && player.colorId && (
                    <div className="flex items-center space-x-1">
                      <div
                        className="w-3 h-3 rounded-full border border-gray-300"
                        style={{ backgroundColor: player.colorHex || '#000000' }}
                        title={`Selected color: ${player.colorId}`}
                      />
                      <span className="text-xs text-gray-500 capitalize">{player.colorId}</span>
                    </div>
                  )}

                  {/* Balance display */}
                  {showBalance && player.balance !== undefined && (
                    <div className="flex items-center space-x-1">
                      <DollarSign className="w-3 h-3 text-green-600" />
                      <span className={clsx(
                        "text-xs font-medium",
                        player.insufficientBalance ? "text-red-600" : "text-green-600"
                      )}>
                        ${player.balance.toFixed(2)}
                      </span>
                    </div>
                  )}

                  {/* Insufficient balance warning */}
                  {player.insufficientBalance && (
                    <div className="flex items-center space-x-1" title="Insufficient balance">
                      <AlertTriangle className="w-3 h-3 text-red-500" />
                      <span className="text-xs text-red-600">Low balance</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Ready Status - Compact */}
            <div className="flex items-center">
              {player.isReady ? (
                <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                  <Check className="w-3 h-3 text-white" />
                </div>
              ) : (
                <div className="w-5 h-5 bg-gray-300 rounded-full flex items-center justify-center">
                  <X className="w-3 h-3 text-gray-600" />
                </div>
              )}
            </div>
          </div>
        ))}

        {/* Empty Slots - Compact */}
        {players.length < 8 && (
          <div className="pt-2 border-t border-gray-200">
            <div className="text-xs text-gray-500 mb-1">
              {8 - players.length} empty slot{8 - players.length !== 1 ? 's' : ''}
            </div>
            <div className="flex flex-wrap gap-1">
              {Array.from({ length: Math.min(8 - players.length, 4) }).map((_, index) => (
                <div
                  key={`empty-${index}`}
                  className="w-6 h-6 rounded-full border border-dashed border-gray-300 bg-gray-100 flex items-center justify-center"
                >
                  <User className="w-3 h-3 text-gray-400" />
                </div>
              ))}
              {8 - players.length > 4 && (
                <div className="text-xs text-gray-400 flex items-center ml-1">
                  +{8 - players.length - 4} more
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PlayerList;
