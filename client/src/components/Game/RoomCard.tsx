import React from 'react';
import { Lock, Clock, DollarSign, Zap, AlertCircle } from 'lucide-react';
import { Room } from '@/types/api';
import { useAuthStore } from '@/store/authStore';
import { useSocketStore } from '@/store/socketStore';
import RoomCapacity from '@/components/UI/RoomCapacity';
import JoinRoomButton from '@/components/UI/JoinRoomButton';
import { clsx } from 'clsx';

interface RoomCardProps {
  room: Room;
  onJoin: (roomId: string, password?: string, betAmount?: number) => void;
  isRecentlyUpdated?: boolean;
  isJoining?: boolean;
}

const RoomCard: React.FC<RoomCardProps> = ({ room, onJoin, isRecentlyUpdated = false, isJoining = false }) => {
  const { user } = useAuthStore();
  const { isJoiningRoom } = useSocketStore();

  const getStatusColor = (status: string) => {
    const normalizedStatus = status?.toUpperCase();
    switch (normalizedStatus) {
      case 'WAITING':
      case 'ROOM_STATUS_WAITING':
        return 'status-waiting';
      case 'STARTING':
      case 'ROOM_STATUS_STARTING':
        return 'status-playing';
      case 'PLAYING':
      case 'ROOM_STATUS_PLAYING':
      case 'ACTIVE':
        return 'status-playing';
      case 'FINISHED':
      case 'ROOM_STATUS_FINISHED':
      case 'COMPLETED':
        return 'status-finished';
      case 'FULL':
        return 'status-offline';
      case 'CLOSED':
      case 'CANCELLED':
        return 'status-offline';
      default:
        return 'status-offline';
    }
  };

  const getStatusText = (status: string) => {
    const normalizedStatus = status?.toUpperCase();
    switch (normalizedStatus) {
      case 'WAITING':
      case 'ROOM_STATUS_WAITING':
        return 'Waiting for players';
      case 'STARTING':
      case 'ROOM_STATUS_STARTING':
        return 'Starting soon';
      case 'PLAYING':
      case 'ROOM_STATUS_PLAYING':
      case 'ACTIVE':
        return 'Game in progress';
      case 'FINISHED':
      case 'ROOM_STATUS_FINISHED':
      case 'COMPLETED':
        return 'Game finished';
      case 'FULL':
        return 'Room full';
      case 'CLOSED':
      case 'CANCELLED':
        return 'Room closed';
      default:
        return status;
    }
  };



  // More robust join logic - handle different status formats
  const normalizedStatus = room.status?.toUpperCase();
  const maxPlayers = room.config?.maxPlayers || room.maxPlayers || 8;
  const currentPlayers = room.currentPlayerCount || 0;

  // Can join if room is waiting for players and not full
  // Handle both uppercase and lowercase status values from different services
  const canJoin = (normalizedStatus === 'WAITING' ||
                   normalizedStatus === 'ROOM_STATUS_WAITING' ||
                   normalizedStatus === 'OPEN' ||
                   normalizedStatus === 'ACTIVE') &&
                  currentPlayers < maxPlayers;

  const betAmount = room.config?.betAmount || room.betAmount || 0;
  const hasInsufficientBalance = user && user.balance < betAmount;

  // Only disable if cannot join due to room state, insufficient balance, or currently joining
  const isCurrentlyJoining = isJoining || isJoiningRoom(room.id);
  const isDisabled = !canJoin || hasInsufficientBalance || isCurrentlyJoining;

  // Removed verbose debug logging for cleaner console

  return (
    <div className={clsx(
      'room-card',
      isRecentlyUpdated && 'ring-2 ring-blue-400 ring-opacity-50 bg-blue-50'
    )}>
      <div className="card-header">
        <div className="flex justify-between items-start">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              {room.name}
              {room.isPrivate && <Lock className="w-4 h-4 ml-2 text-gray-400" />}
              {isRecentlyUpdated && (
                <Zap className="w-4 h-4 ml-2 text-blue-500 animate-pulse" />
              )}
            </h3>
            <p className="text-sm text-gray-600">{room.gameType}</p>
          </div>
          <span className={clsx('status-indicator', getStatusColor(room.status))}>
            {getStatusText(room.status)}
          </span>
        </div>
      </div>

      <div className="card-body">
        <div className="space-y-3">
          {/* Room Info */}
          <div className="flex items-center justify-between">
            <RoomCapacity
              currentPlayers={currentPlayers}
              maxPlayers={maxPlayers}
              size="sm"
              showIcon={true}
              showBadge={false}
            />
            <div className="flex items-center text-sm text-gray-600">
              <DollarSign className="w-4 h-4 mr-1" />
              <span>${room.config?.betAmount || room.betAmount}</span>
            </div>
          </div>

          {/* Game Duration */}
          {(room.config?.gameDuration || room.gameDuration) && (
            <div className="flex items-center text-sm text-gray-600">
              <Clock className="w-4 h-4 mr-1" />
              <span>{Math.round((room.config?.gameDuration || room.gameDuration || 1800000) / 60000)} min game</span>
            </div>
          )}

          {/* Players List */}
          {room.players.length > 0 && (
            <div>
              <p className="text-xs text-gray-500 mb-2">Players:</p>
              <div className="flex flex-wrap gap-1">
                {room.players.slice(0, 6).map((player) => (
                  <div
                    key={player.id}
                    className="player-avatar"
                    title={player.username}
                  >
                    {player.avatar ? (
                      <img
                        src={player.avatar}
                        alt={player.username}
                        className="w-full h-full rounded-full"
                      />
                    ) : (
                      player.username.charAt(0).toUpperCase()
                    )}
                  </div>
                ))}
                {room.players.length > 6 && (
                  <div className="player-avatar">
                    +{room.players.length - 6}
                  </div>
                )}
              </div>
            </div>
          )}


        </div>
      </div>

      <div className="card-footer">
        {/* Balance warning */}
        {hasInsufficientBalance && (
          <div className="mb-3 p-2 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center text-sm text-red-600">
              <AlertCircle className="w-4 h-4 mr-2" />
              <span>Insufficient balance (${user?.balance} / ${betAmount} required)</span>
            </div>
          </div>
        )}

        <JoinRoomButton
          roomId={room.id}
          roomName={room.name}
          betAmount={room.config?.betAmount || room.betAmount}
          userBalance={user?.balance || 0}
          currentPlayers={currentPlayers}
          maxPlayers={maxPlayers}
          roomStatus={room.status}
          isJoining={isCurrentlyJoining}
          canJoin={canJoin}
          isPrivate={room.isPrivate}
          onJoin={onJoin}
          size="md"
          showTooltip={true}
          className="w-full"
        />
      </div>


    </div>
  );
};

export default RoomCard;
