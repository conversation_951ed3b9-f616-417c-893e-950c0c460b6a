import React, { useState, useEffect } from 'react';
import { History, TrendingUp, TrendingDown, RefreshCw, Clock, CheckCircle, XCircle } from 'lucide-react';
import { useGameStore } from '../../store/gameStore';
import { useAuthStore } from '../../store/authStore';
import { apiClient } from '../../services/api';

interface TransactionHistoryProps {
  maxItems?: number;
  showRoomTransactionsOnly?: boolean;
  roomId?: string;
}

export const TransactionHistory: React.FC<TransactionHistoryProps> = ({
  maxItems = 10,
  showRoomTransactionsOnly = false,
  roomId,
}) => {
  const { prizePoolData } = useGameStore();
  const { user } = useAuthStore();
  
  const [transactions, setTransactions] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (user) {
      loadTransactions();
    }
  }, [user, roomId, showRoomTransactionsOnly]);

  const loadTransactions = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const response = await apiClient.getTransactions({
        page: 1,
        limit: maxItems,
      });

      let filteredTransactions = response.transactions;

      // Filter by room if specified
      if (showRoomTransactionsOnly && roomId) {
        filteredTransactions = filteredTransactions.filter(
          (tx: any) => tx.metadata?.room_id === roomId || tx.roomId === roomId
        );
      }

      // Combine with local transaction history
      const localTransactions = prizePoolData.transactionHistory;
      const combinedTransactions = [...localTransactions, ...filteredTransactions]
        .sort((a, b) => new Date(b.created_at || b.createdAt).getTime() - new Date(a.created_at || a.createdAt).getTime())
        .slice(0, maxItems);

      setTransactions(combinedTransactions);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load transactions';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'prize_win':
      case 'WIN':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'entry_fee':
      case 'ready_fee':
      case 'BET':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      case 'refund':
      case 'REFUND':
        return <RefreshCw className="w-4 h-4 text-blue-600" />;
      case 'deposit':
      case 'DEPOSIT':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'withdrawal':
      case 'WITHDRAWAL':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <History className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'prize_win':
      case 'WIN':
      case 'deposit':
      case 'DEPOSIT':
        return 'text-green-600';
      case 'entry_fee':
      case 'ready_fee':
      case 'BET':
      case 'withdrawal':
      case 'WITHDRAWAL':
        return 'text-red-600';
      case 'refund':
      case 'REFUND':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
      case 'COMPLETED':
        return <CheckCircle className="w-3 h-3 text-green-600" />;
      case 'pending':
      case 'PENDING':
        return <Clock className="w-3 h-3 text-yellow-600" />;
      case 'failed':
      case 'FAILED':
      case 'cancelled':
      case 'CANCELLED':
        return <XCircle className="w-3 h-3 text-red-600" />;
      default:
        return <Clock className="w-3 h-3 text-gray-600" />;
    }
  };

  const formatTransactionType = (type: string) => {
    switch (type) {
      case 'prize_win':
      case 'WIN':
        return 'Prize Win';
      case 'entry_fee':
      case 'ready_fee':
      case 'BET':
        return 'Entry Fee';
      case 'refund':
      case 'REFUND':
        return 'Refund';
      case 'deposit':
      case 'DEPOSIT':
        return 'Deposit';
      case 'withdrawal':
      case 'WITHDRAWAL':
        return 'Withdrawal';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  const formatAmount = (amount: number, type: string) => {
    const isPositive = ['prize_win', 'WIN', 'refund', 'REFUND', 'deposit', 'DEPOSIT'].includes(type);
    const sign = isPositive ? '+' : '-';
    return `${sign}$${Math.abs(amount).toFixed(2)}`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-2 mb-4">
          <History className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Transaction History</h3>
        </div>
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading transactions...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <History className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            {showRoomTransactionsOnly ? 'Room Transactions' : 'Transaction History'}
          </h3>
        </div>
        <button
          onClick={loadTransactions}
          disabled={loading}
          className="text-sm text-blue-600 hover:text-blue-800 disabled:opacity-50"
        >
          <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <div className="flex items-center gap-2">
            <XCircle className="w-4 h-4 text-red-600" />
            <span className="text-sm text-red-800">{error}</span>
          </div>
        </div>
      )}

      {transactions.length === 0 ? (
        <div className="text-center py-8">
          <History className="w-12 h-12 text-gray-300 mx-auto mb-3" />
          <p className="text-gray-500">No transactions found</p>
        </div>
      ) : (
        <div className="space-y-3">
          {transactions.map((transaction, index) => (
            <div
              key={transaction.id || index}
              className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center gap-3">
                {getTransactionIcon(transaction.type)}
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-900">
                      {formatTransactionType(transaction.type)}
                    </span>
                    {getStatusIcon(transaction.status)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {transaction.description}
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(transaction.created_at || transaction.createdAt).toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className={`font-semibold ${getTransactionColor(transaction.type)}`}>
                  {formatAmount(transaction.amount, transaction.type)}
                </div>
                {transaction.metadata?.room_id && (
                  <div className="text-xs text-gray-500">
                    Room: {transaction.metadata.room_id.slice(-6)}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {transactions.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="text-xs text-gray-500 text-center">
            Showing {transactions.length} most recent transactions
          </div>
        </div>
      )}
    </div>
  );
};

export default TransactionHistory;
