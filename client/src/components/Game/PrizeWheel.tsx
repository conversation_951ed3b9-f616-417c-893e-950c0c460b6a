import React, { useState, useEffect, useRef } from 'react';
import { WheelColor } from '../../types/socket';
import { DEFAULT_WHEEL_COLORS, WHEEL_CONFIG } from '../../constants/wheelColors';





export interface ReadyPlayer {
  userId: string;
  username: string;
  colorId: string;
  colorHex: string;
}

interface PrizeWheelProps {
  availableColors: WheelColor[];
  selectedColor?: string;
  onColorSelect: (colorId: string) => void;
  isSpinning: boolean;
  finalAngle?: number;
  winnerColor?: string;
  disabled?: boolean;
  size?: number;
  readyPlayers?: ReadyPlayer[]; // Ready players with their colors
  playersWithColors?: ReadyPlayer[]; // All players with color selections (for immediate visual feedback)
  currentUserId?: string; // Current user ID for optimistic updates
  currentUserName?: string; // Current user name for optimistic updates
}

interface WheelSegment {
  color: WheelColor;
  startAngle: number;
  endAngle: number;
  centerAngle: number;
  playerId?: string; // Track which player this segment belongs to
  playerName?: string;
  isReady?: boolean; // Track if this player is ready
}

export const PrizeWheel: React.FC<PrizeWheelProps> = ({
  availableColors,
  selectedColor,
  onColorSelect,
  isSpinning,
  finalAngle = 0,
  winnerColor,
  disabled = false,
  size = WHEEL_CONFIG.DEFAULT_SIZE, // Use the enhanced default size
  readyPlayers = [],
  playersWithColors = [],
  currentUserId,
  currentUserName,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [currentAngle, setCurrentAngle] = useState(0);
  const [animationId, setAnimationId] = useState<number | null>(null);
  const [segments, setSegments] = useState<WheelSegment[]>([]);
  const [wheelState, setWheelState] = useState<'empty' | 'segmented'>('empty');

  // Reset wheel state when key props change (room change, etc.)
  useEffect(() => {
    setCurrentAngle(0);
    setSegments([]);
    setWheelState('empty');
    if (animationId) {
      cancelAnimationFrame(animationId);
      setAnimationId(null);
    }
  }, [currentUserId, currentUserName]); // Reset when user context changes

  // Calculate wheel segments - show colors immediately when selected, full segments when ready
  useEffect(() => {
    // Calculate segments for immediate visual feedback

    // Start with playersWithColors, but add current user's selection optimistically if not already included
    let displayPlayers = [...playersWithColors];

    // If current user has selected a color but isn't in playersWithColors yet, add them optimistically
    if (selectedColor && currentUserId && currentUserName) {
      const currentUserInList = displayPlayers.find(p => p.userId === currentUserId);
      if (!currentUserInList) {
        // Add current user optimistically
        const colorHex = availableColors.find(c => c.id === selectedColor)?.hex || '#000000';
        displayPlayers.push({
          userId: currentUserId,
          username: currentUserName,
          colorId: selectedColor,
          colorHex: colorHex,
        });
      } else if (currentUserInList.colorId !== selectedColor) {
        // Update current user's color optimistically
        currentUserInList.colorId = selectedColor;
        currentUserInList.colorHex = availableColors.find(c => c.id === selectedColor)?.hex || '#000000';
      }
    }

    // Fallback to readyPlayers if no display players
    if (displayPlayers.length === 0) {
      displayPlayers = readyPlayers;
    }

    if (displayPlayers.length === 0) {
      // Empty wheel state - no segments
      setSegments([]);
      setWheelState('empty');
      return;
    }

    // Create segments for players with colors - divide wheel equally among players
    const totalPlayers = displayPlayers.length;
    const segmentAngle = 360 / totalPlayers; // Equal division of 360 degrees

    const newSegments: WheelSegment[] = displayPlayers.map((player, index) => {
      // Find the color data for this player
      const colorData = DEFAULT_WHEEL_COLORS.find(c => c.id === player.colorId) || {
        id: player.colorId,
        name: player.colorId,
        hex: player.colorHex,
      };

      // Check if this player is ready (for visual indicators)
      const isPlayerReady = readyPlayers.some(rp => rp.userId === player.userId);

      const segment = {
        color: {
          ...colorData,
          hex: player.colorHex,
          isAvailable: true,
          selectedBy: player.userId,
        },
        startAngle: index * segmentAngle,
        endAngle: (index + 1) * segmentAngle,
        centerAngle: index * segmentAngle + segmentAngle / 2,
        playerId: player.userId,
        playerName: player.username,
        isReady: isPlayerReady, // Track ready state for visual indicators
      };

      return segment;
    });

    // No placeholder segments - only show actual player segments for clean division
    setSegments(newSegments);
    setWheelState('segmented');
  }, [readyPlayers, playersWithColors, selectedColor, currentUserId, currentUserName, availableColors]);

  // Draw wheel on canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const centerX = size / 2;
    const centerY = size / 2;
    const radius = (size - WHEEL_CONFIG.SEGMENT_BORDER_WIDTH * 2) / 2;

    // Clear canvas
    ctx.clearRect(0, 0, size, size);

    if (wheelState === 'empty') {
      // Draw beautiful empty wheel with clean design

      // Outer decorative ring with subtle gradient
      const outerRingGradient = ctx.createRadialGradient(centerX, centerY, radius - WHEEL_CONFIG.OUTER_RING_WIDTH, centerX, centerY, radius);
      outerRingGradient.addColorStop(0, '#f1f5f9');
      outerRingGradient.addColorStop(0.5, '#e2e8f0');
      outerRingGradient.addColorStop(1, '#cbd5e1');

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.fillStyle = outerRingGradient;
      ctx.fill();

      // Main wheel surface with clean white gradient
      const mainGradient = ctx.createRadialGradient(centerX - radius * 0.2, centerY - radius * 0.2, 0, centerX, centerY, radius - WHEEL_CONFIG.OUTER_RING_WIDTH);
      mainGradient.addColorStop(0, '#ffffff');
      mainGradient.addColorStop(0.3, '#fefefe');
      mainGradient.addColorStop(0.7, '#f8fafc');
      mainGradient.addColorStop(1, '#f1f5f9');

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius - WHEEL_CONFIG.OUTER_RING_WIDTH, 0, 2 * Math.PI);
      ctx.fillStyle = mainGradient;
      ctx.fill();

      // Add subtle inner border
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius - WHEEL_CONFIG.OUTER_RING_WIDTH, 0, 2 * Math.PI);
      ctx.strokeStyle = 'rgba(203, 213, 225, 0.5)';
      ctx.lineWidth = 1;
      ctx.stroke();

      // Draw clean empty state text
      ctx.fillStyle = '#64748b';
      ctx.font = 'bold 16px system-ui, -apple-system, sans-serif';
      ctx.textAlign = 'center';
      ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
      ctx.shadowBlur = 2;
      ctx.shadowOffsetY = 1;
      ctx.fillText('Select Colors', centerX, centerY - 8);
      ctx.fillText('to Begin', centerX, centerY + 12);
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
      ctx.shadowOffsetY = 0;
    } else {
      // Draw premium segmented wheel with enhanced visual effects

      // Draw clean outer decorative ring for segmented wheel
      const outerRingGradient = ctx.createRadialGradient(centerX, centerY, radius - WHEEL_CONFIG.OUTER_RING_WIDTH, centerX, centerY, radius);
      outerRingGradient.addColorStop(0, '#374151');
      outerRingGradient.addColorStop(0.5, '#4b5563');
      outerRingGradient.addColorStop(1, '#6b7280');

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
      ctx.fillStyle = outerRingGradient;
      ctx.fill();

      // Save context for rotation
      ctx.save();
      ctx.translate(centerX, centerY);
      ctx.rotate((currentAngle * Math.PI) / 180);

      // Draw segments with premium metallic effects
      segments.forEach((segment) => {
        const startAngle = (segment.startAngle * Math.PI) / 180;
        const endAngle = (segment.endAngle * Math.PI) / 180;
        const segmentRadius = radius - WHEEL_CONFIG.OUTER_RING_WIDTH;

        // Draw main segment with metallic gradient
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.arc(0, 0, segmentRadius, startAngle, endAngle);
        ctx.closePath();

        // Create enhanced gradient for segment fill
        const isWinner = segment.color.id === winnerColor;

        let baseColor = segment.color.hex;
        if (isWinner) {
          baseColor = '#fbbf24'; // Gold for winner
        }

        // Use solid color with subtle gradient for clean appearance
        if (isWinner) {
          // Gold gradient for winner
          const winnerGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, segmentRadius);
          winnerGradient.addColorStop(0, '#ffd700');
          winnerGradient.addColorStop(0.5, '#ffed4e');
          winnerGradient.addColorStop(1, '#f59e0b');
          ctx.fillStyle = winnerGradient;
        } else {
          // Clean solid color with subtle inner highlight
          ctx.fillStyle = baseColor;
        }
        ctx.fill();

        // Add subtle inner highlight for depth
        if (!isWinner) {
          const innerHighlight = ctx.createRadialGradient(
            Math.cos((startAngle + endAngle) / 2) * segmentRadius * 0.3,
            Math.sin((startAngle + endAngle) / 2) * segmentRadius * 0.3,
            0,
            0, 0, segmentRadius * 0.8
          );
          innerHighlight.addColorStop(0, 'rgba(255, 255, 255, 0.2)');
          innerHighlight.addColorStop(0.7, 'rgba(255, 255, 255, 0.05)');
          innerHighlight.addColorStop(1, 'rgba(255, 255, 255, 0)');

          ctx.beginPath();
          ctx.moveTo(0, 0);
          ctx.arc(0, 0, segmentRadius, startAngle, endAngle);
          ctx.closePath();
          ctx.fillStyle = innerHighlight;
          ctx.fill();
        }

        // Add clean segment borders
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(Math.cos(startAngle) * segmentRadius, Math.sin(startAngle) * segmentRadius);
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = 2;
        ctx.stroke();

        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(Math.cos(endAngle) * segmentRadius, Math.sin(endAngle) * segmentRadius);
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
        ctx.lineWidth = 2;
        ctx.stroke();



        // Draw player name with intelligent sizing and positioning
        if (segment.playerName) {
          ctx.save();
          ctx.rotate(segment.centerAngle * Math.PI / 180);

          // Calculate available space for text based on segment size
          const segmentAngleRad = ((segment.endAngle - segment.startAngle) * Math.PI) / 180;
          const availableWidth = segmentRadius * 0.8; // Maximum radial distance for text
          const maxTextWidth = Math.sin(segmentAngleRad / 2) * segmentRadius * 1.4; // Arc width at text position

          // Dynamic font sizing based on available space and text length
          let fontSize = 18; // Start with larger font
          let displayText = segment.playerName;
          let textMetrics;

          // Find optimal font size that fits within segment boundaries
          do {
            ctx.font = `bold ${fontSize}px system-ui, -apple-system, sans-serif`;
            textMetrics = ctx.measureText(displayText);

            // If text is too wide, try smaller font first
            if (textMetrics.width > maxTextWidth && fontSize > 10) {
              fontSize -= 1;
            } else if (textMetrics.width > maxTextWidth) {
              // If minimum font size reached, truncate text with ellipsis
              let truncatedText = displayText;
              while (textMetrics.width > maxTextWidth && truncatedText.length > 3) {
                truncatedText = truncatedText.slice(0, -1);
                displayText = truncatedText + '...';
                textMetrics = ctx.measureText(displayText);
              }
              break;
            } else {
              break;
            }
          } while (fontSize > 8); // Minimum readable font size

          // Position text optimally within segment
          const textX = availableWidth * 0.75; // Slightly closer to edge for better visibility
          const textY = fontSize * 0.3; // Adjust vertical position based on font size

          ctx.textAlign = 'center';

          // Draw text with black outline for readability (no background)
          ctx.strokeStyle = 'rgba(0, 0, 0, 0.9)';
          ctx.lineWidth = Math.max(2, fontSize * 0.15);
          ctx.lineJoin = 'round';
          ctx.strokeText(displayText, textX, textY);

          // Draw main text in white
          ctx.fillStyle = '#ffffff';
          ctx.fillText(displayText, textX, textY);

          ctx.restore();
        }
      });

      // Restore context
      ctx.restore();
    }



    // Draw prominent arrow indicator outside the wheel - ALWAYS VISIBLE
    ctx.save();
    ctx.translate(centerX, centerY - radius - 60);

    // Draw a large, highly visible arrow
    const arrowSize = 40;

    // Draw shadow first
    ctx.save();
    ctx.translate(4, 4);
    ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
    ctx.beginPath();
    ctx.moveTo(0, arrowSize);
    ctx.lineTo(-arrowSize/2, 0);
    ctx.lineTo(-arrowSize/4, 0);
    ctx.lineTo(-arrowSize/4, -arrowSize/2);
    ctx.lineTo(arrowSize/4, -arrowSize/2);
    ctx.lineTo(arrowSize/4, 0);
    ctx.lineTo(arrowSize/2, 0);
    ctx.closePath();
    ctx.fill();
    ctx.restore();

    // Draw main arrow in bright red
    ctx.fillStyle = '#ff0000';
    ctx.beginPath();
    ctx.moveTo(0, arrowSize);
    ctx.lineTo(-arrowSize/2, 0);
    ctx.lineTo(-arrowSize/4, 0);
    ctx.lineTo(-arrowSize/4, -arrowSize/2);
    ctx.lineTo(arrowSize/4, -arrowSize/2);
    ctx.lineTo(arrowSize/4, 0);
    ctx.lineTo(arrowSize/2, 0);
    ctx.closePath();
    ctx.fill();

    // Add thick white border for maximum visibility
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 4;
    ctx.stroke();

    // Add inner black border for definition
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.stroke();

    ctx.restore();

  }, [segments, currentAngle, selectedColor, winnerColor, size, wheelState]);

  // Handle enhanced spinning animation with premium easing
  useEffect(() => {
    if (isSpinning) {
      const startTime = Date.now();
      const duration = WHEEL_CONFIG.SPIN_DURATION;
      const startAngle = currentAngle;
      const totalRotation = 2160 + finalAngle; // 6 full rotations + final position for more dramatic effect

      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Enhanced easing function with more realistic physics
        // Combines ease-out with a slight bounce at the end
        let easeOut;
        if (progress < 0.8) {
          // Main spinning phase with smooth deceleration
          easeOut = 1 - Math.pow(1 - (progress / 0.8), 4);
        } else {
          // Final settling phase with slight oscillation
          const finalPhase = (progress - 0.8) / 0.2;
          const baseEase = 1 - Math.pow(1 - 1, 4); // Complete the main ease
          const oscillation = Math.sin(finalPhase * Math.PI * 3) * 0.02 * (1 - finalPhase);
          easeOut = baseEase + oscillation;
        }

        const newAngle = startAngle + totalRotation * easeOut;
        setCurrentAngle(newAngle % 360);

        if (progress < 1) {
          const id = requestAnimationFrame(animate);
          setAnimationId(id);
        } else {
          setAnimationId(null);
        }
      };

      const id = requestAnimationFrame(animate);
      setAnimationId(id);
    }

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
        setAnimationId(null);
      }
    };
  }, [isSpinning, finalAngle]);

  // Handle color selection click
  const handleCanvasClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (disabled || isSpinning) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    // Calculate distance from center
    const distance = Math.sqrt(x * x + y * y);
    const radius = size / 2 - WHEEL_CONFIG.OUTER_RING_WIDTH;

    // Check if click is within wheel
    if (distance > radius) return;

    // For empty wheel, only allow clicking if user hasn't selected a color yet
    if (wheelState === 'empty' && availableColors.length > 0) {
      // Only allow selection if user hasn't already selected a color
      if (!selectedColor) {
        const firstAvailable = availableColors.find(color => color.isAvailable);
        if (firstAvailable) {
          onColorSelect(firstAvailable.id);
        }
      }
      return;
    }

    // For segmented wheel, only allow clicking on available colors (not already taken)
    if (wheelState === 'segmented' && segments.length > 0) {
      // Calculate angle of click (convert from canvas coordinates to wheel angle)
      let angle = Math.atan2(y, x) * 180 / Math.PI;
      // Convert to 0-360 range starting from top (12 o'clock position)
      angle = (angle + 90 + 360) % 360;

      // Adjust for current rotation
      angle = (angle - currentAngle + 360) % 360;

      // Find which segment was clicked
      const clickedSegment = segments.find(segment => {
        let startAngle = segment.startAngle;
        let endAngle = segment.endAngle;

        // Handle segments that cross 0 degrees
        if (startAngle > endAngle) {
          return angle >= startAngle || angle < endAngle;
        } else {
          return angle >= startAngle && angle < endAngle;
        }
      });

      // Only allow selection if:
      // 1. Segment exists
      // 2. It's not the current user's already selected color (prevent reselecting same color)
      // 3. Either the segment is empty OR it belongs to current user (allow changing colors)
      if (clickedSegment &&
          clickedSegment.color.id !== selectedColor &&
          (!clickedSegment.playerId || clickedSegment.playerId === currentUserId)) {
        onColorSelect(clickedSegment.color.id);
      }
    }
  };

  return (
    <div className="flex flex-col items-center space-y-8">
      <div className="relative">
        {/* Enhanced outer glow effect with multiple layers */}
        <div className={`absolute inset-0 rounded-full transition-all duration-700 ${
          wheelState === 'segmented'
            ? 'shadow-2xl shadow-purple-500/30'
            : 'shadow-xl shadow-gray-400/25'
        }`} style={{ transform: 'scale(1.03)' }} />

        {/* Additional glow layer for premium effect */}
        <div className={`absolute inset-0 rounded-full transition-all duration-500 ${
          wheelState === 'segmented'
            ? 'shadow-lg shadow-blue-400/20'
            : 'shadow-md shadow-gray-300/20'
        }`} style={{ transform: 'scale(1.01)' }} />

        <canvas
          ref={canvasRef}
          width={size}
          height={size}
          onClick={handleCanvasClick}
          className={`
            relative rounded-full transition-all duration-300 transform
            ${disabled || isSpinning
              ? 'cursor-not-allowed'
              : 'cursor-pointer hover:shadow-2xl hover:scale-[1.02]'
            }
            ${wheelState === 'empty'
              ? 'shadow-lg'
              : 'shadow-xl'
            }
            ${isSpinning ? 'animate-pulse' : ''}
          `}
          style={{
            filter: disabled ? 'grayscale(50%) brightness(0.8)' : 'none',
            transition: 'all 0.3s ease',
            background: 'transparent',
          }}
        />

        {isSpinning && (
          <div className="absolute inset-0 flex items-center justify-center rounded-full bg-black bg-opacity-40 backdrop-blur-sm">
            <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 text-white px-8 py-4 rounded-2xl font-bold text-xl shadow-2xl animate-bounce border border-white/20">
              <div className="flex items-center space-x-2">
                <span className="animate-spin">🎯</span>
                <span>SPINNING...</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced winner announcement with premium styling */}
      {winnerColor && !isSpinning && wheelState === 'segmented' && (
        <div className="flex items-center justify-center space-x-4 bg-gradient-to-r from-yellow-50 via-amber-50 to-orange-50 border-2 border-yellow-400 rounded-2xl px-8 py-4 shadow-2xl animate-pulse">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div
                className="w-8 h-8 rounded-full border-3 border-white shadow-lg"
                style={{ backgroundColor: segments.find(s => s.color.id === winnerColor)?.color.hex }}
              />
              <div className="absolute inset-0 w-8 h-8 rounded-full border-2 border-yellow-400 animate-ping" />
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-2xl animate-bounce">🎉</span>
              <span className="text-yellow-900 font-bold text-xl">
                {segments.find(s => s.color.id === winnerColor)?.playerName || segments.find(s => s.color.id === winnerColor)?.color.name} Wins!
              </span>
              <span className="text-2xl animate-bounce" style={{ animationDelay: '0.1s' }}>🏆</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PrizeWheel;
