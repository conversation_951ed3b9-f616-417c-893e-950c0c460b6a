import React, { useState, useEffect } from 'react';
import { Button } from '../UI/Button';
import { MapPin, AlertCircle, Users, Grid3X3, Target, Clock } from 'lucide-react';
import { useSocketStore } from '@/store/socketStore';
import { useGameStore } from '@/store/gameStore';
import { useAuthStore } from '@/store/authStore';
import { useAmidakujiRealTimeData } from '@/hooks/useAmidakujiRealTimeData';
// Note: Using console.log for now instead of toast notifications

interface AmidakujiReadySectionProps {
  roomId: string;
  currentUserId: string;
  className?: string;
}

export const AmidakujiReadySection: React.FC<AmidakujiReadySectionProps> = ({
  roomId,
  currentUserId,
  className = '',
}) => {
  const [selectedPosition, setSelectedPosition] = useState<number | null>(null);
  const [isTogglingReady, setIsTogglingReady] = useState(false);
  const [isSelectingPosition, setIsSelectingPosition] = useState(false);
  const [gamePhase, setGamePhase] = useState<'waiting' | 'countdown' | 'playing' | 'results'>('waiting');
  const [countdown, setCountdown] = useState(10);

  const { setPlayerReady, selectAmidakujiPosition, currentRoom, currentGame } = useSocketStore();
  const { playerReadyStates } = useGameStore();
  const { user } = useAuthStore();

  // Get current user's ready state from the store instead of local state
  const isReady = playerReadyStates[currentUserId] || false;

  // Use the real-time data hook for Amidakuji
  const {
    getTakenPositions,
    getPlayerPositions,
    getPlayerBalance,
    getPlayerInsufficientBalance,
  } = useAmidakujiRealTimeData();

  // Get real-time data
  const takenPositions = getTakenPositions();
  const playerPositions = getPlayerPositions();

  // Get current user's selected position from real-time data
  const currentUserPosition = playerPositions[currentUserId];

  // Update local selected position when real-time data changes
  useEffect(() => {
    if (currentUserPosition !== undefined && currentUserPosition !== selectedPosition) {
      setSelectedPosition(currentUserPosition);
    }
  }, [currentUserPosition, selectedPosition]);

  const maxPlayers = currentRoom?.maxPlayers || 8;
  const canSelectPosition = gamePhase === 'waiting' && !isReady;
  const canMarkReady = selectedPosition !== null && selectedPosition >= 0 && !isTogglingReady;

  // Handle position selection with real-time sync
  const handlePositionSelect = async (position: number) => {
    if (takenPositions[position] || isSelectingPosition || !canSelectPosition) {
      return;
    }

    setIsSelectingPosition(true);

    try {
      await selectAmidakujiPosition(roomId, position);
      setSelectedPosition(position);
      console.log(`Position ${position + 1} selected!`);
    } catch (error: any) {
      console.error('Failed to select position:', error);

      let errorMessage = 'Failed to select position';
      if (error.code === 'POSITION_TAKEN') {
        errorMessage = 'This position is already taken by another player';
      } else if (error.code === 'GAME_IN_PROGRESS') {
        errorMessage = 'Cannot change position while game is in progress';
      } else if (error.message) {
        errorMessage = error.message;
      }

      console.error(errorMessage);
    } finally {
      setIsSelectingPosition(false);
    }
  };

  // Handle ready toggle with balance validation
  const handleToggleReady = async () => {
    if (!selectedPosition && selectedPosition !== 0) {
      console.error('Please select a position first');
      return;
    }

    // Prevent multiple simultaneous ready toggle requests
    if (isTogglingReady) {
      return;
    }

    // Check if user has sufficient balance using real-time data
    const currentUserBalance = getPlayerBalance(currentUserId);
    const userBalance = currentUserBalance !== undefined ? currentUserBalance : user?.balance || 0;
    const betAmount = (currentRoom?.betAmount || 0) / 100; // Convert cents to dollars

    if (userBalance < betAmount) {
      console.error(`Insufficient balance. You need $${betAmount.toFixed(2)} but only have $${userBalance.toFixed(2)}`);
      return;
    }

    setIsTogglingReady(true);

    try {
      await setPlayerReady(roomId, !isReady);
      // Don't update local state - let the socket event update the store
      console.log(isReady ? 'Marked as not ready' : 'Marked as ready!');
    } catch (error: any) {
      console.error('Failed to set ready state:', error);

      // Enhanced error handling with detailed balance information
      let errorMessage = 'Failed to update ready status';

      if (error.code === 'INSUFFICIENT_BALANCE') {
        const requiredAmount = betAmount.toFixed(2);
        const currentBalance = userBalance.toFixed(2);
        errorMessage = `Insufficient balance. Required: $${requiredAmount}, Available: $${currentBalance}`;
      } else if (error.code === 'POSITION_NOT_SELECTED') {
        errorMessage = 'Please select a position before marking ready';
      } else if (error.code === 'GAME_IN_PROGRESS') {
        errorMessage = 'Cannot change ready status while game is in progress';
      } else if (error.message) {
        errorMessage = error.message;
      }

      console.error(errorMessage);
    } finally {
      setIsTogglingReady(false);
    }
  };

  // Reset all state when entering a new room
  useEffect(() => {
    if (currentRoom) {
      // Reset all local state to sync with new room
      setSelectedPosition(null);
      setGamePhase('waiting');
      setCountdown(10);
      setIsSelectingPosition(false);

      // Log room change for debugging
      console.log('Amidakuji room changed - resetting local state:', {
        roomId: currentRoom.id,
        roomName: currentRoom.name,
        playerCount: currentRoom.playerCount,
        gameType: currentRoom.gameType,
      });
    }
  }, [currentRoom?.id]); // Only trigger when room ID changes

  // Update state based on socket events and room info
  useEffect(() => {
    // Handle room status and countdown from room_info_updated event
    if (currentRoom) {
      switch (currentRoom.status) {
        case 'STARTING':
          setGamePhase('countdown');
          // Get countdown from room state if available
          const roomCountdown = (currentRoom as any).countdown;
          if (roomCountdown !== undefined && roomCountdown !== null) {
            setCountdown(roomCountdown);
          }
          break;
        case 'PLAYING':
          setGamePhase('playing');
          break;
        case 'FINISHED':
          setGamePhase('results');
          break;
        default:
          setGamePhase('waiting');
      }
    }
  }, [currentRoom?.status, currentRoom]);

  // Handle countdown updates from socket events
  useEffect(() => {
    if (currentGame?.data?.countdown !== undefined) {
      setCountdown(currentGame.data.countdown);
    }
  }, [currentGame?.data?.countdown]);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Position Selection */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-2 mb-4">
          <Grid3X3 className="w-5 h-5 text-purple-600" />
          <h3 className="text-lg font-semibold text-gray-900">Select Your Position</h3>
        </div>

        {selectedPosition === null || !isReady ? (
          <div className="space-y-4">
            <div className="grid grid-cols-4 gap-3">
              {Array.from({ length: maxPlayers }, (_, i) => i).map((position) => {
                const isTaken = takenPositions[position] || false;
                const isSelected = selectedPosition === position;
                const isDisabled = isTaken || !canSelectPosition || isSelectingPosition;

                return (
                  <Button
                    key={position}
                    onClick={() => handlePositionSelect(position)}
                    disabled={isDisabled}
                    variant={isSelected ? 'default' : 'outline'}
                    className={`
                      h-12 flex flex-col items-center justify-center
                      ${isTaken
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : isSelected
                          ? 'bg-purple-600 text-white border-purple-600'
                          : 'hover:bg-purple-50 hover:border-purple-300'
                      }
                    `}
                  >
                    <MapPin className="w-4 h-4 mb-1" />
                    <span className="text-xs font-medium">
                      {position + 1}
                    </span>
                  </Button>
                );
              })}
            </div>

            <div className="text-sm text-gray-600">
              <p>Choose your starting position on the Amidakuji ladder. Each position leads to a different prize!</p>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2">
              <MapPin className="w-5 h-5 text-green-600" />
              <span className="font-medium text-green-800">
                Position Selected: {selectedPosition + 1}
              </span>
            </div>
            {!isReady && (
              <Button
                onClick={() => setSelectedPosition(null)}
                variant="outline"
                size="sm"
                disabled={isSelectingPosition}
              >
                Change Position
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Ready Button */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center gap-2 mb-4">
          <Users className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Ready Status</h3>
        </div>

        {gamePhase === 'countdown' && (
          <div className="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
            <div className="flex items-center gap-2 text-orange-800">
              <Clock className="w-5 h-5" />
              <span className="font-medium">Game starting in {countdown} seconds...</span>
            </div>
          </div>
        )}

        {gamePhase === 'playing' && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center gap-2 text-blue-800">
              <Target className="w-5 h-5" />
              <span className="font-medium">Game in progress...</span>
            </div>
          </div>
        )}

        {gamePhase === 'results' && (
          <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-800">
              <Target className="w-5 h-5" />
              <span className="font-medium">Game finished! Check results below.</span>
            </div>
          </div>
        )}

        <Button
          onClick={handleToggleReady}
          disabled={!canMarkReady || gamePhase !== 'waiting'}
          className={`w-full ${
            isReady
              ? 'bg-green-600 hover:bg-green-700 text-white'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          } ${isTogglingReady ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          {isTogglingReady
            ? (isReady ? 'Marking Not Ready...' : 'Marking Ready...')
            : (isReady ? '✓ Ready!' : 'Mark Ready')
          }
        </Button>

        {!canMarkReady && !isReady && gamePhase === 'waiting' && (
          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-yellow-800">
              <AlertCircle className="w-4 h-4" />
              <span>Please select a position before marking ready</span>
            </div>
          </div>
        )}

        {gamePhase !== 'waiting' && (
          <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <AlertCircle className="w-4 h-4" />
              <span>Cannot change ready status during game</span>
            </div>
          </div>
        )}
      </div>

      {/* Player Status */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <h3 className="font-semibold text-gray-900 mb-3">
          Players ({currentRoom?.playerCount || 0}/{currentRoom?.maxPlayers || maxPlayers})
        </h3>
        <div className="space-y-2">
          {/* Current User */}
          <div className="flex items-center justify-between p-3 rounded-lg border bg-blue-50 border-blue-200">
            <div className="flex items-center gap-3">
              <div className="w-4 h-4 rounded-full bg-blue-600"></div>
              <div>
                <span className="font-medium text-blue-800">You</span>
                {selectedPosition !== null && (
                  <div className="text-xs text-blue-600">Position {selectedPosition + 1}</div>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getPlayerInsufficientBalance(currentUserId) && (
                <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                  Low Balance
                </span>
              )}
              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                {isReady ? 'Ready' : 'Not Ready'}
              </span>
            </div>
          </div>

          {/* Other Players */}
          {currentRoom?.players?.filter(player => player.userId !== currentUserId).map((player) => {
            const playerPosition = playerPositions[player.userId];
            const playerReady = playerReadyStates[player.userId] || false;
            const hasInsufficientBalance = getPlayerInsufficientBalance(player.userId);

            return (
              <div key={player.userId} className="flex items-center justify-between p-3 rounded-lg border bg-gray-50 border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="w-4 h-4 rounded-full bg-gray-400"></div>
                  <div>
                    <span className="font-medium text-gray-800">{player.username}</span>
                    {playerPosition !== undefined && (
                      <div className="text-xs text-gray-600">Position {playerPosition + 1}</div>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {hasInsufficientBalance && (
                    <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">
                      Low Balance
                    </span>
                  )}
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    playerReady
                      ? 'bg-green-100 text-green-700'
                      : 'bg-gray-100 text-gray-700'
                  }`}>
                    {playerReady ? 'Ready' : 'Not Ready'}
                  </span>
                </div>
              </div>
            );
          })}
        </div>

        {/* Room Status Summary */}
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 space-y-1">
            <div className="flex justify-between">
              <span>Ready Players:</span>
              <span className="font-medium">{currentRoom?.readyCount || 0}/{currentRoom?.playerCount || 0}</span>
            </div>
            <div className="flex justify-between">
              <span>Bet Amount:</span>
              <span className="font-medium">${((currentRoom?.betAmount || 0) / 100).toFixed(2)}</span>
            </div>
            <div className="flex justify-between">
              <span>Prize Pool:</span>
              <span className="font-medium text-green-600">${((currentRoom?.prizePool || 0) / 100).toFixed(2)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Game Rules */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-2">Amidakuji Rules</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p>• Select your starting position at the top of the ladder</p>
          <p>• Follow the path down, taking horizontal lines when you encounter them</p>
          <p>• Your final position at the bottom determines your prize</p>
          <p>• Only one position will win the entire prize pool!</p>
          <p>• The game map is generated when players are ready</p>
        </div>
      </div>
    </div>
  );
};

export default AmidakujiReadySection;
