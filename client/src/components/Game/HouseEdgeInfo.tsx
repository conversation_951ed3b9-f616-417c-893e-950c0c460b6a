import React, { useState } from 'react';
import { Info, TrendingDown, DollarSign, Percent, HelpCircle, ChevronDown, ChevronUp } from 'lucide-react';

interface HouseEdgeInfoProps {
  houseEdgePercentage: number;
  totalPool: number;
  houseEdgeAmount: number;
  netPrizeAmount: number;
  playerCount: number;
  entryFee: number;
  compact?: boolean;
  className?: string;
}

export const HouseEdgeInfo: React.FC<HouseEdgeInfoProps> = ({
  houseEdgePercentage,
  totalPool,
  houseEdgeAmount,
  netPrizeAmount,
  playerCount,
  entryFee,
  compact = false,
  className = '',
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Calculate additional metrics
  const houseEdgePerPlayer = playerCount > 0 ? houseEdgeAmount / playerCount : 0;
  const effectiveReturnRate = ((netPrizeAmount / totalPool) * 100);
  const expectedValue = playerCount > 0 ? netPrizeAmount / playerCount : 0;

  if (compact) {
    return (
      <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Info className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-800">House Edge</span>
          </div>
          <div className="text-right">
            <div className="text-sm font-bold text-blue-900">
              {houseEdgePercentage}%
            </div>
            <div className="text-xs text-blue-700">
              ${houseEdgeAmount.toFixed(2)}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Header */}
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Info className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">House Edge Information</h3>
            <p className="text-sm text-gray-600">Understanding the game economics</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="text-right">
            <div className="text-lg font-bold text-blue-900">
              {houseEdgePercentage}%
            </div>
            <div className="text-sm text-gray-600">
              ${houseEdgeAmount.toFixed(2)}
            </div>
          </div>
          {isExpanded ? (
            <ChevronUp className="w-5 h-5 text-gray-400" />
          ) : (
            <ChevronDown className="w-5 h-5 text-gray-400" />
          )}
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-100 p-4">
          {/* Visual Breakdown */}
          <div className="mb-6">
            <h4 className="font-medium text-gray-900 mb-3">Prize Pool Breakdown</h4>
            
            {/* Visual Bar */}
            <div className="relative bg-gray-200 rounded-full h-8 mb-3 overflow-hidden">
              <div 
                className="absolute left-0 top-0 h-full bg-gradient-to-r from-green-500 to-green-600 flex items-center justify-center text-white text-sm font-medium"
                style={{ width: `${effectiveReturnRate}%` }}
              >
                Prize Pool
              </div>
              <div 
                className="absolute right-0 top-0 h-full bg-gradient-to-r from-red-500 to-red-600 flex items-center justify-center text-white text-sm font-medium"
                style={{ width: `${houseEdgePercentage}%` }}
              >
                House
              </div>
            </div>

            {/* Legend */}
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded"></div>
                <span className="text-gray-700">
                  Prize Pool: ${netPrizeAmount.toFixed(2)} ({effectiveReturnRate.toFixed(1)}%)
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded"></div>
                <span className="text-gray-700">
                  House Edge: ${houseEdgeAmount.toFixed(2)} ({houseEdgePercentage}%)
                </span>
              </div>
            </div>
          </div>

          {/* Detailed Metrics */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-green-50 rounded-lg p-4 border border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="w-4 h-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">Expected Value</span>
              </div>
              <div className="text-lg font-bold text-green-900">
                ${expectedValue.toFixed(2)}
              </div>
              <div className="text-xs text-green-700">Per player average</div>
            </div>

            <div className="bg-red-50 rounded-lg p-4 border border-red-200">
              <div className="flex items-center gap-2 mb-2">
                <TrendingDown className="w-4 h-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">House Take</span>
              </div>
              <div className="text-lg font-bold text-red-900">
                ${houseEdgePerPlayer.toFixed(2)}
              </div>
              <div className="text-xs text-red-700">Per player contribution</div>
            </div>
          </div>

          {/* Calculation Breakdown */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <h5 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
              <Percent className="w-4 h-4" />
              Calculation Breakdown
            </h5>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Total Entry Fees:</span>
                <span className="font-medium">{playerCount} × ${entryFee.toFixed(2)} = ${totalPool.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">House Edge ({houseEdgePercentage}%):</span>
                <span className="font-medium text-red-700">-${houseEdgeAmount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between border-t border-gray-300 pt-2">
                <span className="text-gray-900 font-medium">Net Prize Pool:</span>
                <span className="font-bold text-green-700">${netPrizeAmount.toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Fairness Information */}
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-start gap-3">
              <HelpCircle className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h5 className="font-medium text-blue-900 mb-2">Why House Edge?</h5>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• Covers operational costs and platform maintenance</p>
                  <p>• Ensures sustainable game operations</p>
                  <p>• Industry standard for fair gaming platforms</p>
                  <p>• Transparent and clearly disclosed to all players</p>
                </div>
              </div>
            </div>
          </div>

          {/* Player Odds */}
          <div className="mt-4 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <h5 className="font-medium text-yellow-900 mb-2">Your Odds</h5>
            <div className="text-sm text-yellow-800">
              <div className="flex justify-between mb-1">
                <span>Probability of winning:</span>
                <span className="font-medium">
                  {playerCount > 0 ? (1 / playerCount * 100).toFixed(1) : 0}% 
                  (1 in {playerCount})
                </span>
              </div>
              <div className="flex justify-between">
                <span>Potential return:</span>
                <span className="font-medium">
                  {entryFee > 0 ? (netPrizeAmount / entryFee).toFixed(2) : 0}x your entry fee
                </span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HouseEdgeInfo;
