import React, { useState } from 'react';
import { Users, Clock, Crown, Eye, EyeOff, ChevronDown, ChevronUp } from 'lucide-react';

interface PlayerColorAssignmentsProps {
  assignments: Record<string, {
    color: { hex: string; id: string; name: string; };
    selectedAt: string;
    userId: string;
    username: string;
  }>;
  selectedColors: Record<string, {
    selectedAt: string;
    userId: string;
    username: string;
  }>;
  currentUserId?: string;
  className?: string;
  compact?: boolean;
}

export const PlayerColorAssignments: React.FC<PlayerColorAssignmentsProps> = ({
  assignments,
  selectedColors,
  currentUserId,
  className = '',
  compact = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(!compact);
  const [showTimestamps, setShowTimestamps] = useState(false);

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatRelativeTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffMs = now.getTime() - time.getTime();
    const diffSecs = Math.floor(diffMs / 1000);
    const diffMins = Math.floor(diffSecs / 60);

    if (diffSecs < 60) return `${diffSecs}s ago`;
    if (diffMins < 60) return `${diffMins}m ago`;
    return formatTime(timestamp);
  };

  const sortedAssignments = Object.values(assignments).sort((a, b) => {
    // Current user first, then by selection time (newest first)
    if (a.userId === currentUserId && b.userId !== currentUserId) return -1;
    if (b.userId === currentUserId && a.userId !== currentUserId) return 1;
    return new Date(b.selectedAt).getTime() - new Date(a.selectedAt).getTime();
  });

  const assignmentCount = Object.keys(assignments).length;

  if (compact && !isExpanded) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-3 ${className}`}>
        <button
          onClick={() => setIsExpanded(true)}
          className="w-full flex items-center justify-between text-left hover:bg-gray-50 rounded p-1 transition-colors"
        >
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-gray-700">
              Player Assignments ({assignmentCount})
            </span>
          </div>
          <ChevronDown className="w-4 h-4 text-gray-400" />
        </button>
        
        {/* Compact Preview */}
        <div className="flex flex-wrap gap-1 mt-2">
          {sortedAssignments.slice(0, 6).map((assignment) => (
            <div
              key={assignment.userId}
              className="flex items-center gap-1 bg-gray-100 rounded px-2 py-1"
              title={`${assignment.username} - ${assignment.color.name}`}
            >
              <div
                className="w-3 h-3 rounded-full border border-gray-300"
                style={{ backgroundColor: assignment.color.hex }}
              />
              <span className={`text-xs ${assignment.userId === currentUserId ? 'font-bold text-blue-700' : 'text-gray-600'}`}>
                {assignment.username}
              </span>
            </div>
          ))}
          {assignmentCount > 6 && (
            <div className="text-xs text-gray-500 px-2 py-1">
              +{assignmentCount - 6} more
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Users className="w-5 h-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Player Color Assignments ({assignmentCount})
          </h3>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setShowTimestamps(!showTimestamps)}
            className="flex items-center gap-1 px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
            title={showTimestamps ? 'Hide timestamps' : 'Show timestamps'}
          >
            {showTimestamps ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
            {showTimestamps ? 'Hide times' : 'Show times'}
          </button>
          {compact && (
            <button
              onClick={() => setIsExpanded(false)}
              className="p-1 hover:bg-gray-100 rounded transition-colors"
            >
              <ChevronUp className="w-4 h-4 text-gray-400" />
            </button>
          )}
        </div>
      </div>

      {/* Assignments List */}
      {assignmentCount === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <Users className="w-12 h-12 text-gray-300 mx-auto mb-2" />
          <p className="text-sm">No color assignments yet</p>
          <p className="text-xs text-gray-400">Players will appear here when they select colors</p>
        </div>
      ) : (
        <div className="space-y-2">
          {sortedAssignments.map((assignment, index) => {
            const isCurrentUser = assignment.userId === currentUserId;
            const isFirstSelection = index === sortedAssignments.length - 1;
            
            return (
              <div
                key={assignment.userId}
                className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
                  isCurrentUser 
                    ? 'bg-blue-50 border-blue-200 ring-1 ring-blue-300' 
                    : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center gap-3">
                  {/* Color Circle */}
                  <div className="relative">
                    <div
                      className={`w-8 h-8 rounded-full border-2 ${
                        isCurrentUser ? 'border-blue-400' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: assignment.color.hex }}
                    />
                    {isCurrentUser && (
                      <Crown className="w-3 h-3 text-blue-600 absolute -top-1 -right-1" />
                    )}
                    {isFirstSelection && (
                      <div className="absolute -top-1 -left-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
                    )}
                  </div>

                  {/* Player Info */}
                  <div>
                    <div className="flex items-center gap-2">
                      <span className={`font-medium ${
                        isCurrentUser ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                        {assignment.username}
                        {isCurrentUser && <span className="text-blue-600 text-sm ml-1">(You)</span>}
                      </span>
                      {isFirstSelection && (
                        <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">
                          First!
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <span className="font-medium" style={{ color: assignment.color.hex }}>
                        {assignment.color.name}
                      </span>
                      <span className="text-gray-400">•</span>
                      <span className="text-xs text-gray-500">
                        {assignment.color.id}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Timestamp */}
                {showTimestamps && (
                  <div className="text-right">
                    <div className="text-xs text-gray-500">
                      {formatTime(assignment.selectedAt)}
                    </div>
                    <div className="text-xs text-gray-400">
                      {formatRelativeTime(assignment.selectedAt)}
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}

      {/* Summary Footer */}
      {assignmentCount > 0 && (
        <div className="mt-4 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>
              {assignmentCount} player{assignmentCount !== 1 ? 's' : ''} selected colors
            </span>
            <span>
              Latest: {formatRelativeTime(sortedAssignments[0]?.selectedAt)}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};
