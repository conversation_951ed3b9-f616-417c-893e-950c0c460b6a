import React, { useState, useEffect } from 'react';
import { Trophy, Star, DollarSign, Users, Sparkles } from 'lucide-react';
import { getColorHexById } from '../../constants/wheelColors';

interface Winner {
  userId: string;
  username: string;
  prizeAmount: number;
  selectedColor: string;
}

interface WinnerAnnouncementProps {
  winner: Winner;
  totalPool: number;
  participantCount: number;
  winningColor: string;
  onClose?: () => void;
  autoCloseDelay?: number;
  className?: string;
}

export const WinnerAnnouncement: React.FC<WinnerAnnouncementProps> = ({
  winner,
  totalPool,
  participantCount,
  winningColor,
  onClose,
  autoCloseDelay = 8000,
  className = '',
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [animatedPrize, setAnimatedPrize] = useState(0);

  // Animation sequence
  useEffect(() => {
    // Start entrance animation
    const timer1 = setTimeout(() => setIsVisible(true), 100);
    
    // Start confetti
    const timer2 = setTimeout(() => setShowConfetti(true), 500);
    
    // Animate prize amount
    const timer3 = setTimeout(() => {
      const duration = 2000;
      const steps = 60;
      const stepValue = winner.prizeAmount / steps;
      const stepDuration = duration / steps;

      let currentStep = 0;
      const interval = setInterval(() => {
        currentStep++;
        setAnimatedPrize(prev => prev + stepValue);

        if (currentStep >= steps) {
          setAnimatedPrize(winner.prizeAmount);
          clearInterval(interval);
        }
      }, stepDuration);

      return () => clearInterval(interval);
    }, 1000);

    // Auto close
    const timer4 = setTimeout(() => {
      if (onClose) {
        setIsVisible(false);
        setTimeout(onClose, 300);
      }
    }, autoCloseDelay);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
      clearTimeout(timer4);
    };
  }, [winner.prizeAmount, autoCloseDelay, onClose]);

  // Generate confetti particles
  const generateConfetti = () => {
    const particles = [];
    for (let i = 0; i < 50; i++) {
      particles.push({
        id: i,
        left: Math.random() * 100,
        animationDelay: Math.random() * 3,
        color: ['#ffd700', '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'][Math.floor(Math.random() * 6)],
      });
    }
    return particles;
  };

  const confettiParticles = generateConfetti();

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${className}`}>
      {/* Backdrop */}
      <div 
        className={`absolute inset-0 bg-black transition-opacity duration-300 ${
          isVisible ? 'bg-opacity-50' : 'bg-opacity-0'
        }`}
        onClick={onClose}
      />

      {/* Confetti */}
      {showConfetti && (
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          {confettiParticles.map((particle) => (
            <div
              key={particle.id}
              className="absolute w-2 h-2 animate-bounce"
              style={{
                left: `${particle.left}%`,
                backgroundColor: particle.color,
                animationDelay: `${particle.animationDelay}s`,
                animationDuration: '3s',
                top: '-10px',
                transform: 'rotate(45deg)',
              }}
            />
          ))}
        </div>
      )}

      {/* Main Content */}
      <div 
        className={`relative bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-500 ${
          isVisible 
            ? 'scale-100 opacity-100 translate-y-0' 
            : 'scale-75 opacity-0 translate-y-8'
        }`}
      >
        {/* Header with Trophy */}
        <div className="relative bg-gradient-to-br from-yellow-400 via-yellow-500 to-yellow-600 rounded-t-2xl p-6 text-center overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-20">
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white to-transparent transform rotate-45"></div>
          </div>

          {/* Floating Stars */}
          <div className="absolute inset-0">
            {[...Array(8)].map((_, i) => (
              <Star
                key={i}
                className={`absolute w-4 h-4 text-yellow-200 animate-pulse`}
                style={{
                  top: `${20 + Math.random() * 60}%`,
                  left: `${10 + Math.random() * 80}%`,
                  animationDelay: `${i * 0.5}s`,
                }}
              />
            ))}
          </div>

          {/* Trophy Icon */}
          <div className="relative mb-4">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-white bg-opacity-20 rounded-full backdrop-blur-sm">
              <Trophy className="w-12 h-12 text-white animate-bounce" />
            </div>
          </div>

          <h1 className="text-2xl font-bold text-white mb-2">🎉 WINNER! 🎉</h1>
          <p className="text-yellow-100">Congratulations on your victory!</p>
        </div>

        {/* Winner Details */}
        <div className="p-6">
          {/* Winner Info */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center gap-3 px-4 py-2 bg-gray-100 rounded-full mb-4">
              <div 
                className="w-6 h-6 rounded-full border-2 border-white shadow-md"
                style={{ backgroundColor: getColorHexById(winner.selectedColor) }}
              />
              <span className="font-medium text-gray-900 capitalize">
                {winner.selectedColor}
              </span>
            </div>
            
            <h2 className="text-xl font-bold text-gray-900 mb-1">
              {winner.username}
            </h2>
            <p className="text-gray-600">Selected the winning color!</p>
          </div>

          {/* Prize Amount */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6 mb-6 text-center border border-green-200">
            <div className="flex items-center justify-center gap-2 mb-2">
              <DollarSign className="w-6 h-6 text-green-600" />
              <span className="text-lg font-semibold text-green-800">Prize Won</span>
            </div>
            <div className="text-4xl font-bold text-green-900 mb-2">
              ${animatedPrize.toFixed(2)}
            </div>
            <div className="text-sm text-green-700">
              From a total pool of ${totalPool.toFixed(2)}
            </div>
          </div>

          {/* Game Statistics */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-blue-50 rounded-lg p-4 text-center border border-blue-200">
              <Users className="w-5 h-5 text-blue-600 mx-auto mb-2" />
              <div className="text-lg font-bold text-blue-900">{participantCount}</div>
              <div className="text-xs text-blue-700">Players</div>
            </div>
            <div className="bg-purple-50 rounded-lg p-4 text-center border border-purple-200">
              <Sparkles className="w-5 h-5 text-purple-600 mx-auto mb-2" />
              <div className="text-lg font-bold text-purple-900 capitalize">{winningColor}</div>
              <div className="text-xs text-purple-700">Winning Color</div>
            </div>
          </div>

          {/* Celebration Message */}
          <div className="bg-gradient-to-r from-pink-50 to-rose-50 rounded-lg p-4 text-center border border-pink-200">
            <p className="text-pink-800 font-medium">
              🎊 Amazing luck! You beat {participantCount - 1} other players! 🎊
            </p>
          </div>

          {/* Close Button */}
          {onClose && (
            <button
              onClick={() => {
                setIsVisible(false);
                setTimeout(onClose, 300);
              }}
              className="w-full mt-6 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 transform hover:scale-105"
            >
              Continue Playing
            </button>
          )}
        </div>
      </div>

      {/* Floating Elements */}
      <div className="absolute inset-0 pointer-events-none">
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute animate-float"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDelay: `${i * 0.3}s`,
              animationDuration: `${3 + Math.random() * 2}s`,
            }}
          >
            {i % 3 === 0 ? (
              <Trophy className="w-6 h-6 text-yellow-400 opacity-60" />
            ) : i % 3 === 1 ? (
              <Star className="w-5 h-5 text-blue-400 opacity-60" />
            ) : (
              <Sparkles className="w-4 h-4 text-purple-400 opacity-60" />
            )}
          </div>
        ))}
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        .animate-float {
          animation: float 3s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
};

export default WinnerAnnouncement;
