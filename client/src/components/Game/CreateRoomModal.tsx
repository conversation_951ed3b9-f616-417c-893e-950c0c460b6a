import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { X, Plus } from 'lucide-react';
import { useGameStore } from '@/store/gameStore';
import { config } from '@/config/env';
import LoadingSpinner from '@/components/UI/LoadingSpinner';
import toast from 'react-hot-toast';
import type { Room } from '@/types/api';

const createRoomSchema = z.object({
  name: z
    .string()
    .min(1, 'Room name is required')
    .max(config.game.maxRoomNameLength, 'Room name is too long'),
  gameType: z.enum(config.game.gameTypes),
  betAmount: z
    .number()
    .min(config.game.minBetAmount, `Minimum bet is $${config.game.minBetAmount}`)
    .max(config.game.maxBetAmount, `Maximum bet is $${config.game.maxBetAmount}`),
  maxPlayers: z
    .number()
    .min(config.game.minPlayersPerRoom, `Minimum ${config.game.minPlayersPerRoom} players`)
    .max(config.game.maxPlayersPerRoom, `Maximum ${config.game.maxPlayersPerRoom} players`),
  isPrivate: z.boolean(),
  password: z.string().optional(),
});

type CreateRoomFormData = z.infer<typeof createRoomSchema>;

interface CreateRoomModalProps {
  onClose: () => void;
  onSuccess: (room: Room) => void;
}

const CreateRoomModal: React.FC<CreateRoomModalProps> = ({ onClose, onSuccess }) => {
  const { createRoom } = useGameStore();
  const [isLoading, setIsLoading] = React.useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<CreateRoomFormData>({
    resolver: zodResolver(createRoomSchema),
    defaultValues: {
      gameType: 'PRIZEWHEEL',
      betAmount: config.game.defaultBetAmount,
      maxPlayers: 8,
      isPrivate: false,
    },
  });

  const isPrivate = watch('isPrivate');

  const onSubmit = async (data: CreateRoomFormData) => {
    setIsLoading(true);
    try {
      const roomData = {
        ...data,
        currency: 'USD',
      };
      
      const room = await createRoom(roomData);
      toast.success('Room created successfully!');
      onSuccess(room);
    } catch (error) {
      toast.error('Failed to create room');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Create New Room</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-4">
          <div>
            <label className="label">Room Name</label>
            <input
              {...register('name')}
              type="text"
              className={`input ${errors.name ? 'input-error' : ''}`}
              placeholder="Enter room name"
            />
            {errors.name && (
              <p className="error-text">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label className="label">Game Type</label>
            <select
              {...register('gameType')}
              className={`input ${errors.gameType ? 'input-error' : ''}`}
            >
              {config.game.gameTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
            {errors.gameType && (
              <p className="error-text">{errors.gameType.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="label">Bet Amount ($)</label>
              <input
                {...register('betAmount', { valueAsNumber: true })}
                type="number"
                min={config.game.minBetAmount}
                max={config.game.maxBetAmount}
                className={`input ${errors.betAmount ? 'input-error' : ''}`}
              />
              {errors.betAmount && (
                <p className="error-text">{errors.betAmount.message}</p>
              )}
            </div>

            <div>
              <label className="label">Max Players</label>
              <input
                {...register('maxPlayers', { valueAsNumber: true })}
                type="number"
                min={config.game.minPlayersPerRoom}
                max={config.game.maxPlayersPerRoom}
                className={`input ${errors.maxPlayers ? 'input-error' : ''}`}
              />
              {errors.maxPlayers && (
                <p className="error-text">{errors.maxPlayers.message}</p>
              )}
            </div>
          </div>

          <div>
            <label className="flex items-center">
              <input
                {...register('isPrivate')}
                type="checkbox"
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="ml-2 text-sm text-gray-700">Private Room</span>
            </label>
          </div>

          {isPrivate && (
            <div>
              <label className="label">Password</label>
              <input
                {...register('password')}
                type="password"
                className={`input ${errors.password ? 'input-error' : ''}`}
                placeholder="Enter room password"
              />
              {errors.password && (
                <p className="error-text">{errors.password.message}</p>
              )}
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="btn-outline"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="btn-primary"
            >
              {isLoading ? (
                <LoadingSpinner size="sm" />
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  Create Room
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateRoomModal;
