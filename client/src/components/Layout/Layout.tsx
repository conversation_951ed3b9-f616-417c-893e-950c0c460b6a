import React from 'react';
import { Outlet } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import ConnectionStatus from './ConnectionStatus';
import NotificationCenter from './NotificationCenter';

const Layout: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header />

      <div className="flex">
        {/* Sidebar */}
        <Sidebar />

        {/* Main content */}
        <main className="flex-1 p-6">
          <Outlet />
        </main>
      </div>

      {/* Connection Status */}
      <ConnectionStatus />

      {/* Notification Center */}
      <NotificationCenter />
    </div>
  );
};

export default Layout;
