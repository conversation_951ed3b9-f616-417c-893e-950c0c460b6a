import React from 'react';
import { Wifi, WifiOff, RotateCcw } from 'lucide-react';
import { useSocketStore } from '@/store/socketStore';
import { clsx } from 'clsx';

const ConnectionStatus: React.FC = () => {
  const { connectionInfo, isConnected } = useSocketStore();

  const getStatusText = () => {
    switch (connectionInfo.state) {
      case 'connected':
        return 'Connected';
      case 'connecting':
        return 'Connecting...';
      case 'reconnecting':
        return `Reconnecting... (${connectionInfo.reconnectAttempts})`;
      case 'disconnected':
        return 'Disconnected';
      case 'error':
        return 'Connection Error';
      default:
        return 'Unknown';
    }
  };

  const getStatusIcon = () => {
    switch (connectionInfo.state) {
      case 'connected':
        return <Wifi className="w-4 h-4" />;
      case 'connecting':
      case 'reconnecting':
        return <RotateCcw className="w-4 h-4 animate-spin" />;
      case 'disconnected':
      case 'error':
        return <WifiOff className="w-4 h-4" />;
      default:
        return <WifiOff className="w-4 h-4" />;
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div
        className={clsx(
          'flex items-center space-x-2 px-3 py-2 rounded-lg shadow-lg text-sm font-medium',
          {
            'bg-success-100 text-success-800 border border-success-200': isConnected,
            'bg-warning-100 text-warning-800 border border-warning-200': 
              connectionInfo.state === 'connecting' || connectionInfo.state === 'reconnecting',
            'bg-error-100 text-error-800 border border-error-200': 
              connectionInfo.state === 'disconnected' || connectionInfo.state === 'error',
          }
        )}
      >
        {getStatusIcon()}
        <span>{getStatusText()}</span>
        {connectionInfo.lastPing && (
          <span className="text-xs opacity-75">
            ({connectionInfo.lastPing}ms)
          </span>
        )}
      </div>
    </div>
  );
};

export default ConnectionStatus;
