/**
 * Enhanced Room Subscription Example Component
 * Demonstrates the new room subscription management with automatic lobby fallback
 * 
 * Key Features Demonstrated:
 * 1. Automatic lobby re-subscription on room join failure
 * 2. Room-specific event subscription on join success
 * 3. Real-time room info updates for all room participants
 * 4. Clean subscription state management
 */

import { useState, useCallback } from 'react';
import { useRoomSubscription, type RoomInfoUpdate } from '@/hooks/useRoomSubscription';
import { Button } from '@/components/UI/Button';
import { Input } from '@/components/UI/Input';
import toast from 'react-hot-toast';

export function EnhancedRoomSubscriptionExample() {
  const [roomId, setRoomId] = useState('');
  const [password, setPassword] = useState('');
  const [betAmount, setBetAmount] = useState<number>(100);
  const [roomUpdates, setRoomUpdates] = useState<RoomInfoUpdate[]>([]);

  // Use the enhanced room subscription hook
  const {
    isJoining,
    isLeaving,
    currentSubscription,
    roomId: currentRoomId,
    error,
    isConnected,
    currentRoom,
    lobbyRooms,
    joinRoom,
    leaveRoom,
    subscribeToLobby,
    unsubscribeFromLobby,
    getSubscriptionState,
    getTransitionHistory,
  } = useRoomSubscription({
    // Handle real-time room info updates
    onRoomInfoUpdate: useCallback((update: RoomInfoUpdate) => {
      console.log('Room info update received:', update);
      
      // Add to updates list (keep last 10)
      setRoomUpdates(prev => [update, ...prev].slice(0, 10));
      
      // Show toast for significant updates
      if (update.action === 'player_joined') {
        toast.success(`${update.playerName || 'A player'} joined the room`);
      } else if (update.action === 'player_left') {
        toast(`${update.playerName || 'A player'} left the room`);
      }
    }, []),
    
    // Handle subscription changes
    onSubscriptionChange: useCallback((type: 'lobby' | 'room' | null, roomId?: string) => {
      console.log('Subscription changed:', { type, roomId });

      if (type === 'lobby') {
        toast.success('Subscribed to lobby - receiving room updates');
      } else if (type === 'room') {
        toast.success(`Subscribed to room ${roomId} - receiving real-time updates`);
      } else {
        toast('Unsubscribed from all updates');
      }
    }, []),
    
    // Enable automatic lobby fallback on join failure
    autoLobbyFallback: true,
  });

  const handleJoinRoom = async () => {
    if (!roomId.trim()) {
      toast.error('Please enter a room ID');
      return;
    }

    try {
      await joinRoom(roomId.trim(), password || undefined, betAmount);
      toast.success('Successfully joined room!');
      setRoomId('');
      setPassword('');
    } catch (error) {
      // Error is already handled by the hook and toast is shown
      console.error('Join room failed:', error);
    }
  };

  const handleLeaveRoom = async () => {
    if (!currentRoomId) return;

    try {
      await leaveRoom(currentRoomId, 'voluntary');
      toast.success('Successfully left room!');
    } catch (error) {
      toast.error('Failed to leave room');
      console.error('Leave room failed:', error);
    }
  };

  const handleSubscribeToLobby = async () => {
    try {
      await subscribeToLobby();
    } catch (error) {
      toast.error('Failed to subscribe to lobby');
      console.error('Subscribe to lobby failed:', error);
    }
  };

  const handleUnsubscribeFromLobby = async () => {
    try {
      await unsubscribeFromLobby();
    } catch (error) {
      toast.error('Failed to unsubscribe from lobby');
      console.error('Unsubscribe from lobby failed:', error);
    }
  };

  const getSubscriptionBadgeColor = (type: string | null) => {
    switch (type) {
      case 'lobby': return 'bg-blue-500';
      case 'room': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold mb-2">Enhanced Room Subscription Management</h2>
          <p className="text-gray-600">
            Demonstrates automatic lobby fallback, room-specific subscriptions, and real-time updates
          </p>
        </div>

        <div className="space-y-4">
          {/* Connection Status */}
          <div className="flex items-center gap-2">
            <span className={`px-2 py-1 rounded text-sm ${isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
            <span className={`px-2 py-1 rounded text-sm ${getSubscriptionBadgeColor(currentSubscription)}`}>
              {currentSubscription ? `Subscribed to ${currentSubscription}` : 'Not subscribed'}
            </span>
            {currentRoomId && (
              <span className="px-2 py-1 rounded text-sm bg-gray-100 text-gray-800">Room: {currentRoomId}</span>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded p-3">
              <p className="text-red-800">{error.message}</p>
            </div>
          )}

          <hr className="my-4" />

          {/* Room Join Section */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Join Room</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <Input
                placeholder="Room ID"
                value={roomId}
                onChange={(e) => setRoomId(e.target.value)}
                disabled={isJoining || !!currentRoom}
              />
              <Input
                placeholder="Password (optional)"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isJoining || !!currentRoom}
              />
              <Input
                placeholder="Bet Amount"
                type="number"
                value={betAmount}
                onChange={(e) => setBetAmount(Number(e.target.value))}
                disabled={isJoining || !!currentRoom}
              />
            </div>
            <div className="flex gap-2">
              <Button 
                onClick={handleJoinRoom} 
                disabled={isJoining || !!currentRoom || !isConnected}
                className="flex-1"
              >
                {isJoining ? 'Joining...' : 'Join Room'}
              </Button>
              {currentRoom && (
                <Button 
                  onClick={handleLeaveRoom} 
                  disabled={isLeaving}
                  variant="outline"
                  className="flex-1"
                >
                  {isLeaving ? 'Leaving...' : 'Leave Room'}
                </Button>
              )}
            </div>
          </div>

          <hr className="my-4" />

          {/* Lobby Subscription Section */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">Lobby Subscription</h3>
            <div className="flex gap-2">
              <Button
                onClick={handleSubscribeToLobby}
                disabled={currentSubscription === 'lobby' || !!currentRoom}
                className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded"
              >
                Subscribe to Lobby
              </Button>
              <Button
                onClick={handleUnsubscribeFromLobby}
                disabled={currentSubscription !== 'lobby'}
                className="bg-gray-100 hover:bg-gray-200 text-gray-800 px-4 py-2 rounded"
              >
                Unsubscribe from Lobby
              </Button>
            </div>
            {lobbyRooms && lobbyRooms.length > 0 && (
              <div className="text-sm text-gray-600">
                Available rooms: {lobbyRooms.length}
              </div>
            )}
          </div>

          <hr className="my-4" />

          {/* Current Room Info */}
          {currentRoom && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Current Room</h3>
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div><strong>Name:</strong> {currentRoom.name}</div>
                  <div><strong>Status:</strong> {currentRoom.status}</div>
                  <div><strong>Players:</strong> {currentRoom.playerCount}/{currentRoom.maxPlayers}</div>
                  <div><strong>Bet Amount:</strong> ${currentRoom.betAmount}</div>
                </div>
              </div>
            </div>
          )}

          {/* Real-time Room Updates */}
          {roomUpdates.length > 0 && (
            <div className="space-y-3">
              <h3 className="text-lg font-semibold">Real-time Room Updates</h3>
              <div className="max-h-40 overflow-y-auto space-y-2">
                {roomUpdates.map((update, index) => (
                  <div key={index} className="bg-blue-50 p-2 rounded text-sm">
                    <div className="flex justify-between items-start">
                      <div>
                        <strong>Room {update.roomId}:</strong> {update.action}
                        {update.playerName && ` - ${update.playerName}`}
                        {update.playerCount !== undefined && (
                          <span className="ml-2 text-gray-600">
                            ({update.playerCount}/{update.maxPlayers} players)
                          </span>
                        )}
                      </div>
                      <span className="text-xs text-gray-500">
                        {update.timestamp ? new Date(update.timestamp).toLocaleTimeString() : 'now'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Debug Info */}
          <details className="text-xs">
            <summary className="cursor-pointer font-medium">Debug Information</summary>
            <div className="mt-2 space-y-2">
              <div>
                <strong>Subscription State:</strong>
                <pre className="bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                  {JSON.stringify(getSubscriptionState(), null, 2)}
                </pre>
              </div>
              <div>
                <strong>Transition History:</strong>
                <pre className="bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                  {JSON.stringify(getTransitionHistory(), null, 2)}
                </pre>
              </div>
            </div>
          </details>
        </div>
      </div>
    </div>
  );
}
