/**
 * Player Slots Demo Component
 * Demonstrates the enhanced player slots with real-time room_info_updated events
 */

import { useState } from 'react';
import { EnhancedPlayerSlots } from '@/components/Room/EnhancedPlayerSlots';
import { useRoomSubscription } from '@/hooks/useRoomSubscription';
import { useSocketStore } from '@/store/socketStore';
import { Button } from '@/components/UI/Button';
import { Input } from '@/components/UI/Input';
import toast from 'react-hot-toast';

export function PlayerSlotsDemo() {
  const [roomId, setRoomId] = useState('');
  const [updateLog, setUpdateLog] = useState<any[]>([]);
  
  const { currentRoom, isConnected } = useSocketStore();
  
  const {
    joinRoom,
    leaveRoom,
    isJoining,
    isLeaving,
    currentSubscription,
    error,
  } = useRoomSubscription({
    // Log all room info updates
    onRoomInfoUpdate: (update) => {
      console.log('PlayerSlotsDemo: Room info update:', update);
      
      // Add to update log (keep last 10)
      setUpdateLog(prev => [
        {
          timestamp: new Date().toISOString(),
          type: 'room_info_updated',
          data: update,
        },
        ...prev
      ].slice(0, 10));
      
      // Show toast for player changes
      if (update.players) {
        const playerCount = update.players.length;
        const maxPlayers = update.room?.maxPlayers || 4;
        toast.success(`Room updated: ${playerCount}/${maxPlayers} players`);
      }
    },
    
    // Handle subscription changes
    onSubscriptionChange: (type, roomId) => {
      if (type === 'room') {
        toast.success(`Subscribed to room ${roomId} - receiving real-time updates`);
      } else if (type === 'lobby') {
        toast.success('Returned to lobby - room list updates enabled');
      }
    },
    
    // Enable automatic lobby fallback
    autoLobbyFallback: true,
  });

  const handleJoinRoom = async () => {
    if (!roomId.trim()) {
      toast.error('Please enter a room ID');
      return;
    }

    try {
      await joinRoom(roomId.trim());
      toast.success('Successfully joined room!');
    } catch (error) {
      console.error('Failed to join room:', error);
      // Error handling is automatic with lobby fallback
    }
  };

  const handleLeaveRoom = async () => {
    if (!currentRoom) return;

    try {
      await leaveRoom(currentRoom.id, 'voluntary');
      toast.success('Successfully left room!');
      setRoomId('');
    } catch (error) {
      toast.error('Failed to leave room');
      console.error('Leave room failed:', error);
    }
  };

  const simulateRoomUpdate = () => {
    // Simulate a room_info_updated event for testing
    const mockUpdate = {
      room: {
        id: "test-room-123",
        name: "Test Room",
        gameType: "prizewheel",
        status: "waiting",
        playerCount: 2,
        maxPlayers: 4,
        betAmount: 10,
        prizePool: 20,
        isPrivate: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      roomState: {
        playerCount: 2,
        readyCount: 1,
        canStartGame: false,
        prizePool: 20,
        gameInProgress: false,
        countdown: null,
      },
      players: [
        {
          betAmount: 10,
          isReady: true,
          joinedAt: new Date(Date.now() - 60000).toISOString(),
          position: 0,
          userId: "user1",
          username: "Player1",
        },
        {
          betAmount: 10,
          isReady: false,
          joinedAt: new Date().toISOString(),
          position: 1,
          userId: "user2",
          username: "Player2",
        },
      ],
      gameConfig: {
        betAmount: 10,
        gameType: "prizewheel",
        maxPlayers: 4,
        minPlayers: 2,
        settings: {},
      },
      gameSpecificData: {
        gameType: "prizewheel",
        colorSelections: {
          "user1": "red",
          "user2": "blue",
        },
        availableColors: ["red", "blue", "green", "yellow", "purple", "orange", "pink", "teal"],
        playerColorMappings: {
          "user1": "red",
          "user2": "blue",
        },
        colorSelectionTimestamps: {
          "user1": new Date().toISOString(),
          "user2": new Date().toISOString(),
        },
      },
      timestamp: new Date().toISOString(),
    };

    // Dispatch the event
    window.dispatchEvent(new CustomEvent('roomInfoUpdate', { 
      detail: { roomId: 'test-room-123', roomInfo: mockUpdate } 
    }));
    
    toast.success('Simulated room update sent!');
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-4">Player Slots Demo</h1>
        <p className="text-gray-600 mb-4">
          This demo shows real-time player slots that update automatically when receiving 
          <code className="bg-gray-100 px-2 py-1 rounded mx-1">room_info_updated</code> events.
        </p>

        {/* Connection Status */}
        <div className="flex items-center gap-4 mb-4">
          <div className={`px-3 py-1 rounded-full text-sm ${
            isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
          }`}>
            {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </div>
          
          <div className={`px-3 py-1 rounded-full text-sm ${
            currentSubscription === 'room' ? 'bg-blue-100 text-blue-800' :
            currentSubscription === 'lobby' ? 'bg-yellow-100 text-yellow-800' :
            'bg-gray-100 text-gray-600'
          }`}>
            📡 {currentSubscription ? `Subscribed to ${currentSubscription}` : 'Not subscribed'}
          </div>
        </div>

        {/* Room Controls */}
        <div className="space-y-4">
          {!currentRoom ? (
            <div className="flex gap-2">
              <Input
                placeholder="Enter room ID (e.g., 68412c9af494b684c1c18ecf)"
                value={roomId}
                onChange={(e) => setRoomId(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={handleJoinRoom}
                disabled={isJoining || !isConnected}
                className="bg-blue-500 hover:bg-blue-600 text-white"
              >
                {isJoining ? 'Joining...' : 'Join Room'}
              </Button>
            </div>
          ) : (
            <div className="flex gap-2 items-center">
              <span className="text-green-600 font-medium">
                ✓ In room: {currentRoom.name || currentRoom.id}
              </span>
              <Button
                onClick={handleLeaveRoom}
                disabled={isLeaving}
                className="bg-red-500 hover:bg-red-600 text-white"
              >
                {isLeaving ? 'Leaving...' : 'Leave Room'}
              </Button>
            </div>
          )}

          {/* Test Button */}
          <div className="flex gap-2">
            <Button
              onClick={simulateRoomUpdate}
              className="bg-purple-500 hover:bg-purple-600 text-white"
            >
              🧪 Simulate Room Update
            </Button>
            <span className="text-sm text-gray-500 self-center">
              (For testing without joining a real room)
            </span>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded text-red-800">
            Error: {error.message}
          </div>
        )}
      </div>

      {/* Player Slots Component */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Player Slots</h2>
        <EnhancedPlayerSlots
          showColorSelection={true}
          showReadyStatus={true}
          showBetAmount={true}
          showJoinTime={true}
        />
      </div>

      {/* Update Log */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Real-time Update Log</h2>
        
        {updateLog.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>No updates received yet</p>
            <p className="text-sm mt-2">Join a room or simulate an update to see real-time changes</p>
          </div>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {updateLog.map((log, index) => (
              <div key={index} className="border rounded-lg p-3 bg-gray-50">
                <div className="flex justify-between items-start mb-2">
                  <span className="font-medium text-blue-600">{log.type}</span>
                  <span className="text-xs text-gray-500">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                
                <div className="text-sm space-y-1">
                  <div><strong>Room:</strong> {log.data.room?.name || 'Unknown'}</div>
                  <div><strong>Players:</strong> {log.data.players?.length || 0}/{log.data.room?.maxPlayers || 4}</div>
                  <div><strong>Ready:</strong> {log.data.roomState?.readyCount || 0}</div>
                  <div><strong>Status:</strong> {log.data.room?.status || 'Unknown'}</div>
                </div>
                
                <details className="mt-2">
                  <summary className="text-xs text-gray-600 cursor-pointer">Raw Data</summary>
                  <pre className="text-xs bg-white p-2 rounded mt-1 overflow-x-auto">
                    {JSON.stringify(log.data, null, 2)}
                  </pre>
                </details>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Usage Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="font-semibold text-blue-800 mb-3">How to Use</h3>
        <ol className="list-decimal list-inside space-y-2 text-blue-700">
          <li>Enter a real room ID (like the one from your example: <code>68412c9af494b684c1c18ecf</code>)</li>
          <li>Click "Join Room" to connect and start receiving real-time updates</li>
          <li>Watch the player slots update automatically as players join/leave</li>
          <li>See color selections and ready status change in real-time</li>
          <li>Use "Simulate Room Update" to test without joining a real room</li>
        </ol>
        
        <div className="mt-4 p-3 bg-blue-100 rounded">
          <strong>Expected Events:</strong> The component listens for <code>room_info_updated</code> events 
          with the exact structure you provided, including room data, player positions, color selections, 
          and ready status.
        </div>
      </div>
    </div>
  );
}

export default PlayerSlotsDemo;
