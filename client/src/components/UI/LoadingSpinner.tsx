import React from 'react';
import { clsx } from 'clsx';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ 
  size = 'md', 
  className 
}) => {
  return (
    <div
      className={clsx(
        'spinner',
        {
          'spinner-sm': size === 'sm',
          'spinner-md': size === 'md',
          'spinner-lg': size === 'lg',
        },
        className
      )}
    />
  );
};

export default LoadingSpinner;
