import React from 'react';
import { Play, Loader2, AlertCircle, DollarSign, Users, XCircle, Lock } from 'lucide-react';
import { clsx } from 'clsx';

interface JoinRoomButtonProps {
  roomId: string;
  roomName: string;
  betAmount: number;
  userBalance: number;
  currentPlayers: number;
  maxPlayers: number;
  roomStatus: string;
  isJoining: boolean;
  canJoin: boolean;
  isPrivate?: boolean;
  onJoin: (roomId: string, password?: string, betAmount?: number) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
}

const JoinRoomButton: React.FC<JoinRoomButtonProps> = ({
  roomId,
  roomName,
  betAmount,
  userBalance,
  currentPlayers,
  maxPlayers,
  roomStatus,
  isJoining,
  canJoin,
  isPrivate = false,
  onJoin,
  className,
  size = 'md',
  showTooltip = true
}) => {
  const [showPasswordInput, setShowPasswordInput] = React.useState(false);
  const [password, setPassword] = React.useState('');
  const [showConfirmModal, setShowConfirmModal] = React.useState(false);
  const [showTooltipModal, setShowTooltipModal] = React.useState(false);

  // Capacity checks
  const isFull = currentPlayers >= maxPlayers;
  const hasInsufficientBalance = userBalance < betAmount;
  const isRoomNotWaiting = !['WAITING', 'OPEN', 'ACTIVE'].includes(roomStatus?.toUpperCase());

  // Determine if button should be disabled and why
  const getDisabledReason = () => {
    if (isJoining) return 'joining';
    if (isFull) return 'full';
    if (hasInsufficientBalance) return 'balance';
    if (isRoomNotWaiting) return 'status';
    if (!canJoin) return 'general';
    return null;
  };

  const disabledReason = getDisabledReason();
  const isDisabled = disabledReason !== null;

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  // Get button text based on state
  const getButtonText = () => {
    switch (disabledReason) {
      case 'joining':
        return 'Joining...';
      case 'full':
        return 'Room Full';
      case 'balance':
        return 'Insufficient Balance';
      case 'status':
        return `Room ${roomStatus}`;
      default:
        return 'Join Room';
    }
  };

  // Get button icon based on state
  const getButtonIcon = () => {
    switch (disabledReason) {
      case 'joining':
        return <Loader2 className={clsx(iconSizes[size], 'animate-spin')} />;
      case 'full':
        return <XCircle className={iconSizes[size]} />;
      case 'balance':
        return <DollarSign className={iconSizes[size]} />;
      case 'status':
        return <AlertCircle className={iconSizes[size]} />;
      default:
        return isPrivate ? <Lock className={iconSizes[size]} /> : <Play className={iconSizes[size]} />;
    }
  };

  // Get tooltip message
  const getTooltipMessage = () => {
    switch (disabledReason) {
      case 'full':
        return `This room is full (${currentPlayers}/${maxPlayers} players). Try another room or wait for a player to leave.`;
      case 'balance':
        return `You need $${betAmount} to join this room, but you only have $${userBalance}.`;
      case 'status':
        return `This room is currently ${roomStatus.toLowerCase()} and cannot accept new players.`;
      case 'general':
        return 'This room is not available for joining at the moment.';
      default:
        return null;
    }
  };

  const handleJoinClick = () => {
    // If disabled, show tooltip modal if enabled
    if (isDisabled) {
      if (showTooltip && getTooltipMessage()) {
        setShowTooltipModal(true);
      }
      return;
    }

    if (isPrivate && !showPasswordInput) {
      setShowPasswordInput(true);
      return;
    }

    setShowConfirmModal(true);
  };

  const confirmJoin = () => {
    onJoin(roomId, isPrivate ? password : undefined, betAmount);
    setPassword('');
    setShowPasswordInput(false);
    setShowConfirmModal(false);
  };

  const cancelJoin = () => {
    setShowConfirmModal(false);
    setPassword('');
    setShowPasswordInput(false);
  };

  return (
    <>
      {/* Password Input */}
      {showPasswordInput && (
        <div className="mb-3">
          <input
            type="password"
            placeholder="Enter room password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            onKeyPress={(e) => e.key === 'Enter' && handleJoinClick()}
            autoFocus
          />
        </div>
      )}

      {/* Join Button */}
      <button
        onClick={handleJoinClick}
        disabled={isDisabled && disabledReason === 'joining'}
        className={clsx(
          'flex items-center justify-center gap-2 font-medium rounded-md transition-colors',
          sizeClasses[size],
          isDisabled
            ? disabledReason === 'full'
              ? 'bg-red-100 text-red-600 cursor-pointer hover:bg-red-200'
              : disabledReason === 'balance'
              ? 'bg-yellow-100 text-yellow-600 cursor-pointer hover:bg-yellow-200'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed'
            : 'bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500',
          className
        )}
        title={showTooltip ? getTooltipMessage() || undefined : undefined}
      >
        {getButtonIcon()}
        <span>{getButtonText()}</span>
      </button>

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Confirm Enhanced Room Join
              </h3>

              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Room:</span>
                  <span className="font-medium">{roomName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Bet Amount:</span>
                  <span className="font-medium flex items-center">
                    <DollarSign className="w-4 h-4 mr-1" />
                    {betAmount}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Room Capacity:</span>
                  <span className="font-medium flex items-center">
                    <Users className="w-4 h-4 mr-1" />
                    {currentPlayers}/{maxPlayers} players
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Your Balance:</span>
                  <span className={clsx(
                    'font-medium flex items-center',
                    hasInsufficientBalance ? 'text-red-600' : 'text-green-600'
                  )}>
                    <DollarSign className="w-4 h-4 mr-1" />
                    {userBalance}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">After Join:</span>
                  <span className={clsx(
                    'font-medium flex items-center',
                    (userBalance - betAmount) < 0 ? 'text-red-600' : 'text-gray-900'
                  )}>
                    <DollarSign className="w-4 h-4 mr-1" />
                    {userBalance - betAmount}
                  </span>
                </div>
                {isPrivate && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Private Room:</span>
                    <span className="font-medium">Password required</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Enhanced Features:</span>
                  <span className="font-medium text-blue-600">Position Assignment</span>
                </div>
              </div>

              {hasInsufficientBalance && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                  <div className="flex items-center text-sm text-red-600">
                    <AlertCircle className="w-4 h-4 mr-2" />
                    <span>Insufficient balance to join this room</span>
                  </div>
                </div>
              )}

              <div className="flex space-x-3">
                <button
                  onClick={cancelJoin}
                  className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmJoin}
                  disabled={hasInsufficientBalance}
                  className={clsx(
                    'flex-1 px-4 py-2 rounded-md font-medium transition-colors',
                    hasInsufficientBalance
                      ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                      : 'bg-blue-600 text-white hover:bg-blue-700'
                  )}
                >
                  {hasInsufficientBalance ? 'Insufficient Balance' : 'Join Room'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tooltip Modal */}
      {showTooltipModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                {disabledReason === 'full' && <XCircle className="w-6 h-6 text-red-500 mr-3" />}
                {disabledReason === 'balance' && <DollarSign className="w-6 h-6 text-yellow-500 mr-3" />}
                {disabledReason === 'status' && <AlertCircle className="w-6 h-6 text-gray-500 mr-3" />}
                <h3 className="text-lg font-medium text-gray-900">
                  Cannot Join Room
                </h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-600 mb-4">
                  {getTooltipMessage()}
                </p>

                {disabledReason === 'full' && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div className="text-sm text-blue-700">
                      <p className="font-medium mb-2">Suggestions:</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>Try refreshing to see if a spot opens up</li>
                        <li>Look for similar rooms with available space</li>
                        <li>Create your own room with similar settings</li>
                      </ul>
                    </div>
                  </div>
                )}

                {disabledReason === 'balance' && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div className="text-sm text-yellow-700">
                      <p className="font-medium mb-2">Options:</p>
                      <ul className="list-disc list-inside space-y-1">
                        <li>Add funds to your account</li>
                        <li>Look for rooms with lower bet amounts</li>
                        <li>Wait for your balance to increase from other games</li>
                      </ul>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-end">
                <button
                  onClick={() => setShowTooltipModal(false)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Got it
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default JoinRoomButton;
