import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, Clock, CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { clsx } from 'clsx';

interface ReconnectionModalProps {
  isOpen: boolean;
  roomId: string;
  roomName: string;
  reconnectionWindow: number; // in seconds
  onReconnect: () => Promise<boolean>;
  onCancel: () => void;
  onTimeout: () => void;
}

const ReconnectionModal: React.FC<ReconnectionModalProps> = ({
  isOpen,
  roomName,
  reconnectionWindow,
  onReconnect,
  onCancel,
  onTimeout
}) => {
  const [timeRemaining, setTimeRemaining] = useState(reconnectionWindow);
  const [isReconnecting, setIsReconnecting] = useState(false);
  const [reconnectionStatus, setReconnectionStatus] = useState<'idle' | 'success' | 'failed'>('idle');

  // Countdown timer
  useEffect(() => {
    if (!isOpen || timeRemaining <= 0) return;

    const timer = setInterval(() => {
      setTimeRemaining(prev => {
        if (prev <= 1) {
          onTimeout();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [isOpen, timeRemaining, onTimeout]);

  // Format time remaining
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Handle reconnection attempt
  const handleReconnect = async () => {
    setIsReconnecting(true);
    setReconnectionStatus('idle');

    try {
      const success = await onReconnect();
      if (success) {
        setReconnectionStatus('success');
        // Auto-close after success
        setTimeout(() => {
          onCancel();
        }, 2000);
      } else {
        setReconnectionStatus('failed');
      }
    } catch (error) {
      console.error('Reconnection failed:', error);
      setReconnectionStatus('failed');
    } finally {
      setIsReconnecting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        {/* Header */}
        <div className="flex items-center gap-3 mb-4">
          <div className="flex-shrink-0">
            {reconnectionStatus === 'success' ? (
              <CheckCircle className="w-8 h-8 text-green-500" />
            ) : reconnectionStatus === 'failed' ? (
              <XCircle className="w-8 h-8 text-red-500" />
            ) : (
              <WifiOff className="w-8 h-8 text-yellow-500" />
            )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {reconnectionStatus === 'success' 
                ? 'Reconnected Successfully!' 
                : reconnectionStatus === 'failed'
                ? 'Reconnection Failed'
                : 'Reconnection Available'
              }
            </h3>
            <p className="text-sm text-gray-600">
              {reconnectionStatus === 'success' 
                ? 'You have been reconnected to the room.'
                : reconnectionStatus === 'failed'
                ? 'Unable to reconnect to the room.'
                : `Room: ${roomName}`
              }
            </p>
          </div>
        </div>

        {/* Status Content */}
        {reconnectionStatus === 'idle' && (
          <>
            {/* Time Remaining */}
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-5 h-5 text-blue-500" />
                <span className="font-medium text-blue-900">Time Remaining</span>
              </div>
              <div className="text-2xl font-mono font-bold text-blue-700">
                {formatTime(timeRemaining)}
              </div>
              <p className="text-sm text-blue-600 mt-1">
                Your spot is reserved while you reconnect
              </p>
            </div>

            {/* Reconnection Info */}
            <div className="bg-gray-50 border border-gray-200 rounded-md p-3 mb-4">
              <div className="text-sm text-gray-700">
                <p className="font-medium mb-1">What happened?</p>
                <p className="mb-2">
                  You were disconnected while in ready state. Your position and ready status 
                  have been preserved to prevent game disruption.
                </p>
                <p className="font-medium mb-1">What can you do?</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Click "Reconnect" to rejoin the room</li>
                  <li>Your ready status will be restored</li>
                  <li>The game will continue as normal</li>
                </ul>
              </div>
            </div>
          </>
        )}

        {reconnectionStatus === 'success' && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-4">
            <div className="flex items-center gap-2">
              <Wifi className="w-5 h-5 text-green-500" />
              <span className="font-medium text-green-900">Connection Restored</span>
            </div>
            <p className="text-sm text-green-700 mt-1">
              You have been successfully reconnected to the room. Your ready status has been restored.
            </p>
          </div>
        )}

        {reconnectionStatus === 'failed' && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-4">
            <div className="flex items-center gap-2">
              <XCircle className="w-5 h-5 text-red-500" />
              <span className="font-medium text-red-900">Reconnection Failed</span>
            </div>
            <p className="text-sm text-red-700 mt-1">
              Unable to reconnect to the room. The room may no longer be available or the game may have ended.
            </p>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-3">
          {reconnectionStatus === 'idle' && (
            <>
              <button
                onClick={onCancel}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
                disabled={isReconnecting}
              >
                Cancel
              </button>
              <button
                onClick={handleReconnect}
                disabled={isReconnecting || timeRemaining <= 0}
                className={clsx(
                  'flex-1 px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2',
                  isReconnecting || timeRemaining <= 0
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
                )}
              >
                {isReconnecting ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Reconnecting...</span>
                  </div>
                ) : (
                  'Reconnect'
                )}
              </button>
            </>
          )}

          {reconnectionStatus === 'failed' && (
            <>
              <button
                onClick={onCancel}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Close
              </button>
              <button
                onClick={handleReconnect}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium"
              >
                Try Again
              </button>
            </>
          )}

          {reconnectionStatus === 'success' && (
            <button
              onClick={onCancel}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors focus:outline-none focus:ring-2 focus:ring-green-500 font-medium"
            >
              Continue
            </button>
          )}
        </div>

        {/* Progress Bar */}
        {reconnectionStatus === 'idle' && (
          <div className="mt-4">
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-1000"
                style={{ 
                  width: `${(timeRemaining / reconnectionWindow) * 100}%` 
                }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReconnectionModal;
