import React from 'react';
import { LogOut, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Shield } from 'lucide-react';
import { clsx } from 'clsx';

interface LeaveRoomButtonProps {
  roomId: string;
  roomName: string;
  isLeaving: boolean;
  onLeave: (roomId: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'danger';
  showConfirmation?: boolean;
  isPlayerReady?: boolean;
  canLeaveWhileReady?: boolean;
}

const LeaveRoomButton: React.FC<LeaveRoomButtonProps> = ({
  roomId,
  roomName,
  isLeaving,
  onLeave,
  className,
  size = 'md',
  variant = 'danger',
  showConfirmation = true,
  isPlayerReady = false,
  canLeaveWhileReady = false
}) => {
  const [showConfirmModal, setShowConfirmModal] = React.useState(false);
  const [showReadyWarning, setShowReadyWarning] = React.useState(false);

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
  };

  const handleLeaveClick = () => {
    if (isLeaving) {
      return;
    }

    // Check if player is ready and cannot leave while ready
    if (isPlayerReady && !canLeaveWhileReady) {
      setShowReadyWarning(true);
      return;
    }

    if (showConfirmation) {
      setShowConfirmModal(true);
    } else {
      onLeave(roomId);
    }
  };

  const confirmLeave = () => {
    onLeave(roomId);
    setShowConfirmModal(false);
  };

  const cancelLeave = () => {
    setShowConfirmModal(false);
  };

  const getButtonText = () => {
    if (isLeaving) return 'Leaving...';
    if (isPlayerReady && !canLeaveWhileReady) return 'Cannot Leave (Ready)';
    return 'Leave Room';
  };

  const getButtonIcon = () => {
    if (isLeaving) return <Loader2 className={clsx(iconSizes[size], 'animate-spin')} />;
    if (isPlayerReady && !canLeaveWhileReady) return <Shield className={iconSizes[size]} />;
    return <LogOut className={iconSizes[size]} />;
  };

  const isButtonDisabled = () => {
    return isLeaving || (isPlayerReady && !canLeaveWhileReady);
  };

  return (
    <>
      {/* Leave Button */}
      <button
        onClick={handleLeaveClick}
        disabled={isButtonDisabled()}
        className={clsx(
          'flex items-center justify-center gap-2 font-medium rounded-md transition-colors focus:outline-none focus:ring-2',
          sizeClasses[size],
          isButtonDisabled()
            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
            : variantClasses[variant],
          className
        )}
        title={
          isLeaving
            ? 'Leaving room...'
            : isPlayerReady && !canLeaveWhileReady
            ? 'Cannot leave room while ready. Please unready first.'
            : 'Leave this room'
        }
      >
        {getButtonIcon()}
        <span>{getButtonText()}</span>
      </button>

      {/* Confirmation Modal */}
      {showConfirmModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <AlertTriangle className="w-6 h-6 text-yellow-500 mr-3" />
                <h3 className="text-lg font-medium text-gray-900">
                  Leave Room
                </h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-600">
                  Are you sure you want to leave <strong>"{roomName}"</strong>?
                </p>
                <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <div className="flex items-start">
                    <AlertTriangle className="w-4 h-4 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-yellow-700">
                      <p className="font-medium">Important:</p>
                      <ul className="mt-1 list-disc list-inside space-y-1">
                        <li>You will lose your spot in this room</li>
                        <li>If a game is in progress, you may forfeit your bet</li>
                        <li>You'll need to rejoin manually if you want to return</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={cancelLeave}
                  className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
                <button
                  onClick={confirmLeave}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 font-medium"
                >
                  Leave Room
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Ready Warning Modal */}
      {showReadyWarning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="flex-shrink-0">
                <Shield className="w-6 h-6 text-yellow-500" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Cannot Leave While Ready</h3>
                <p className="text-sm text-gray-600 mt-1">
                  You cannot leave the room while in ready state. Please unready first, then try leaving again.
                </p>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4">
              <div className="flex">
                <AlertTriangle className="w-5 h-5 text-yellow-400 mr-2 flex-shrink-0 mt-0.5" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">Enhanced Leave Protection</p>
                  <p className="mt-1">
                    This prevents accidental game disruption. If you disconnect while ready,
                    you'll have 5 minutes to reconnect and resume the game.
                  </p>
                </div>
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setShowReadyWarning(false)}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 font-medium"
              >
                Got It
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default LeaveRoomButton;
