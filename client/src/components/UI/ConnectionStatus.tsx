import React from 'react';
import { Wifi, WifiOff, RefreshCw, AlertTriangle } from 'lucide-react';
import { useSocketStore } from '@/store/socketStore';
import { clsx } from 'clsx';

interface ConnectionStatusProps {
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  className,
  showText = true,
  size = 'md'
}) => {
  const { connectionInfo, connect } = useSocketStore();

  const handleReconnect = async () => {
    try {
      await connect();
    } catch (error) {
      console.error('Manual reconnect failed:', error);
    }
  };

  const getStatusConfig = () => {
    switch (connectionInfo.state) {
      case 'connected':
        return {
          icon: Wifi,
          color: 'text-green-500',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          text: 'Connected',
          description: 'Real-time updates active'
        };
      case 'connecting':
      case 'reconnecting':
        return {
          icon: RefreshCw,
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          text: connectionInfo.state === 'connecting' ? 'Connecting...' : 'Reconnecting...',
          description: `Attempt ${connectionInfo.reconnectAttempts + 1}`,
          animate: true
        };
      case 'error':
        return {
          icon: AlertTriangle,
          color: 'text-red-500',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          text: 'Connection Error',
          description: connectionInfo.error || 'Failed to connect'
        };
      case 'disconnected':
      default:
        return {
          icon: WifiOff,
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          text: 'Disconnected',
          description: 'No real-time updates'
        };
    }
  };

  const config = getStatusConfig();
  const Icon = config.icon;

  const sizeClasses = {
    sm: {
      icon: 'w-3 h-3',
      text: 'text-xs',
      padding: 'px-2 py-1',
      gap: 'gap-1'
    },
    md: {
      icon: 'w-4 h-4',
      text: 'text-sm',
      padding: 'px-3 py-1.5',
      gap: 'gap-2'
    },
    lg: {
      icon: 'w-5 h-5',
      text: 'text-base',
      padding: 'px-4 py-2',
      gap: 'gap-3'
    }
  };

  const sizeConfig = sizeClasses[size];

  if (!showText) {
    return (
      <div
        className={clsx(
          'inline-flex items-center justify-center rounded-full',
          config.bgColor,
          config.borderColor,
          'border',
          sizeConfig.padding,
          className
        )}
        title={`${config.text}: ${config.description}`}
      >
        <Icon
          className={clsx(
            sizeConfig.icon,
            config.color,
            config.animate && 'animate-spin'
          )}
        />
      </div>
    );
  }

  return (
    <div
      className={clsx(
        'inline-flex items-center rounded-full border',
        config.bgColor,
        config.borderColor,
        sizeConfig.padding,
        sizeConfig.gap,
        className
      )}
    >
      <Icon
        className={clsx(
          sizeConfig.icon,
          config.color,
          config.animate && 'animate-spin'
        )}
      />
      {showText && (
        <div className="flex flex-col">
          <span className={clsx('font-medium', config.color, sizeConfig.text)}>
            {config.text}
          </span>
          {config.description && size !== 'sm' && (
            <span className={clsx('text-gray-500', 'text-xs')}>
              {config.description}
            </span>
          )}
        </div>
      )}

      {/* Show reconnect button for error state */}
      {connectionInfo.state === 'error' && (
        <button
          onClick={handleReconnect}
          className={clsx(
            'ml-2 text-xs px-2 py-1 rounded bg-red-100 text-red-700 hover:bg-red-200 transition-colors',
            size === 'sm' && 'text-xs px-1 py-0.5'
          )}
        >
          Retry
        </button>
      )}
    </div>
  );
};

export default ConnectionStatus;
