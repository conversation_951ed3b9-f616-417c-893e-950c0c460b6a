import React from 'react';
import { Users, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { clsx } from 'clsx';

interface RoomCapacityProps {
  currentPlayers: number;
  maxPlayers: number;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  showPercentage?: boolean;
  showBadge?: boolean;
  className?: string;
}

const RoomCapacity: React.FC<RoomCapacityProps> = ({
  currentPlayers,
  maxPlayers,
  size = 'md',
  showIcon = true,
  showPercentage = false,
  showBadge = false,
  className
}) => {
  const percentage = maxPlayers > 0 ? (currentPlayers / maxPlayers) * 100 : 0;
  const isFull = currentPlayers >= maxPlayers;
  const isNearFull = percentage >= 75 && !isFull;
  const isEmpty = currentPlayers === 0;

  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  const iconSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  const getCapacityColor = () => {
    if (isFull) return 'text-red-600';
    if (isNearFull) return 'text-yellow-600';
    if (isEmpty) return 'text-gray-400';
    return 'text-green-600';
  };

  const getCapacityIcon = () => {
    if (isFull) return <XCircle className={clsx(iconSizes[size], 'text-red-500')} />;
    if (isNearFull) return <AlertTriangle className={clsx(iconSizes[size], 'text-yellow-500')} />;
    return <CheckCircle className={clsx(iconSizes[size], 'text-green-500')} />;
  };

  const getCapacityBadge = () => {
    if (isFull) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
          FULL
        </span>
      );
    }
    if (isNearFull) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
          ALMOST FULL
        </span>
      );
    }
    if (isEmpty) {
      return (
        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
          EMPTY
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
        AVAILABLE
      </span>
    );
  };

  const getProgressBarColor = () => {
    if (isFull) return 'bg-red-500';
    if (isNearFull) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <div className={clsx('flex items-center space-x-2', className)}>
      {/* Icon */}
      {showIcon && (
        <div className="flex-shrink-0">
          {isFull || isNearFull ? (
            getCapacityIcon()
          ) : (
            <Users className={clsx(iconSizes[size], getCapacityColor())} />
          )}
        </div>
      )}

      {/* Capacity Text */}
      <div className="flex items-center space-x-2">
        <span className={clsx(sizeClasses[size], getCapacityColor(), 'font-medium')}>
          {currentPlayers}/{maxPlayers}
        </span>

        {showPercentage && (
          <span className={clsx(sizeClasses[size], 'text-gray-500')}>
            ({Math.round(percentage)}%)
          </span>
        )}

        {/* Progress Bar for larger sizes */}
        {size !== 'sm' && (
          <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden progress-bar">
            <div
              className={clsx('h-full transition-all duration-300 progress-fill', getProgressBarColor())}
              style={{ width: `${Math.min(percentage, 100)}%` }}
            />
          </div>
        )}
      </div>

      {/* Badge */}
      {showBadge && (
        <div className="flex-shrink-0">
          {getCapacityBadge()}
        </div>
      )}
    </div>
  );
};

export default RoomCapacity;
