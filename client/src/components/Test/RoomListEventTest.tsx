/**
 * Room List Event Test Component
 * Tests the handling of room_list_updated events, especially the new 'subscribe' action
 */

import React, { useState, useEffect } from 'react';
import { socketService } from '@/services/socket';
import { useLobbyStore, useRoomListUpdates } from '@/store/lobbyStore';
import type { RoomListUpdateData } from '@/types/socket';

interface EventLog {
  id: string;
  timestamp: string;
  action: string;
  data: RoomListUpdateData;
  processed: boolean;
}

export const RoomListEventTest: React.FC = () => {
  const [eventLogs, setEventLogs] = useState<EventLog[]>([]);
  const [isListening, setIsListening] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<string>('disconnected');
  
  const { 
    isSubscribed, 
    subscriptionLoading, 
    subscriptionError, 
    lastUpdate, 
    updateCount,
    subscribeLobby,
    unsubscribeLobby,
    clearError 
  } = useLobbyStore();

  // Monitor socket connection status
  useEffect(() => {
    const updateConnectionStatus = () => {
      setConnectionStatus(socketService.isConnected ? 'connected' : 'disconnected');
    };

    updateConnectionStatus();
    const interval = setInterval(updateConnectionStatus, 1000);

    return () => clearInterval(interval);
  }, []);

  // Listen to room list updates
  useRoomListUpdates((data: RoomListUpdateData) => {
    const newLog: EventLog = {
      id: `${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
      action: data.action,
      data,
      processed: true,
    };

    setEventLogs(prev => [newLog, ...prev.slice(0, 19)]); // Keep last 20 events
  });

  // Direct socket event listener for comparison
  useEffect(() => {
    if (!isListening) return;

    const handleRoomListUpdate = (data: RoomListUpdateData) => {
      const newLog: EventLog = {
        id: `direct-${Date.now()}-${Math.random()}`,
        timestamp: new Date().toISOString(),
        action: `direct:${data.action}`,
        data,
        processed: false,
      };

      setEventLogs(prev => [newLog, ...prev.slice(0, 19)]);
    };

    socketService.on('room_list_updated', handleRoomListUpdate);

    return () => {
      socketService.off('room_list_updated', handleRoomListUpdate);
    };
  }, [isListening]);

  const handleSubscribeLobby = async () => {
    try {
      clearError();
      await subscribeLobby();
    } catch (error) {
      console.error('Failed to subscribe to lobby:', error);
    }
  };

  const handleUnsubscribeLobby = async () => {
    try {
      clearError();
      await unsubscribeLobby();
    } catch (error) {
      console.error('Failed to unsubscribe from lobby:', error);
    }
  };

  const clearLogs = () => {
    setEventLogs([]);
  };

  const formatRoomData = (rooms: any[]) => {
    if (!rooms || !Array.isArray(rooms)) return 'No rooms';
    
    return rooms.map(room => ({
      id: room.id,
      name: room.name,
      gameType: room.gameType || room.game_type,
      players: `${room.playerCount || room.current_players}/${room.maxPlayers || room.max_players}`,
      status: room.status,
      bet: room.betAmount || room.bet_amount,
    }));
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold mb-4">Room List Event Test</h2>
        
        {/* Connection Status */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-sm text-gray-600">Connection</div>
            <div className={`font-semibold ${connectionStatus === 'connected' ? 'text-green-600' : 'text-red-600'}`}>
              {connectionStatus}
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-sm text-gray-600">Lobby Subscription</div>
            <div className={`font-semibold ${isSubscribed ? 'text-green-600' : 'text-gray-600'}`}>
              {subscriptionLoading ? 'Loading...' : isSubscribed ? 'Subscribed' : 'Not Subscribed'}
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-sm text-gray-600">Update Count</div>
            <div className="font-semibold text-blue-600">{updateCount}</div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded">
            <div className="text-sm text-gray-600">Event Logs</div>
            <div className="font-semibold text-purple-600">{eventLogs.length}</div>
          </div>
        </div>

        {/* Error Display */}
        {subscriptionError && (
          <div className="bg-red-50 border border-red-200 rounded p-3 mb-4">
            <div className="text-red-800 font-semibold">Subscription Error:</div>
            <div className="text-red-600">{subscriptionError}</div>
          </div>
        )}

        {/* Controls */}
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={handleSubscribeLobby}
            disabled={subscriptionLoading || isSubscribed}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400"
          >
            Subscribe to Lobby
          </button>
          
          <button
            onClick={handleUnsubscribeLobby}
            disabled={subscriptionLoading || !isSubscribed}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:bg-gray-400"
          >
            Unsubscribe from Lobby
          </button>
          
          <button
            onClick={() => setIsListening(!isListening)}
            className={`px-4 py-2 rounded text-white ${isListening ? 'bg-orange-600 hover:bg-orange-700' : 'bg-blue-600 hover:bg-blue-700'}`}
          >
            {isListening ? 'Stop Direct Listening' : 'Start Direct Listening'}
          </button>
          
          <button
            onClick={clearLogs}
            className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700"
          >
            Clear Logs
          </button>
        </div>

        {/* Last Update Summary */}
        {lastUpdate && (
          <div className="bg-blue-50 border border-blue-200 rounded p-4 mb-6">
            <h3 className="font-semibold text-blue-800 mb-2">Last Update Summary</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
              <div>
                <span className="text-blue-600 font-medium">Action:</span> {lastUpdate.action}
              </div>
              <div>
                <span className="text-blue-600 font-medium">Timestamp:</span> {lastUpdate.timestamp || 'N/A'}
              </div>
              <div>
                <span className="text-blue-600 font-medium">Room Count:</span> {lastUpdate.rooms?.length || (lastUpdate as any).count || 0}
              </div>
              <div>
                <span className="text-blue-600 font-medium">Source:</span> {(lastUpdate as any).source || 'N/A'}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Event Logs */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-bold mb-4">Event Logs</h3>
        
        {eventLogs.length === 0 ? (
          <div className="text-gray-500 text-center py-8">
            No events logged yet. Subscribe to lobby to start receiving events.
          </div>
        ) : (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {eventLogs.map((log) => (
              <div
                key={log.id}
                className={`border rounded p-3 ${log.processed ? 'bg-green-50 border-green-200' : 'bg-yellow-50 border-yellow-200'}`}
              >
                <div className="flex justify-between items-start mb-2">
                  <div className="font-semibold text-gray-800">
                    Action: {log.action}
                  </div>
                  <div className="text-xs text-gray-500">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </div>
                </div>
                
                <div className="text-sm space-y-1">
                  {log.data.action === 'subscribe' || log.data.action === 'unsubscribe' ? (
                    <>
                      <div><strong>Count:</strong> {(log.data as any).count}</div>
                      <div><strong>User:</strong> {(log.data as any).username} ({(log.data as any).userId})</div>
                      <div><strong>Source:</strong> {(log.data as any).source}</div>
                      <div><strong>Socket ID:</strong> {(log.data as any).socketId}</div>
                    </>
                  ) : null}
                  
                  {log.data.rooms && (
                    <details className="mt-2">
                      <summary className="cursor-pointer font-medium">
                        Rooms ({log.data.rooms.length})
                      </summary>
                      <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                        {JSON.stringify(formatRoomData(log.data.rooms), null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default RoomListEventTest;
