/**
 * Enhanced Room Join <PERSON><PERSON> Component
 * Demonstrates the new room subscription management with automatic lobby fallback
 */

import { useState } from 'react';
import { useRoomSubscription } from '@/hooks/useRoomSubscription';
import { Button } from '@/components/UI/Button';
import { Input } from '@/components/UI/Input';
import toast from 'react-hot-toast';

interface EnhancedRoomJoinButtonProps {
  roomId: string;
  roomName: string;
  playerCount: number;
  maxPlayers: number;
  betAmount: number;
  isPrivate?: boolean;
  onJoinSuccess?: (roomData: any) => void;
  onJoinFailure?: (error: Error) => void;
  className?: string;
}

export function EnhancedRoomJoinButton({
  roomId,
  roomName,
  playerCount,
  maxPlayers,
  betAmount,
  isPrivate = false,
  onJoinSuccess,
  onJoinFailure,
  className = '',
}: EnhancedRoomJoinButtonProps) {
  const [password, setPassword] = useState('');
  const [showPasswordInput, setShowPasswordInput] = useState(false);

  const {
    joinRoom,
    isJoining,
    waitingForRoomInfo,
    currentRoom,
    currentSubscription,
    error,
  } = useRoomSubscription({
    // Enable automatic lobby fallback on join failure
    autoLobbyFallback: true,
    
    // Handle real-time room info updates
    onRoomInfoUpdate: (update) => {
      if (update.roomId === roomId) {
        // Show toast for room updates
        if (update.action === 'player_joined') {
          toast.success(`${update.playerName || 'A player'} joined ${roomName}`);
        } else if (update.action === 'player_left') {
          toast(`${update.playerName || 'A player'} left ${roomName}`);
        }
      }
    },
    
    // Handle subscription changes
    onSubscriptionChange: (type, subscribedRoomId) => {
      if (type === 'room' && subscribedRoomId === roomId) {
        console.log(`Successfully subscribed to room ${roomName} events`);
      } else if (type === 'lobby') {
        console.log('Returned to lobby - room listings will be updated');
      }
    },
  });

  const handleJoinRoom = async () => {
    try {
      // Validate password for private rooms
      if (isPrivate && !password.trim()) {
        setShowPasswordInput(true);
        toast.error('Password required for private room');
        return;
      }

      // Attempt to join room with enhanced subscription management
      await joinRoom(roomId, isPrivate ? password : undefined, betAmount);
      
      // Success callback
      onJoinSuccess?.({ roomId, roomName });
      
      // Show success message
      toast.success(`Successfully joined ${roomName}!`);
      
      // Reset password input
      setPassword('');
      setShowPasswordInput(false);
      
    } catch (error) {
      console.error('Enhanced room join failed:', error);
      
      // Failure callback
      onJoinFailure?.(error as Error);
      
      // Error toast is already shown by the subscription manager
      // but we can add specific handling here if needed
      const errorMessage = (error as Error).message;
      
      if (errorMessage.includes('password')) {
        setShowPasswordInput(true);
        toast.error('Invalid password. Please try again.');
      } else if (errorMessage.includes('full')) {
        toast.error('Room is full. You\'ve been returned to the lobby.');
      } else if (errorMessage.includes('balance')) {
        toast.error('Insufficient balance to join this room.');
      }
    }
  };

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleJoinRoom();
  };

  // Don't show join button if already in this room
  if (currentRoom?.id === roomId) {
    return (
      <div className={`text-center ${className}`}>
        <span className="text-green-600 font-medium">✓ In Room</span>
        <div className="text-xs text-gray-500 mt-1">
          Subscribed to room events
        </div>
      </div>
    );
  }

  // Don't show join button if in another room
  if (currentRoom && currentRoom.id !== roomId) {
    return (
      <div className={`text-center ${className}`}>
        <span className="text-gray-500 text-sm">In another room</span>
      </div>
    );
  }

  const isRoomFull = playerCount >= maxPlayers;
  const isProcessing = isJoining || waitingForRoomInfo;
  const canJoin = !isProcessing && !isRoomFull && currentSubscription !== null;

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Password input for private rooms */}
      {isPrivate && showPasswordInput && (
        <form onSubmit={handlePasswordSubmit} className="space-y-2">
          <Input
            type="password"
            placeholder="Room password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            className="text-sm"
            autoFocus
          />
          <div className="flex gap-1">
            <Button
              type="submit"
              disabled={!password.trim() || isProcessing}
              className="flex-1 text-xs py-1"
            >
              {isJoining ? 'Joining...' : waitingForRoomInfo ? 'Loading...' : 'Join'}
            </Button>
            <Button
              type="button"
              onClick={() => {
                setShowPasswordInput(false);
                setPassword('');
              }}
              className="px-2 text-xs py-1 bg-gray-200 hover:bg-gray-300"
            >
              Cancel
            </Button>
          </div>
        </form>
      )}

      {/* Main join button */}
      {(!isPrivate || !showPasswordInput) && (
        <Button
          onClick={handleJoinRoom}
          disabled={!canJoin}
          className={`w-full ${
            isRoomFull 
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed' 
              : canJoin
              ? 'bg-blue-500 hover:bg-blue-600 text-white'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          {isJoining ? (
            <span className="flex items-center justify-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Joining...
            </span>
          ) : waitingForRoomInfo ? (
            <span className="flex items-center justify-center gap-2">
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Loading room info...
            </span>
          ) : isRoomFull ? (
            'Room Full'
          ) : isPrivate && !showPasswordInput ? (
            '🔒 Join Private Room'
          ) : (
            `Join (${betAmount} coins)`
          )}
        </Button>
      )}

      {/* Room status indicators */}
      <div className="text-xs text-center space-y-1">
        <div className="text-gray-600">
          {playerCount}/{maxPlayers} players
        </div>
        
        {error && (
          <div className="text-red-500">
            Join failed - returned to lobby
          </div>
        )}
        
        {currentSubscription === 'lobby' && (
          <div className="text-blue-500">
            📡 Receiving live room updates
          </div>
        )}
      </div>
    </div>
  );
}

export default EnhancedRoomJoinButton;
