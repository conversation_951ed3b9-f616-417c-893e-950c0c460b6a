/**
 * Room Player Display Component
 * Simple, easy-to-integrate component for showing players in slots
 * Automatically handles room_info_updated events
 */

import { useState } from 'react';
import { useRoomSubscription } from '@/hooks/useRoomSubscription';

interface Player {
  userId: string;
  username: string;
  position: number;
  isReady: boolean;
  betAmount: number;
  joinedAt: string;
}

interface RoomPlayerDisplayProps {
  /** Maximum number of player slots to show */
  maxSlots?: number;
  /** Show ready status indicators */
  showReady?: boolean;
  /** Show player colors (for games like Prize Wheel) */
  showColors?: boolean;
  /** Compact layout for smaller spaces */
  compact?: boolean;
  /** Custom CSS classes */
  className?: string;
}

export function RoomPlayerDisplay({
  maxSlots = 4,
  showReady = true,
  showColors = true,
  compact = false,
  className = '',
}: RoomPlayerDisplayProps) {
  const [players, setPlayers] = useState<Player[]>([]);
  const [gameData, setGameData] = useState<any>(null);

  // Subscribe to room updates
  useRoomSubscription({
    onRoomInfoUpdate: (update) => {
      // Extract players from room_info_updated event
      if (update.players) {
        const updatedPlayers: Player[] = update.players.map((p: any) => ({
          userId: p.userId,
          username: p.username,
          position: p.position,
          isReady: p.isReady,
          betAmount: p.betAmount,
          joinedAt: p.joinedAt,
        }));
        
        setPlayers(updatedPlayers);
      }

      // Store game-specific data for color mapping
      if (update.gameSpecificData) {
        setGameData(update.gameSpecificData);
      }
    },
  });

  // Create slots array
  const slots = Array.from({ length: maxSlots }, (_, index) => {
    // Fix: Server sends 1-based positions, so match with index + 1
    const player = players.find(p => p.position === index + 1);
    return { slotIndex: index, player };
  });

  // Get player color
  const getPlayerColor = (userId: string): string => {
    if (!showColors || !gameData) return '#3b82f6';
    
    const colorId = gameData.playerColorMappings?.[userId] || gameData.colorSelections?.[userId];
    
    const colorMap: Record<string, string> = {
      red: '#ef4444',
      blue: '#3b82f6', 
      green: '#10b981',
      yellow: '#f59e0b',
      purple: '#8b5cf6',
      orange: '#f97316',
      pink: '#ec4899',
      teal: '#14b8a6',
    };
    
    return colorMap[colorId] || '#3b82f6';
  };

  if (compact) {
    // Compact horizontal layout
    return (
      <div className={`flex gap-2 ${className}`}>
        {slots.map(({ slotIndex, player }) => (
          <div
            key={slotIndex}
            className={`
              w-12 h-12 rounded-full flex items-center justify-center text-white text-sm font-bold
              ${player ? 'shadow-md' : 'border-2 border-dashed border-gray-300 text-gray-400'}
            `}
            style={player ? { backgroundColor: getPlayerColor(player.userId) } : {}}
            title={player ? `${player.username} ${showReady ? (player.isReady ? '(Ready)' : '(Not Ready)') : ''}` : `Slot ${slotIndex + 1} - Empty`}
          >
            {player ? (
              <>
                {player.username.charAt(0).toUpperCase()}
                {showReady && (
                  <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full ${
                    player.isReady ? 'bg-green-500' : 'bg-yellow-500'
                  }`} />
                )}
              </>
            ) : (
              '+'
            )}
          </div>
        ))}
      </div>
    );
  }

  // Full card layout
  return (
    <div className={`grid grid-cols-2 md:grid-cols-4 gap-3 ${className}`}>
      {slots.map(({ slotIndex, player }) => (
        <div
          key={slotIndex}
          className={`
            border rounded-lg p-3 text-center transition-all duration-200
            ${player 
              ? 'border-blue-200 bg-blue-50 hover:bg-blue-100' 
              : 'border-gray-200 bg-gray-50 border-dashed'
            }
          `}
        >
          {player ? (
            <div>
              {/* Avatar */}
              <div
                className="w-10 h-10 rounded-full flex items-center justify-center text-white font-bold mx-auto mb-2 relative"
                style={{ backgroundColor: getPlayerColor(player.userId) }}
              >
                {player.username.charAt(0).toUpperCase()}
                
                {/* Ready indicator */}
                {showReady && (
                  <div className={`
                    absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white
                    ${player.isReady ? 'bg-green-500' : 'bg-yellow-500'}
                  `} />
                )}
              </div>

              {/* Name */}
              <div className="font-medium text-sm truncate" title={player.username}>
                {player.username}
              </div>

              {/* Status */}
              {showReady && (
                <div className={`text-xs mt-1 ${
                  player.isReady ? 'text-green-600' : 'text-yellow-600'
                }`}>
                  {player.isReady ? '✓ Ready' : '⏳ Waiting'}
                </div>
              )}

              {/* Bet amount */}
              <div className="text-xs text-gray-500 mt-1">
                {player.betAmount} coins
              </div>
            </div>
          ) : (
            <div className="text-gray-400">
              <div className="w-10 h-10 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center mx-auto mb-2">
                <span className="text-lg">+</span>
              </div>
              <div className="text-sm">Slot {slotIndex + 1}</div>
              <div className="text-xs">Empty</div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}

// Simplified version for quick integration
export function SimplePlayerSlots({ className = '' }: { className?: string }) {
  return (
    <RoomPlayerDisplay
      maxSlots={4}
      showReady={true}
      showColors={true}
      compact={false}
      className={className}
    />
  );
}

// Compact version for headers/sidebars
export function CompactPlayerSlots({ className = '' }: { className?: string }) {
  return (
    <RoomPlayerDisplay
      maxSlots={4}
      showReady={true}
      showColors={true}
      compact={true}
      className={className}
    />
  );
}

export default RoomPlayerDisplay;
