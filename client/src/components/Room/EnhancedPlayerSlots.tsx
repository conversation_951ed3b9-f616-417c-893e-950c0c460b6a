/**
 * Enhanced Player Slots Component
 * Handles the exact room_info_updated event structure you provided
 * Shows players in their assigned slots with real-time updates
 */

import { useState } from 'react';
import { useRoomSubscription } from '@/hooks/useRoomSubscription';
import { useSocketStore } from '@/store/socketStore';

interface RoomInfoUpdateData {
  room: {
    id: string;
    name: string;
    gameType: string;
    status: string;
    playerCount: number;
    maxPlayers: number;
    betAmount: number;
    prizePool: number;
    isPrivate: boolean;
    createdAt: string;
    updatedAt: string;
  };
  roomState: {
    playerCount: number;
    readyCount: number;
    canStartGame: boolean;
    prizePool: number;
    gameInProgress: boolean;
    countdown: number | null;
  };
  players: Array<{
    betAmount: number;
    isReady: boolean;
    joinedAt: string;
    position: number;
    userId: string;
    username: string;
  }>;
  gameConfig: {
    betAmount: number;
    gameType: string;
    maxPlayers: number;
    minPlayers: number;
    settings: any;
  };
  gameSpecificData: {
    gameType: string;
    colorSelections: Record<string, string>;
    availableColors: string[];
    playerColorMappings: Record<string, string>;
    colorSelectionTimestamps: Record<string, string>;
  };
  timestamp: string;
}

interface EnhancedPlayerSlotsProps {
  showColorSelection?: boolean;
  showReadyStatus?: boolean;
  showBetAmount?: boolean;
  showJoinTime?: boolean;
  className?: string;
}

export function EnhancedPlayerSlots({
  showColorSelection = true,
  showReadyStatus = true,
  showBetAmount = true,
  showJoinTime = false,
  className = '',
}: EnhancedPlayerSlotsProps) {
  const [roomData, setRoomData] = useState<RoomInfoUpdateData | null>(null);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);
  
  const { currentRoom } = useSocketStore();

  // Subscribe to room info updates
  useRoomSubscription({
    onRoomInfoUpdate: (update) => {
      console.log('EnhancedPlayerSlots: Room info update received:', update);
      
      // The update should match the RoomInfoUpdateData structure
      if (update.room && update.roomState && update.players) {
        setRoomData(update as RoomInfoUpdateData);
        setLastUpdateTime(new Date());
      }
    },
  });

  // Create slots array based on maxPlayers from room data
  const maxPlayers = roomData?.room?.maxPlayers || currentRoom?.maxPlayers || 4;
  const players = roomData?.players || [];
  
  const slots = Array.from({ length: maxPlayers }, (_, index) => {
    // Fix: Server sends 1-based positions, so match with index + 1
    const player = players.find(p => p.position === index + 1);
    return {
      slotIndex: index,
      player: player || null,
    };
  });

  const getColorForPlayer = (userId: string): { colorId: string; colorHex: string } | null => {
    if (!roomData?.gameSpecificData) return null;
    
    const colorId = roomData.gameSpecificData.playerColorMappings[userId] || 
                   roomData.gameSpecificData.colorSelections[userId];
    
    if (!colorId) return null;
    
    // Map color names to hex values
    const colorMap: Record<string, string> = {
      red: '#ef4444',
      blue: '#3b82f6',
      green: '#10b981',
      yellow: '#f59e0b',
      purple: '#8b5cf6',
      orange: '#f97316',
      pink: '#ec4899',
      teal: '#14b8a6',
    };
    
    return {
      colorId,
      colorHex: colorMap[colorId] || '#6b7280',
    };
  };

  const formatJoinTime = (joinedAt: string) => {
    const date = new Date(joinedAt);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getReadyStatusColor = (isReady: boolean) => {
    return isReady ? 'text-green-600 bg-green-100' : 'text-yellow-600 bg-yellow-100';
  };

  const getGameTypeIcon = (gameType: string) => {
    switch (gameType.toLowerCase()) {
      case 'prizewheel':
        return '🎡';
      case 'amidakuji':
        return '🎯';
      default:
        return '🎮';
    }
  };

  if (!roomData && !currentRoom) {
    return (
      <div className={`player-slots ${className}`}>
        <div className="text-center py-8 text-gray-500">
          <p>No room data available</p>
          <p className="text-sm mt-2">Join a room to see player slots</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`player-slots ${className}`}>
      {/* Room Header */}
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border">
        <div className="flex justify-between items-start mb-3">
          <div>
            <h3 className="text-xl font-bold text-gray-800">
              {getGameTypeIcon(roomData?.room?.gameType || 'game')} {roomData?.room?.name || currentRoom?.name}
            </h3>
            <p className="text-sm text-gray-600 capitalize">
              {roomData?.room?.gameType || currentRoom?.gameType} • {roomData?.room?.status || currentRoom?.status}
            </p>
          </div>
          <div className="text-right">
            <div className="text-lg font-semibold text-blue-600">
              {roomData?.roomState?.playerCount || players.length}/{maxPlayers}
            </div>
            <div className="text-xs text-gray-500">Players</div>
          </div>
        </div>

        {/* Room Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div className="text-center p-2 bg-white rounded">
            <div className="font-semibold text-green-600">{roomData?.roomState?.readyCount || 0}</div>
            <div className="text-xs text-gray-500">Ready</div>
          </div>
          <div className="text-center p-2 bg-white rounded">
            <div className="font-semibold text-blue-600">{roomData?.room?.betAmount || currentRoom?.betAmount || 0}</div>
            <div className="text-xs text-gray-500">Bet Amount</div>
          </div>
          <div className="text-center p-2 bg-white rounded">
            <div className="font-semibold text-purple-600">{roomData?.roomState?.prizePool || 0}</div>
            <div className="text-xs text-gray-500">Prize Pool</div>
          </div>
          <div className="text-center p-2 bg-white rounded">
            <div className={`font-semibold ${roomData?.roomState?.canStartGame ? 'text-green-600' : 'text-gray-400'}`}>
              {roomData?.roomState?.canStartGame ? '✓' : '✗'}
            </div>
            <div className="text-xs text-gray-500">Can Start</div>
          </div>
        </div>

        {/* Countdown */}
        {roomData?.roomState?.countdown && roomData.roomState.countdown > 0 && (
          <div className="mt-3 text-center">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-orange-100 text-orange-800 rounded-full font-medium">
              <span className="animate-pulse">⏱️</span>
              Game starting in {roomData.roomState.countdown}s
            </div>
          </div>
        )}
      </div>

      {/* Player Slots Grid */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        {slots.map(({ slotIndex, player }) => {
          const playerColor = player ? getColorForPlayer(player.userId) : null;
          
          return (
            <div
              key={slotIndex}
              className={`
                relative border-2 rounded-xl p-4 min-h-[140px] flex flex-col justify-center items-center
                transition-all duration-300 hover:shadow-md
                ${player 
                  ? 'border-blue-300 bg-gradient-to-br from-blue-50 to-blue-100' 
                  : 'border-gray-200 bg-gray-50 border-dashed hover:border-gray-300'
                }
              `}
            >
              {/* Slot Number */}
              <div className="absolute top-2 left-2 text-xs font-medium text-gray-400">
                #{slotIndex + 1}
              </div>

              {player ? (
                <div className="text-center w-full">
                  {/* Player Avatar */}
                  <div className="relative mb-3">
                    <div 
                      className="w-14 h-14 rounded-full flex items-center justify-center text-white font-bold text-lg mx-auto shadow-md"
                      style={{ 
                        backgroundColor: playerColor?.colorHex || '#3b82f6',
                        border: '3px solid white',
                        boxShadow: '0 4px 12px rgba(0,0,0,0.15)'
                      }}
                    >
                      {player.username.charAt(0).toUpperCase()}
                    </div>
                    
                    {/* Ready Status Badge */}
                    {showReadyStatus && (
                      <div className={`
                        absolute -bottom-1 -right-1 px-2 py-1 rounded-full text-xs font-medium
                        ${getReadyStatusColor(player.isReady)}
                      `}>
                        {player.isReady ? '✓' : '⏳'}
                      </div>
                    )}
                  </div>

                  {/* Player Name */}
                  <div className="font-semibold text-sm mb-2 truncate" title={player.username}>
                    {player.username}
                  </div>

                  {/* Player Details */}
                  <div className="space-y-1 text-xs">
                    {showBetAmount && (
                      <div className="text-gray-600">
                        💰 {player.betAmount} coins
                      </div>
                    )}

                    {showColorSelection && playerColor && (
                      <div className="flex items-center justify-center gap-1">
                        <div 
                          className="w-3 h-3 rounded-full border border-gray-300"
                          style={{ backgroundColor: playerColor.colorHex }}
                        ></div>
                        <span className="capitalize text-gray-600">{playerColor.colorId}</span>
                      </div>
                    )}

                    {showJoinTime && (
                      <div className="text-gray-400">
                        🕐 {formatJoinTime(player.joinedAt)}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center text-gray-400">
                  <div className="w-14 h-14 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center mb-3 mx-auto">
                    <span className="text-2xl">+</span>
                  </div>
                  <div className="text-sm font-medium">Empty Slot</div>
                  <div className="text-xs mt-1">Waiting for player</div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Color Selection Status (for Prize Wheel) */}
      {showColorSelection && roomData?.gameSpecificData?.gameType === 'prizewheel' && (
        <div className="mb-4 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-sm mb-3">Color Selection Status</h4>
          <div className="grid grid-cols-4 md:grid-cols-8 gap-2">
            {roomData.gameSpecificData.availableColors.map(color => {
              const isSelected = Object.values(roomData.gameSpecificData.colorSelections).includes(color) ||
                               Object.values(roomData.gameSpecificData.playerColorMappings).includes(color);
              const playerWithColor = players.find(p => 
                roomData.gameSpecificData.colorSelections[p.userId] === color ||
                roomData.gameSpecificData.playerColorMappings[p.userId] === color
              );
              
              const colorMap: Record<string, string> = {
                red: '#ef4444', blue: '#3b82f6', green: '#10b981', yellow: '#f59e0b',
                purple: '#8b5cf6', orange: '#f97316', pink: '#ec4899', teal: '#14b8a6',
              };
              
              return (
                <div key={color} className="text-center">
                  <div 
                    className={`w-8 h-8 rounded-full mx-auto mb-1 border-2 ${
                      isSelected ? 'border-gray-800' : 'border-gray-300'
                    }`}
                    style={{ backgroundColor: colorMap[color] || '#6b7280' }}
                  ></div>
                  <div className="text-xs capitalize">{color}</div>
                  {playerWithColor && (
                    <div className="text-xs text-gray-500 truncate" title={playerWithColor.username}>
                      {playerWithColor.username}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Last Update Info */}
      {lastUpdateTime && (
        <div className="text-xs text-gray-500 text-center">
          Last updated: {lastUpdateTime.toLocaleTimeString()}
        </div>
      )}
    </div>
  );
}

export default EnhancedPlayerSlots;
