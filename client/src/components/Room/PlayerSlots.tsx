/**
 * Player Slots Component
 * Displays players in their assigned slots with real-time updates from room_info_updated events
 */

import { useState, useEffect } from 'react';
import { useRoomSubscription } from '@/hooks/useRoomSubscription';
import { useSocketStore } from '@/store/socketStore';

interface Player {
  userId: string;
  username: string;
  betAmount: number;
  isReady: boolean;
  joinedAt: string;
  position: number;
  balance?: number;
  insufficientBalance?: boolean;
  colorId?: string;
  colorHex?: string;
}

interface PlayerSlotsProps {
  maxPlayers?: number;
  showBetAmount?: boolean;
  showReadyStatus?: boolean;
  showJoinTime?: boolean;
  showColorSelection?: boolean;
  className?: string;
}

export function PlayerSlots({
  maxPlayers = 4,
  showBetAmount = true,
  showReadyStatus = true,
  showJoinTime = false,
  showColorSelection = true,
  className = '',
}: PlayerSlotsProps) {
  const [players, setPlayers] = useState<Player[]>([]);
  const [roomInfo, setRoomInfo] = useState<any>(null);
  
  const { currentRoom } = useSocketStore();

  // Subscribe to room info updates
  useRoomSubscription({
    onRoomInfoUpdate: (update) => {
      console.log('PlayerSlots: Room info update received:', update);
      
      // Update room info
      setRoomInfo(update);
      
      // Update players from the room info update
      if (update.players) {
        const updatedPlayers = update.players.map((player: any) => ({
          userId: player.userId,
          username: player.username,
          betAmount: player.betAmount,
          isReady: player.isReady,
          joinedAt: player.joinedAt,
          position: player.position,
          balance: player.balance,
          insufficientBalance: player.insufficientBalance,
          colorId: player.colorId,
          colorHex: player.colorHex,
        }));

        console.log('PlayerSlots received players update:', {
          playersLength: updatedPlayers.length,
          maxPlayers,
          players: updatedPlayers
        });

        setPlayers(updatedPlayers);
      }
    },
  });

  // Initialize with current room players
  useEffect(() => {
    if (currentRoom?.players) {
      const initialPlayers = currentRoom.players.map(player => ({
        userId: player.userId,
        username: player.username,
        betAmount: currentRoom.betAmount || 0,
        isReady: player.isReady,
        joinedAt: new Date().toISOString(),
        position: player.position,
        balance: player.balance,
        insufficientBalance: player.insufficientBalance,
        colorId: player.colorId,
        colorHex: player.colorHex,
      }));
      
      setPlayers(initialPlayers);
    }
  }, [currentRoom]);

  // Create slots array with players positioned correctly
  const slots = Array.from({ length: maxPlayers }, (_, index) => {
    // Fix: Server sends 1-based positions, so match with index + 1
    const player = players.find(p => p.position === index + 1);
    return {
      slotIndex: index,
      player: player || null,
    };
  });

  // Debug slots creation
  console.log('PlayerSlots slots created:', {
    maxPlayers,
    playersCount: players.length,
    slots: slots.map(slot => ({
      slotIndex: slot.slotIndex,
      hasPlayer: !!slot.player,
      playerName: slot.player?.username,
      playerPosition: slot.player?.position
    }))
  });

  const formatJoinTime = (joinedAt: string) => {
    const date = new Date(joinedAt);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getReadyStatusColor = (isReady: boolean) => {
    return isReady ? 'text-green-600' : 'text-yellow-600';
  };

  const getReadyStatusText = (isReady: boolean) => {
    return isReady ? '✓ Ready' : '⏳ Not Ready';
  };

  return (
    <div className={`player-slots ${className}`}>
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">
          Players ({players.length}/{maxPlayers})
        </h3>
        
        {roomInfo && (
          <div className="text-sm text-gray-600 mb-3">
            <span>Ready: {roomInfo.readyCount || 0}/{players.length}</span>
            {roomInfo.canStartGame && (
              <span className="ml-4 text-green-600 font-medium">✓ Can Start Game</span>
            )}
          </div>
        )}
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {slots.map(({ slotIndex, player }) => (
          <div
            key={slotIndex}
            className={`
              border-2 rounded-lg p-4 min-h-[120px] flex flex-col justify-center items-center
              transition-all duration-200
              ${player 
                ? 'border-blue-300 bg-blue-50 hover:bg-blue-100' 
                : 'border-gray-200 bg-gray-50 border-dashed'
              }
            `}
          >
            {player ? (
              <div className="text-center w-full">
                {/* Player Avatar/Initial */}
                <div className={`
                  w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg mb-2 mx-auto
                  ${player.colorHex 
                    ? '' 
                    : 'bg-gradient-to-br from-blue-500 to-purple-600'
                  }
                `}
                style={player.colorHex ? { backgroundColor: player.colorHex } : {}}
                >
                  {player.username.charAt(0).toUpperCase()}
                </div>

                {/* Player Name */}
                <div className="font-semibold text-sm mb-1 truncate" title={player.username}>
                  {player.username}
                </div>

                {/* Position */}
                <div className="text-xs text-gray-500 mb-2">
                  Slot {slotIndex + 1}
                </div>

                {/* Bet Amount */}
                {showBetAmount && (
                  <div className="text-xs text-gray-600 mb-1">
                    Bet: {player.betAmount} coins
                  </div>
                )}

                {/* Ready Status */}
                {showReadyStatus && (
                  <div className={`text-xs font-medium ${getReadyStatusColor(player.isReady)}`}>
                    {getReadyStatusText(player.isReady)}
                  </div>
                )}

                {/* Color Selection */}
                {showColorSelection && player.colorId && (
                  <div className="mt-2">
                    <div className="text-xs text-gray-500 mb-1">Color:</div>
                    <div className="flex items-center justify-center gap-1">
                      <div 
                        className="w-4 h-4 rounded-full border border-gray-300"
                        style={{ backgroundColor: player.colorHex || '#ccc' }}
                      ></div>
                      <span className="text-xs capitalize">{player.colorId}</span>
                    </div>
                  </div>
                )}

                {/* Join Time */}
                {showJoinTime && (
                  <div className="text-xs text-gray-400 mt-1">
                    Joined: {formatJoinTime(player.joinedAt)}
                  </div>
                )}

                {/* Balance Warning */}
                {player.insufficientBalance && (
                  <div className="text-xs text-red-500 mt-1 font-medium">
                    ⚠️ Low Balance
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center text-gray-400">
                <div className="w-12 h-12 rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center mb-2 mx-auto">
                  <span className="text-2xl">+</span>
                </div>
                <div className="text-sm">
                  Slot {slotIndex + 1}
                </div>
                <div className="text-xs mt-1">
                  Waiting for player
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Room Status */}
      {roomInfo && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm text-gray-600">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="font-medium">Room Status:</span> {roomInfo.room?.status || 'Unknown'}
              </div>
              <div>
                <span className="font-medium">Game Type:</span> {roomInfo.room?.gameType || 'Unknown'}
              </div>
              <div>
                <span className="font-medium">Prize Pool:</span> {roomInfo.roomState?.prizePool || 0} coins
              </div>
              <div>
                <span className="font-medium">Game In Progress:</span> {roomInfo.roomState?.gameInProgress ? 'Yes' : 'No'}
              </div>
            </div>
            
            {roomInfo.roomState?.countdown && (
              <div className="mt-2 text-center">
                <span className="font-medium text-orange-600">
                  Game starting in: {roomInfo.roomState.countdown}s
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Debug Info */}
      <details className="mt-4">
        <summary className="text-xs text-gray-500 cursor-pointer">Debug Info</summary>
        <div className="mt-2 text-xs">
          <div className="bg-gray-100 p-2 rounded">
            <div><strong>Players:</strong> {players.length}</div>
            <div><strong>Last Update:</strong> {roomInfo?.timestamp ? new Date(roomInfo.timestamp).toLocaleTimeString() : 'None'}</div>
            <div><strong>Room ID:</strong> {roomInfo?.room?.id || currentRoom?.id || 'None'}</div>
          </div>
          
          <details className="mt-2">
            <summary className="cursor-pointer">Raw Player Data</summary>
            <pre className="bg-gray-100 p-2 rounded mt-1 text-xs overflow-x-auto">
              {JSON.stringify(players, null, 2)}
            </pre>
          </details>
          
          <details className="mt-2">
            <summary className="cursor-pointer">Raw Room Info</summary>
            <pre className="bg-gray-100 p-2 rounded mt-1 text-xs overflow-x-auto">
              {JSON.stringify(roomInfo, null, 2)}
            </pre>
          </details>
        </div>
      </details>
    </div>
  );
}

export default PlayerSlots;
