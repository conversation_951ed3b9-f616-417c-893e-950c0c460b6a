import React, { useState } from 'react';
import { 
  LayoutDashboard, 
  Trophy, 
  Users, 
  Settings, 
  BarChart3,
  Shield
} from 'lucide-react';
import { PrizePoolDashboard } from './PrizePoolDashboard';
import { RoomManagement } from './RoomManagement';

type DashboardTab = 'overview' | 'prize-pools' | 'rooms' | 'analytics' | 'settings';

interface AdminDashboardProps {
  className?: string;
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({
  className = '',
}) => {
  const [activeTab, setActiveTab] = useState<DashboardTab>('overview');
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null);

  const tabs = [
    {
      id: 'overview' as DashboardTab,
      name: 'Overview',
      icon: LayoutDashboard,
      description: 'System overview and key metrics',
    },
    {
      id: 'prize-pools' as DashboardTab,
      name: 'Prize Pools',
      icon: Trophy,
      description: 'Monitor and manage prize pools',
    },
    {
      id: 'rooms' as DashboardTab,
      name: 'Room Management',
      icon: Users,
      description: 'Manage game rooms and players',
    },
    {
      id: 'analytics' as DashboardTab,
      name: 'Analytics',
      icon: BarChart3,
      description: 'Performance metrics and reports',
    },
    {
      id: 'settings' as DashboardTab,
      name: 'Settings',
      icon: Settings,
      description: 'System configuration',
    },
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4">System Overview</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <Trophy className="w-8 h-8 text-blue-600" />
                    <div>
                      <div className="text-2xl font-bold text-blue-900">24</div>
                      <div className="text-sm text-blue-700">Active Prize Pools</div>
                    </div>
                  </div>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <Users className="w-8 h-8 text-green-600" />
                    <div>
                      <div className="text-2xl font-bold text-green-900">156</div>
                      <div className="text-sm text-green-700">Active Players</div>
                    </div>
                  </div>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <BarChart3 className="w-8 h-8 text-purple-600" />
                    <div>
                      <div className="text-2xl font-bold text-purple-900">$2,450</div>
                      <div className="text-sm text-purple-700">Total Pool Value</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">New prize pool created</div>
                      <div className="text-sm text-gray-600">Room: Wheel Masters - $50.00</div>
                    </div>
                    <div className="text-sm text-gray-500">2 min ago</div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">Prize distributed</div>
                      <div className="text-sm text-gray-600">Winner: Player123 - $75.00</div>
                    </div>
                    <div className="text-sm text-gray-500">5 min ago</div>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">Room created</div>
                      <div className="text-sm text-gray-600">Lucky Spins - 8 players max</div>
                    </div>
                    <div className="text-sm text-gray-500">12 min ago</div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">System Health</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">API Response Time</span>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-900">45ms</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Socket Connections</span>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-900">156 active</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Database Status</span>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-900">Healthy</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-700">Error Rate</span>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm text-gray-900">0.02%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      case 'prize-pools':
        return <PrizePoolDashboard />;

      case 'rooms':
        return selectedRoomId ? (
          <RoomManagement 
            roomId={selectedRoomId} 
            onClose={() => setSelectedRoomId(null)} 
          />
        ) : (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Room Management</h2>
            <p className="text-gray-600 mb-4">
              Select a room to manage or monitor room activities.
            </p>
            <div className="space-y-3">
              <button
                onClick={() => setSelectedRoomId('room-1')}
                className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
              >
                <div className="font-medium text-gray-900">Wheel Masters</div>
                <div className="text-sm text-gray-600">8/8 players • $50.00 prize pool</div>
              </button>
              <button
                onClick={() => setSelectedRoomId('room-2')}
                className="w-full text-left p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
              >
                <div className="font-medium text-gray-900">Lucky Spins</div>
                <div className="text-sm text-gray-600">3/6 players • $30.00 prize pool</div>
              </button>
            </div>
          </div>
        );

      case 'analytics':
        return (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Analytics Dashboard</h2>
            <p className="text-gray-600">
              Analytics and reporting features will be implemented here.
            </p>
          </div>
        );

      case 'settings':
        return (
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">System Settings</h2>
            <p className="text-gray-600">
              System configuration and settings will be implemented here.
            </p>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 ${className}`}>
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <Shield className="w-8 h-8 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Admin Dashboard</h1>
                <p className="text-sm text-gray-600">Prize Wheel Game Management</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                Last updated: {new Date().toLocaleTimeString()}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* Sidebar Navigation */}
          <div className="w-64 flex-shrink-0">
            <nav className="space-y-2">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => {
                      setActiveTab(tab.id);
                      if (tab.id !== 'rooms') {
                        setSelectedRoomId(null);
                      }
                    }}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      activeTab === tab.id
                        ? 'bg-blue-100 text-blue-900 border border-blue-200'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <Icon className="w-5 h-5" />
                      <div>
                        <div className="font-medium">{tab.name}</div>
                        <div className="text-xs text-gray-500">{tab.description}</div>
                      </div>
                    </div>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
