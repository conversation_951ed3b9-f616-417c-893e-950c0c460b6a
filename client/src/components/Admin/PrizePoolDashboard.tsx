import React, { useState, useEffect } from 'react';
import { 
  Trophy, 
  DollarSign, 
  Users, 
  TrendingUp, 
  RefreshCw, 
  Filter,
  Download,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { apiClient } from '../../services/api';
import type { PrizePool, PrizePoolsResponse } from '../../types/api';

interface PrizePoolDashboardProps {
  className?: string;
}

export const PrizePoolDashboard: React.FC<PrizePoolDashboardProps> = ({
  className = '',
}) => {
  const [prizePools, setPrizePools] = useState<PrizePool[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    status: 'all' as 'all' | 'accumulating' | 'locked' | 'distributed' | 'cancelled',
    gameType: 'all' as 'all' | 'prize_wheel',
    page: 1,
    perPage: 20,
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  useEffect(() => {
    loadPrizePools();
  }, [filters]);

  const loadPrizePools = async () => {
    setLoading(true);
    setError(null);

    try {
      const params: any = {
        page: filters.page,
        per_page: filters.perPage,
      };

      if (filters.status !== 'all') {
        params.status = filters.status;
      }

      if (filters.gameType !== 'all') {
        params.game_type = filters.gameType;
      }

      const response: PrizePoolsResponse = await apiClient.getPrizePools(params);
      setPrizePools(response.prize_pools);
      setPagination(response.pagination);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load prize pools';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'accumulating':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'locked':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'distributed':
        return <CheckCircle className="w-4 h-4 text-blue-600" />;
      case 'cancelled':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accumulating':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'locked':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'distributed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const calculateStatistics = () => {
    const stats = {
      totalPools: prizePools.length,
      totalValue: prizePools.reduce((sum, pool) => sum + pool.total_pool, 0),
      activeRooms: prizePools.filter(pool => pool.status === 'accumulating').length,
      completedGames: prizePools.filter(pool => pool.status === 'distributed').length,
      averagePoolSize: 0,
      totalHouseEdge: prizePools.reduce((sum, pool) => sum + pool.house_edge_amount, 0),
    };

    stats.averagePoolSize = stats.totalPools > 0 ? stats.totalValue / stats.totalPools : 0;

    return stats;
  };

  const stats = calculateStatistics();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Prize Pool Dashboard</h1>
          <p className="text-gray-600">Monitor and manage prize pools across all games</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={loadPrizePools}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
            <Download className="w-4 h-4" />
            Export
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Trophy className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{stats.totalPools}</div>
              <div className="text-sm text-gray-600">Total Prize Pools</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">${stats.totalValue.toFixed(2)}</div>
              <div className="text-sm text-gray-600">Total Pool Value</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Users className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">{stats.activeRooms}</div>
              <div className="text-sm text-gray-600">Active Rooms</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <TrendingUp className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900">${stats.averagePoolSize.toFixed(2)}</div>
              <div className="text-sm text-gray-600">Average Pool Size</div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-gray-600" />
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>
          
          <select
            value={filters.status}
            onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as any, page: 1 }))}
            className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
          >
            <option value="all">All Status</option>
            <option value="accumulating">Accumulating</option>
            <option value="locked">Locked</option>
            <option value="distributed">Distributed</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <select
            value={filters.gameType}
            onChange={(e) => setFilters(prev => ({ ...prev, gameType: e.target.value as any, page: 1 }))}
            className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
          >
            <option value="all">All Games</option>
            <option value="prize_wheel">Prize Wheel</option>
          </select>

          <select
            value={filters.perPage}
            onChange={(e) => setFilters(prev => ({ ...prev, perPage: parseInt(e.target.value), page: 1 }))}
            className="px-3 py-1 border border-gray-300 rounded-lg text-sm"
          >
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
          </select>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
      )}

      {/* Prize Pools Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Room
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Pool Value
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Players
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Entry Fee
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  House Edge
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading ? (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center">
                    <RefreshCw className="w-6 h-6 animate-spin text-gray-400 mx-auto mb-2" />
                    <span className="text-gray-600">Loading prize pools...</span>
                  </td>
                </tr>
              ) : prizePools.length === 0 ? (
                <tr>
                  <td colSpan={8} className="px-6 py-12 text-center">
                    <Trophy className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                    <span className="text-gray-600">No prize pools found</span>
                  </td>
                </tr>
              ) : (
                prizePools.map((pool) => (
                  <tr key={pool.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{pool.room_name}</div>
                        <div className="text-sm text-gray-500">{pool.room_id.slice(-8)}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(pool.status)}`}>
                        {getStatusIcon(pool.status)}
                        {pool.status}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">${pool.total_pool.toFixed(2)}</div>
                      <div className="text-sm text-gray-500">Net: ${pool.net_prize_amount.toFixed(2)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{pool.player_count}/{pool.max_players}</div>
                      <div className="text-sm text-gray-500">{pool.contributing_players.length} paid</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      ${pool.entry_fee_per_player.toFixed(2)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{pool.house_edge_percentage}%</div>
                      <div className="text-sm text-gray-500">${pool.house_edge_amount.toFixed(2)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(pool.created_at).toLocaleDateString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 mr-3">
                        <Eye className="w-4 h-4" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setFilters(prev => ({ ...prev, page: prev.page - 1 }))}
                  disabled={!pagination.hasPrev}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <span className="px-3 py-1 text-sm text-gray-700">
                  Page {pagination.page} of {pagination.totalPages}
                </span>
                <button
                  onClick={() => setFilters(prev => ({ ...prev, page: prev.page + 1 }))}
                  disabled={!pagination.hasNext}
                  className="px-3 py-1 border border-gray-300 rounded-lg text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PrizePoolDashboard;
