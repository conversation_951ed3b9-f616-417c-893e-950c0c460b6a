import React, { useState, useEffect } from 'react';
import { 
  <PERSON>, 
  Settings, 
  UserX, 
  Eye, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Clock,
  DollarSign,
  Trophy
} from 'lucide-react';
import { apiClient } from '../../services/api';
import type { EnhancedRoomDetails, KickPlayerRequest } from '../../types/api';
import toast from 'react-hot-toast';

interface RoomManagementProps {
  roomId: string;
  onClose?: () => void;
}

export const RoomManagement: React.FC<RoomManagementProps> = ({
  roomId,
  onClose,
}) => {
  const [roomDetails, setRoomDetails] = useState<EnhancedRoomDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [kickingPlayer, setKickingPlayer] = useState<string | null>(null);

  useEffect(() => {
    loadRoomDetails();
  }, [roomId]);

  const loadRoomDetails = async () => {
    setLoading(true);
    setError(null);

    try {
      const details = await apiClient.getEnhancedRoomDetails(roomId);
      setRoomDetails(details);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load room details';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleKickPlayer = async (userId: string, username: string) => {
    const reason = prompt(`Enter reason for kicking ${username}:`);
    if (!reason) return;

    setKickingPlayer(userId);

    try {
      const request: KickPlayerRequest = {
        user_id: userId,
        reason,
        notify_player: true,
      };

      await apiClient.kickPlayer(roomId, request);
      toast.success(`${username} has been kicked from the room`);
      
      // Reload room details to reflect changes
      await loadRoomDetails();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to kick player';
      toast.error(errorMessage);
    } finally {
      setKickingPlayer(null);
    }
  };

  const getPlayerStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'completed':
        return <Trophy className="w-4 h-4 text-blue-600" />;
      case 'cancelled':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getPlayerStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-center py-8">
          <RefreshCw className="w-6 h-6 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading room details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <span className="text-red-800">{error}</span>
          </div>
        </div>
        <div className="mt-4 flex justify-center">
          <button
            onClick={loadRoomDetails}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!roomDetails) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="text-center py-8">
          <span className="text-gray-600">Room not found</span>
        </div>
      </div>
    );
  }

  const { room } = roomDetails;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-bold text-gray-900">Room Management</h2>
          <p className="text-gray-600">{room.name} ({room.id.slice(-8)})</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={loadRoomDetails}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
            >
              Close
            </button>
          )}
        </div>
      </div>

      {/* Room Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-3">
            <Users className="w-5 h-5 text-blue-600" />
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {room.current_players}/{room.max_players}
              </div>
              <div className="text-sm text-gray-600">Players</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-3">
            <DollarSign className="w-5 h-5 text-green-600" />
            <div>
              <div className="text-lg font-semibold text-gray-900">
                ${room.prize_pool.toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Prize Pool</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-3">
            <Trophy className="w-5 h-5 text-purple-600" />
            <div>
              <div className="text-lg font-semibold text-gray-900">
                ${room.bet_amount.toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Entry Fee</div>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-4">
          <div className="flex items-center gap-3">
            <Settings className="w-5 h-5 text-gray-600" />
            <div>
              <div className="text-lg font-semibold text-gray-900 capitalize">
                {room.status}
              </div>
              <div className="text-sm text-gray-600">Status</div>
            </div>
          </div>
        </div>
      </div>

      {/* Room Configuration */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-700">Game Type</label>
            <div className="text-sm text-gray-900 capitalize">{room.game_type}</div>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Privacy</label>
            <div className="text-sm text-gray-900">{room.is_private ? 'Private' : 'Public'}</div>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Game Duration</label>
            <div className="text-sm text-gray-900">{room.configuration.game_duration}s</div>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Spin Duration</label>
            <div className="text-sm text-gray-900">{room.configuration.spin_duration}s</div>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Result Display</label>
            <div className="text-sm text-gray-900">{room.configuration.result_display_duration}s</div>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-700">Wheel Sections</label>
            <div className="text-sm text-gray-900">{room.configuration.wheel_sections}</div>
          </div>
        </div>
      </div>

      {/* Players Management */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Players ({room.players.length})</h3>
        
        {room.players.length === 0 ? (
          <div className="text-center py-8">
            <Users className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <span className="text-gray-600">No players in this room</span>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Player</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Ready</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Color</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Sessions</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Joined</th>
                  <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {room.players.map((player) => (
                  <tr key={player.user_id} className="hover:bg-gray-50">
                    <td className="px-4 py-3">
                      <div>
                        <div className="font-medium text-gray-900">{player.username}</div>
                        <div className="text-sm text-gray-500">{player.user_id.slice(-8)}</div>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium border ${getPlayerStatusColor(player.status)}`}>
                        {getPlayerStatusIcon(player.status)}
                        {player.status}
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <div className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                        player.is_ready 
                          ? 'bg-green-100 text-green-800 border-green-200' 
                          : 'bg-gray-100 text-gray-800 border-gray-200'
                      }`}>
                        {player.is_ready ? <CheckCircle className="w-3 h-3" /> : <Clock className="w-3 h-3" />}
                        {player.is_ready ? 'Ready' : 'Not Ready'}
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      {player.selected_color ? (
                        <div className="flex items-center gap-2">
                          <div 
                            className="w-4 h-4 rounded-full border border-gray-300"
                            style={{ backgroundColor: player.selected_color }}
                          />
                          <span className="text-sm text-gray-900 capitalize">{player.selected_color}</span>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500">None</span>
                      )}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-900">
                      {player.session_count}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      {new Date(player.joined_at).toLocaleString()}
                    </td>
                    <td className="px-4 py-3">
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleKickPlayer(player.user_id, player.username)}
                          disabled={kickingPlayer === player.user_id}
                          className="text-red-600 hover:text-red-900 disabled:opacity-50"
                          title="Kick Player"
                        >
                          {kickingPlayer === player.user_id ? (
                            <RefreshCw className="w-4 h-4 animate-spin" />
                          ) : (
                            <UserX className="w-4 h-4" />
                          )}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Game Sessions */}
      {room.game_sessions.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Game Sessions</h3>
          <div className="space-y-3">
            {room.game_sessions.slice(0, 5).map((session) => (
              <div key={session.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">Session {session.session_id.slice(-8)}</div>
                  <div className="text-sm text-gray-600">
                    {new Date(session.started_at).toLocaleString()} - {session.ended_at ? new Date(session.ended_at).toLocaleString() : 'In Progress'}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-gray-900">${session.win_amount.toFixed(2)}</div>
                  <div className="text-sm text-gray-600 capitalize">{session.status}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default RoomManagement;
