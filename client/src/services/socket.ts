import { io, Socket } from 'socket.io-client';
import { config, env } from '@/config/env';
import { apiClient } from './api';
import { useAuthStore } from '@/store/authStore';
import { subscriptionManager } from '@/utils/subscriptionManager';
import type {
  SocketEvents,
  ConnectionInfo,
  RoomState,
  JoinRoomData,
  LeaveRoomData,
  SocketResponse,
  ConnectAckData,
  BalanceUpdateData,
  RoomUpdateSpecData,
  RoomInfoUpdatedData,
} from '@/types/socket';

type EventCallback<T = any> = (data: T) => void;
type EventMap = {
  [K in keyof SocketEvents]: SocketEvents[K] extends (data: infer T, ...args: any[]) => void ? T : never;
};

class SocketService {
  private socket: Socket | null = null;
  private connectionInfo: ConnectionInfo = { state: 'disconnected', reconnectAttempts: 0 };
  private eventListeners: Map<string, Set<EventCallback>> = new Map();
  private roomState: RoomState | null = null;
  private heartbeatInterval: number | null = null;
  private playerReadyState: boolean = false;

  constructor() {
    this.initializeEventListeners();
  }

  // Connection management
  async connect(): Promise<void> {
    if (this.socket?.connected) return;

    const token = apiClient.getAuthToken();
    if (!token) throw new Error('No authentication token available');

    this.updateConnectionState('connecting');

    this.socket = io(config.socket.url, {
      ...config.socket.options,
      auth: { token },
    });

    this.setupSocketEventHandlers();

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        if (this.connectionInfo.state === 'connecting') {
          reject(new Error('Connection timeout'));
        }
      }, config.timeouts.socketConnect);

      this.socket!.on('connect', () => {
        clearTimeout(timeout);
        this.updateConnectionState('connected');
        this.startHeartbeat();
        resolve();
      });

      this.socket!.on('connect_error', (error) => {
        clearTimeout(timeout);
        this.updateConnectionState('error', error.message);
        reject(error);
      });
    });
  }

  disconnect(): void {
    this.socket?.disconnect();
    this.socket = null;
    this.stopHeartbeat();
    this.updateConnectionState('disconnected');
    this.roomState = null;
  }

  private updateConnectionState(state: ConnectionInfo['state'], error?: string): void {
    this.connectionInfo = {
      state,
      connectedAt: state === 'connected' ? new Date() : this.connectionInfo.connectedAt,
      reconnectAttempts: state === 'connected' ? 0 : this.connectionInfo.reconnectAttempts + (state === 'error' ? 1 : 0),
      error,
    };
    this.emit('connectionStateChanged', this.connectionInfo);
  }

  private setupSocketEventHandlers(): void {
    if (!this.socket) return;

    // Connection events
    this.socket.on('disconnect', (reason) => {
      this.updateConnectionState('disconnected');
      this.stopHeartbeat();
      this.emit('disconnect', reason);
      subscriptionManager.handleDisconnect();
      this.handleDisconnectRoomLeave(reason);
      env.DEBUG && console.log('Socket disconnected:', reason);
    });

    this.socket.on('reconnect', () => {
      this.updateConnectionState('connected');
      this.startHeartbeat();
    });

    this.socket.on('reconnecting', () => {
      this.updateConnectionState('reconnecting');
    });

    // Connection acknowledgment
    this.socket.on('connect_ack', (data: ConnectAckData) => {
      if (data.balance !== undefined) {
        useAuthStore.getState().updateBalance(data.balance);
      }
      this.emit('connect_ack', data);
      env.DEBUG && console.log('Connection acknowledged:', data);
    });

    // Lobby and room events
    this.socket.on('room_list_updated', (data) => {
      this.emit('room_list_updated', data);
      this.logRoomListUpdate(data);
    });

    // Player events
    const playerEvents = ['player_joined', 'player_left', 'player_left_enhanced',
                         'player_disconnected_ready', 'player_reconnected', 'positions_updated',
                         'player_ready_status', 'player_ready_changed'];

    playerEvents.forEach(event => {
      this.socket!.on(event, (data) => {
        if (event.includes('player_joined') || event.includes('player_left') ||
            event.includes('ready') || event.includes('positions')) {
          this.updateRoomState(event, data);
        }
        this.emit(event, data);
        env.DEBUG && this.logPlayerEvent(event, data);
      });
    });

    // Game events
    const gameEvents = ['game_starting', 'game_started', 'game_update', 'game_finished', 'game_cancelled'];
    gameEvents.forEach(event => {
      this.socket!.on(event, (data) => this.emit(event, data));
    });

    // System events
    this.socket.on('notification', (data) => this.emit('notification', data));
    this.socket.on('error', (data) => {
      this.emit('error', data);
      env.DEBUG && console.error('Socket error:', data);
    });

    // Balance update events
    this.socket.on('balance_updated', (data: BalanceUpdateData) => {
      if (data.balance !== undefined) {
        useAuthStore.getState().updateBalance(data.balance);
      }
      this.emit('balance_updated', data);
      env.DEBUG && console.log('Balance updated:', data);
    });

    // Ping/Pong for latency measurement
    this.socket.on('pong', (data) => {
      this.connectionInfo.lastPing = data.latency;
      this.emit('pong', data);
    });

    // Color selection events
    const colorEvents = [
      'colorStateUpdate', 'room_color_state_updated', 'available_colors',
      'color_selection_update', 'color_state_sync', 'color_selection_updated'
    ];

    colorEvents.forEach(event => {
      this.socket!.on(event, (data: any) => {
        // Handle different color event types and update room state
        if (this.roomState && this.roomState.id === data.roomId) {
          this.updateRoomColorState(event, data);
        }
        this.emit(event, data);
        env.DEBUG && this.logColorEvent(event, data);
      });
    });

    // Room info updated event
    this.socket.on('room_info_updated', (data: RoomInfoUpdatedData) => {
      this.emit('room_info_updated', data);
      env.DEBUG && this.logRoomInfoUpdate(data);
    });

    // Room update events
    this.socket.on('room_update_spec', (data: RoomUpdateSpecData) => {
      this.updateRoomFromSpec(data);
      this.emit('room_update_spec', data);
      env.DEBUG && console.log('Room update spec received:', data);
    });

    // Room state updated event
    this.socket.on('room_state_updated', (data: any) => {
      this.updateRoomFromStateData(data);
      this.emit('room_state_updated', data);
      env.DEBUG && console.log('Room state updated:', data);
    });
  }

  // Lobby management
  async subscribeLobby(): Promise<SocketResponse> {
    return this.emitWithTimeout('subscribe_lobby', undefined, 'Failed to subscribe to lobby');
  }

  async unsubscribeLobby(): Promise<SocketResponse> {
    return this.emitWithTimeout('unsubscribe_lobby', undefined, 'Failed to unsubscribe from lobby');
  }

  // Room subscription management
  async subscribeRoom(roomId: string): Promise<SocketResponse> {
    if (!roomId) throw new Error('Room ID is required for room subscription');
    const response = await this.emitWithTimeout('subscribe_room', { roomId }, 'Failed to subscribe to room');
    env.DEBUG && console.log('Successfully subscribed to room:', roomId);
    return response;
  }

  async unsubscribeRoom(roomId: string): Promise<SocketResponse> {
    if (!roomId) throw new Error('Room ID is required for room unsubscription');
    const response = await this.emitWithTimeout('unsubscribe_room', { roomId }, 'Failed to unsubscribe from room');
    env.DEBUG && console.log('Successfully unsubscribed from room:', roomId);
    return response;
  }

  // Helper methods
  private async emitWithTimeout(event: string, data: any, errorMessage: string): Promise<SocketResponse> {
    if (!this.socket?.connected) throw new Error('Socket not connected');

    return new Promise((resolve, reject) => {
      this.socket!.emit(event, data, (response: SocketResponse) => {
        response.success ? resolve(response) : reject(new Error(response.error || errorMessage));
      });

      setTimeout(() => reject(new Error(`${errorMessage} - timeout`)), config.timeouts.gameAction);
    });
  }

  private logRoomListUpdate(data: any): void {
    const actions: Record<string, () => void> = {
      'initial_load': () => console.log(`📋 Room list loaded: ${data.rooms?.length || 0} rooms`),
      'subscribe': () => console.log(`🔗 ${data.action}: ${data.count || data.rooms?.length || 0} rooms`),
      'unsubscribe': () => console.log(`🔗 ${data.action}: ${data.count || data.rooms?.length || 0} rooms`),
      'player_count_changed': () => {
        const room = data.room;
        if (room && (room.playerCount === 0 || room.playerCount >= room.maxPlayers)) {
          console.log(`👥 ${room.name}: ${room.playerCount}/${room.maxPlayers}`);
        }
      }
    };
    actions[data.action]?.();
  }

  private logPlayerEvent(event: string, data: any): void {
    if (event.includes('enhanced') || event.includes('disconnected') || event.includes('reconnected')) {
      console.log(`${event}:`, data);
    }
  }

  private logColorEvent(event: string, data: any): void {
    const loggers: Record<string, () => void> = {
      'available_colors': () => console.log('Available colors updated:', {
        available: data.data?.availableCount, taken: data.data?.takenCount, roomId: data.data?.roomId
      }),
      'color_selection_update': () => console.log('Color selection update:', {
        player: data.data?.player?.username, action: data.data?.action,
        color: data.data?.player?.selectedColor?.name || data.data?.player?.unselectedColor?.name
      }),
      'color_selection_updated': () => console.log('🎨 Color selection updated:', {
        action: data.action,
        player: data.player?.username,
        color: data.player?.color?.name,
        roomId: data.roomId,
        availableCount: data.colorState?.statistics?.availableCount,
        selectionRate: data.colorState?.statistics?.selectionRate + '%'
      })
    };
    loggers[event]?.();
  }

  private logRoomInfoUpdate(data: RoomInfoUpdatedData): void {
    if (data.game && data.metadata && data.players && data.room && data.roomId) {
      console.log('🆕 New comprehensive room_info_updated format:', {
        roomId: data.roomId, reason: data.reason, gameType: data.game.type, canStart: data.game.canStart,
        allColorsSelected: data.game.allColorsSelected, playerCount: (data.players as any).total,
        playersWithColors: (data.players as any).withColors, roomStatus: data.room.status,
      });
    } else {
      const roomId = (data.roomState as any)?.id || data.room?.id;
      console.log('📋 Legacy room_info_updated format:', { roomId, playerCount: data.roomState?.playerCount });
    }
  }

  private updateRoomFromSpec(data: RoomUpdateSpecData): void {
    if (!this.roomState || this.roomState.id !== data.roomId) return;

    this.roomState.participantCount = data.participantCount;
    this.roomState.prizePool = data.prizePool;
    this.roomState.status = data.gameState as any;

    if (data.players && this.roomState.players) {
      this.roomState.players = this.roomState.players.map(player => {
        const realPlayerData = data.players[player.userId];
        return realPlayerData ? {
          ...player,
          balance: realPlayerData.balance,
          insufficientBalance: realPlayerData.insufficientBalance,
          colorId: realPlayerData.color,
          isReady: realPlayerData.ready,
        } : player;
      });
    }

    this.roomState.colorState = {
      playerColors: Object.fromEntries(Object.entries(data.players).map(([userId, player]) => [userId, player.color])),
      availableColors: data.availableColors,
      takenColors: data.takenColors,
    };
  }

  private updateRoomFromStateData(data: any): void {
    if (!this.roomState || this.roomState.id !== data.roomId) return;

    const { roomState, players, gameConfig, playerColors } = data;

    this.roomState = {
      ...this.roomState,
      ...(roomState || {}),
      players: players || this.roomState.players || [],
      playerCount: roomState?.playerCount || players?.length || 0,
      readyCount: roomState?.readyCount || 0,
      canStartGame: roomState?.canStartGame || false,
      prizePool: roomState?.prizePool || 0,
      betAmount: gameConfig?.betAmount || this.roomState.betAmount,
      maxPlayers: gameConfig?.maxPlayers || this.roomState.maxPlayers,
      gameType: gameConfig?.gameType || this.roomState.gameType,
      colorState: playerColors ? {
        playerColors: playerColors,
        availableColors: this.roomState.colorState?.availableColors || [],
        takenColors: Object.fromEntries(Object.entries(playerColors).map(([, colorId]) => [colorId as string, true])),
      } : this.roomState.colorState,
    };
  }

  private updateRoomColorState(event: string, data: any): void {
    if (!this.roomState) return;

    switch (event) {
      case 'colorStateUpdate':
        this.roomState.colorState = {
          playerColors: data.playerColors,
          availableColors: data.availableColors,
          takenColors: data.takenColors,
        };
        break;

      case 'color_selection_updated':
        // Handle the new comprehensive color selection event
        if (data.colorState) {
          const colorState = data.colorState;
          this.roomState.colorState = {
            playerColors: this.extractPlayerColors(colorState.assignments),
            availableColors: colorState.availableColors || [],
            takenColors: this.extractTakenColors(colorState.taken || []),
          };

          // Store additional color state information in a separate property for extended data
          (this.roomState as any).extendedColorState = {
            assignments: colorState.assignments,
            selectedColors: colorState.selectedColors,
            statistics: colorState.statistics,
            currentPlayerColor: colorState.currentPlayerColor,
          };
        }
        break;

      case 'available_colors':
        if (data.data?.availableColors) {
          this.roomState.colorState = {
            playerColors: this.roomState.colorState?.playerColors || {},
            availableColors: data.data.availableColors,
            takenColors: this.roomState.colorState?.takenColors || {},
          };
        }
        break;

      case 'color_selection_update':
        // Handle individual color selection updates
        if (data.data?.player) {
          const player = data.data.player;
          if (this.roomState.colorState) {
            this.roomState.colorState.playerColors = {
              ...this.roomState.colorState.playerColors,
              [player.userId]: player.selectedColor?.id || player.unselectedColor?.id,
            };
          }
        }
        break;

      case 'color_state_sync':
        // Handle complete color state synchronization
        if (data.data) {
          this.roomState.colorState = {
            playerColors: this.roomState.colorState?.playerColors || {},
            availableColors: this.roomState.colorState?.availableColors || [],
            takenColors: this.roomState.colorState?.takenColors || {},
            ...data.data,
          };
        }
        break;
    }
  }

  private extractPlayerColors(assignments: any): Record<string, string> {
    if (!assignments) return {};
    return Object.fromEntries(
      Object.entries(assignments).map(([userId, assignment]: [string, any]) => [
        userId,
        assignment.color?.id || assignment.color
      ])
    );
  }

  private extractTakenColors(takenColors: any[]): Record<string, boolean> {
    if (!Array.isArray(takenColors)) return {};
    return Object.fromEntries(
      takenColors.map((color: any) => [color.id || color, true])
    );
  }

  // Room management
  async joinRoom(roomId: string, password?: string, betAmount?: number): Promise<SocketResponse> {
    const data: JoinRoomData = {
      roomId,
      password,
      betAmount,
      enhanced: true,
      timestamp: new Date().toISOString()
    };

    try {
      const response = await this.emitWithTimeout('join_room', data, 'Failed to join room');

      const roomData = (response as any).room;
      const playerData = (response as any).player;

      // Initialize room state
      this.roomState = {
        id: roomId,
        name: roomData?.name || '',
        gameType: roomData?.gameType || 'PRIZEWHEEL',
        status: roomData?.status || 'WAITING',
        players: roomData?.players || [],
        playerCount: roomData?.playerCount || 0,
        maxPlayers: roomData?.maxPlayers || 8,
        readyCount: 0,
        canStartGame: false,
        betAmount: roomData?.betAmount || 0,
        prizePool: roomData?.prizePool || 0,
      };

      // Update balance if provided
      if (playerData?.balance !== undefined) {
        useAuthStore.getState().updateBalance(playerData.balance);
      }

      this.emit('room_joined', { room: this.roomState, player: playerData, success: true });
      env.DEBUG && console.log('Successfully joined room:', roomId);

      return response;
    } catch (error: any) {
      const enhancedError = this.createEnhancedJoinError(error.message, error.code || 'UNKNOWN_ERROR', {});
      throw enhancedError;
    }
  }

  async leaveRoom(roomId: string, reason: 'voluntary' | 'disconnected' | 'kicked' = 'voluntary'): Promise<SocketResponse> {
    const data: LeaveRoomData = { roomId, reason };
    env.DEBUG && console.log('Leaving room:', { roomId, reason, currentRoomState: this.roomState });

    try {
      const response = await this.emitWithTimeout('leave_room', data, 'Failed to leave room');
      const errorCode = (response as any).code;
      const previousRoom = this.roomState;

      if (errorCode === 'RECONNECTION_ENABLED') {
        this.emit('reconnection_enabled', {
          roomId,
          reason,
          previousRoom,
          reconnectionWindow: (response as any).roomState?.reconnectionWindow || 300,
          success: true
        });
        env.DEBUG && console.log('Reconnection enabled for ready player');
      } else {
        this.roomState = null;
        this.emit('room_left', {
          roomId,
          reason,
          previousRoom,
          success: true,
          enhanced: (response as any).enhanced || false
        });
        env.DEBUG && console.log('Successfully left room:', roomId);
      }

      return response;
    } catch (error: any) {
      const errorCode = error.code || 'LEAVE_ROOM_FAILED';
      const userFriendlyMessage = this.getLeaveRoomErrorMessage(errorCode, error.message);

      const enhancedError = new Error(userFriendlyMessage);
      (enhancedError as any).code = errorCode;
      (enhancedError as any).originalMessage = error.message;

      throw enhancedError;
    }
  }

  private getLeaveRoomErrorMessage(code: string, defaultMessage: string): string {
    const errorMessages: Record<string, string> = {
      'CANNOT_LEAVE_WHILE_READY': 'Cannot leave room while in ready state. Please unready first.',
      'PLAYER_NOT_IN_ROOM': 'You are not currently in this room.',
      'ROOM_NOT_FOUND': 'Room not found or no longer available.',
      'LEAVE_ROOM_FAILED': 'Failed to leave room. Please try again.',
    };
    return errorMessages[code] || defaultMessage;
  }

  async setPlayerReady(roomId: string, isReady: boolean): Promise<SocketResponse> {
    const eventName = isReady ? 'player_ready_spec' : 'player_unready_spec';
    const data = { payload: {} };

    try {
      const response = await this.emitWithTimeout(eventName, data, `Failed to set ${isReady ? 'ready' : 'unready'} status`);
      this.playerReadyState = isReady;
      env.DEBUG && console.log('Player ready state updated:', { roomId, isReady, eventName });
      return response;
    } catch (error: any) {
      if (error.code === 'VALIDATION_ERROR' && error.details?.errors) {
        const validationErrors = error.details.errors;
        error.message = validationErrors.map((err: any) => err.message).join(', ');
      }
      throw error;
    }
  }

  async setPlayerUnready(roomId: string): Promise<SocketResponse> {
    return this.setPlayerReady(roomId, false);
  }

  async selectWheelColor(roomId: string, colorId: string): Promise<SocketResponse> {
    const data = { payload: { color: colorId } };

    try {
      const response = await new Promise<SocketResponse>((resolve, reject) => {
        this.socket!.emit('select_color_spec', data, (response: SocketResponse) => {
          response.success ? resolve(response) : reject(new Error(response.error || 'Failed to select wheel color'));
        });
        setTimeout(() => reject(new Error('Wheel color selection timeout')), config.timeouts.wheelColorSelection);
      });

      env.DEBUG && console.log('Wheel color selected successfully:', { roomId, colorId });
      return response;
    } catch (error: any) {
      if (error.code === 'VALIDATION_ERROR' && error.details?.errors) {
        error.message = error.details.errors.map((err: any) => err.message).join(', ');
      }
      throw error;
    }
  }

  async selectAmidakujiPosition(roomId: string, position: number): Promise<SocketResponse> {
    const data = { payload: { position } };

    try {
      const response = await this.emitWithTimeout('select_position_spec', data, 'Failed to select position');
      env.DEBUG && console.log('Amidakuji position selected successfully:', { roomId, position });
      return response;
    } catch (error: any) {
      if (error.code === 'VALIDATION_ERROR' && error.details?.errors) {
        error.message = error.details.errors.map((err: any) => err.message).join(', ');
      }
      throw error;
    }
  }

  // Disconnect handling with reconnection support
  private handleDisconnectRoomLeave(reason: string): void {
    if (!this.roomState) return;

    const { id: roomId } = this.roomState;
    const previousRoom = this.roomState;

    env.DEBUG && console.log('Disconnect room leave evaluation:', {
      roomId,
      playerReadyState: this.playerReadyState,
      disconnectReason: reason,
      willPreserveForReconnection: this.playerReadyState
    });

    if (this.playerReadyState) {
      // Ready player - preserve room state for reconnection
      this.emit('player_ready_disconnected', {
        roomId,
        reason: 'disconnected',
        previousRoom,
        canReconnect: true,
        reconnectionWindow: 300,
        preservedState: true
      });
      this.storeReconnectionInfo(roomId, reason);
      console.log('Preserving room state for ready player reconnection');
    } else {
      // Non-ready player - auto-leave
      this.roomState = null;
      this.emit('room_left', {
        roomId,
        reason: 'disconnected',
        previousRoom,
        success: true,
        autoLeave: true
      });
      console.log('Auto-left room due to disconnect (non-ready player)');
    }
  }

  // Reconnection management
  private storeReconnectionInfo(roomId: string, reason: string): void {
    const reconnectionInfo = {
      roomId,
      reason,
      disconnectedAt: new Date(),
      playerWasReady: this.playerReadyState,
      roomState: this.roomState
    };

    try {
      localStorage.setItem('reconnection_info', JSON.stringify(reconnectionInfo));
      setTimeout(() => this.clearReconnectionInfo(), 300000); // 5 minutes
      env.DEBUG && console.log('Stored reconnection info:', reconnectionInfo);
    } catch (error) {
      console.warn('Failed to store reconnection info:', error);
    }
  }

  public clearReconnectionInfo(): void {
    try {
      localStorage.removeItem('reconnection_info');
      env.DEBUG && console.log('Cleared reconnection info');
    } catch (error) {
      console.warn('Failed to clear reconnection info:', error);
    }
  }

  checkForPendingReconnection(): any | null {
    try {
      const reconnectionInfoStr = localStorage.getItem('reconnection_info');
      if (!reconnectionInfoStr) return null;

      const reconnectionInfo = JSON.parse(reconnectionInfoStr);
      const timeDiff = Date.now() - new Date(reconnectionInfo.disconnectedAt).getTime();
      const fiveMinutes = 5 * 60 * 1000;

      if (timeDiff > fiveMinutes) {
        this.clearReconnectionInfo();
        return null;
      }

      env.DEBUG && console.log('Found pending reconnection:', { ...reconnectionInfo, timeRemaining: fiveMinutes - timeDiff });
      return reconnectionInfo;
    } catch (error) {
      console.warn('Failed to check for pending reconnection:', error);
      this.clearReconnectionInfo();
      return null;
    }
  }

  async attemptReconnection(reconnectionInfo: any): Promise<boolean> {
    try {
      env.DEBUG && console.log('Attempting reconnection to room:', reconnectionInfo.roomId);

      const response = await this.joinRoom(reconnectionInfo.roomId);

      if (response.success) {
        this.clearReconnectionInfo();
        this.emit('reconnection_successful', {
          roomId: reconnectionInfo.roomId,
          reconnectedAt: new Date(),
          wasReady: reconnectionInfo.playerWasReady
        });
        console.log('Successfully reconnected to room:', reconnectionInfo.roomId);
        return true;
      } else {
        console.warn('Failed to reconnect to room:', response.error);
        return false;
      }
    } catch (error) {
      console.error('Reconnection attempt failed:', error);
      return false;
    }
  }

  // Utility methods
  ping(): void {
    if (!this.socket?.connected) return;

    const startTime = Date.now();
    this.socket.emit('ping', (response: any) => {
      const latency = Date.now() - startTime;
      this.connectionInfo.lastPing = latency;
      this.emit('ping_response', { ...response, latency });
    });
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => this.ping(), config.timeouts.heartbeat) as unknown as number;
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  private updateRoomState(eventType: string, data: any): void {
    if (!this.roomState) return;

    env.DEBUG && console.log('updateRoomState called:', { eventType, hasRoomState: !!data.roomState });

    const roomState = data.roomState;
    if (!roomState && env.DEBUG) {
      console.warn(`${eventType} event missing roomState data:`, data);
      return;
    }

    switch (eventType) {
      case 'player_joined':
      case 'player_left':
      case 'player_left_enhanced':
        if (roomState) {
          this.roomState.playerCount = roomState.playerCount;
          this.roomState.readyCount = roomState.readyCount || 0;
        }
        break;
      case 'player_ready_status':
      case 'player_ready_changed':
        this.roomState.readyCount = data.readyCount ?? roomState?.readyCount ?? 0;
        this.roomState.canStartGame = data.canStartGame ?? roomState?.canStartGame ?? false;
        break;
      case 'positions_updated':
        // Position updates handled separately if needed
        break;
      default:
        env.DEBUG && console.log('updateRoomState: unhandled event type:', eventType);
        break;
    }
  }

  // Event system
  private initializeEventListeners(): void {
    const events = [
      'connectionStateChanged', 'connect_ack', 'disconnect', 'room_list_updated', 'room_joined', 'room_left',
      'player_joined', 'player_left', 'player_ready_status', 'game_starting', 'game_started', 'game_update',
      'game_finished', 'game_cancelled', 'notification', 'error', 'balance_updated', 'pong', 'ping_response',
      'colorStateUpdate', 'room_color_state_updated', 'room_update_spec', 'room_info_updated', 'room_state_updated',
      'room_subscribed', 'room_unsubscribed', 'available_colors', 'color_selection_update', 'color_state_sync',
    ];
    events.forEach(event => this.eventListeners.set(event, new Set()));
  }

  on<K extends keyof EventMap>(event: K, callback: EventCallback<EventMap[K]>): void;
  on(event: string, callback: EventCallback): void;
  on(event: string, callback: EventCallback): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    this.eventListeners.get(event)!.add(callback);
  }

  off<K extends keyof EventMap>(event: K, callback: EventCallback<EventMap[K]>): void;
  off(event: string, callback: EventCallback): void;
  off(event: string, callback: EventCallback): void {
    this.eventListeners.get(event)?.delete(callback);
  }

  private emit(event: string, data?: any): void {
    this.eventListeners.get(event)?.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in event listener for ${event}:`, error);
      }
    });
  }

  // Create enhanced join error with additional context
  private createEnhancedJoinError(message: string, code: string, details: any): Error {
    const error = new Error(message);
    const errorContext: Record<string, any> = {
      code,
      details,
      enhanced: true,
      timestamp: new Date().toISOString(),
    };

    const actionMap: Record<string, { actionRequired: string; userAction: string }> = {
      'USER_ALREADY_IN_ROOM': { actionRequired: 'Leave current room first', userAction: 'navigate_to_current_room' },
      'ROOM_FULL': { actionRequired: 'Try another room', userAction: 'browse_other_rooms' },
      'INSUFFICIENT_BALANCE': { actionRequired: 'Add funds to account', userAction: 'add_balance' },
      'ROOM_NOT_WAITING': { actionRequired: 'Wait for room to finish current game', userAction: 'wait_and_retry' },
      'INVALID_PASSWORD': { actionRequired: 'Enter correct password', userAction: 'retry_with_password' },
    };

    Object.assign(errorContext, actionMap[code] || { actionRequired: 'Try again', userAction: 'retry' });
    Object.assign(error, errorContext);

    return error;
  }

  // Getters
  get isConnected(): boolean {
    return this.socket?.connected || false;
  }

  get connectionState(): ConnectionInfo {
    return { ...this.connectionInfo };
  }

  get currentRoom(): RoomState | null {
    return this.roomState ? { ...this.roomState } : null;
  }
}

// Export singleton instance
export const socketService = new SocketService();
export default socketService;
