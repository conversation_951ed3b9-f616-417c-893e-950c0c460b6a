# Prize Wheel Testing Guide

## Overview

This guide covers the comprehensive testing strategy for the Prize Wheel backend integration features. The testing suite includes unit tests, integration tests, and end-to-end testing scenarios.

## Test Structure

### Test Categories

1. **Unit Tests** - Individual component and utility testing
2. **Integration Tests** - Feature interaction testing
3. **Hook Tests** - Custom React hooks testing
4. **Error Handling Tests** - Error scenarios and edge cases

### Test Files Organization

```
src/
├── components/
│   └── Game/
│       └── __tests__/
│           ├── PrizePoolDisplay.test.tsx
│           ├── BalanceAndEntryFee.test.tsx
│           └── PrizeWheelIntegration.test.tsx
├── hooks/
│   └── __tests__/
│       ├── usePrizePoolData.test.ts
│       ├── useRealTimeBalance.test.ts
│       └── useEntryFeeProcessing.test.ts
├── utils/
│   └── __tests__/
│       └── prizeWheelErrorHandler.test.ts
└── test/
    └── setup.ts
```

## Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test PrizePoolDisplay.test.tsx

# Run tests matching pattern
npm test -- --grep "prize pool"
```

### Coverage Targets

- **Lines**: 70%
- **Functions**: 70%
- **Branches**: 70%
- **Statements**: 70%

## Test Categories

### 1. Component Tests

#### PrizePoolDisplay Component
- ✅ Renders prize pool information correctly
- ✅ Shows compact version
- ✅ Displays user potential winnings
- ✅ Handles loading and error states
- ✅ Uses real-time data when available

#### BalanceAndEntryFee Component
- ✅ Validates balance correctly
- ✅ Processes entry fees
- ✅ Shows insufficient balance warnings
- ✅ Handles different entry fee states
- ✅ Integrates with error handling

#### Prize Wheel Integration
- ✅ Complete game flow testing
- ✅ Color selection with validation
- ✅ Entry fee processing integration
- ✅ Winner announcement display
- ✅ Error state handling

### 2. Hook Tests

#### usePrizePoolData Hook
- ✅ Loads prize pool data on mount
- ✅ Provides derived data correctly
- ✅ Handles loading and error states
- ✅ Refreshes data when requested
- ✅ Syncs with game store

#### useRealTimeBalance Hook
- ✅ Validates balance against requirements
- ✅ Handles real-time balance updates
- ✅ Processes socket events correctly
- ✅ Provides balance status information

#### useEntryFeeProcessing Hook
- ✅ Processes entry fee payments
- ✅ Handles refund processing
- ✅ Tracks payment status
- ✅ Integrates with balance validation

### 3. Utility Tests

#### PrizeWheelErrorHandler
- ✅ Handles different error formats
- ✅ Maps error codes correctly
- ✅ Provides user-friendly messages
- ✅ Shows appropriate toast notifications
- ✅ Logs errors for debugging

## Test Scenarios

### Happy Path Scenarios

1. **Complete Game Flow**
   ```typescript
   // User joins room → validates balance → pays entry fee → 
   // selects color → marks ready → game plays → receives winnings
   ```

2. **Prize Pool Growth**
   ```typescript
   // Multiple players join → entry fees accumulate → 
   // prize pool grows → real-time updates
   ```

3. **Winner Announcement**
   ```typescript
   // Game completes → winner determined → 
   // announcement shown → prizes distributed
   ```

### Error Scenarios

1. **Insufficient Balance**
   ```typescript
   // User attempts to pay entry fee with insufficient balance →
   // validation fails → clear error message shown
   ```

2. **Network Failures**
   ```typescript
   // API calls fail → retry logic activated → 
   // graceful degradation → user informed
   ```

3. **Prize Pool Errors**
   ```typescript
   // Prize pool not found → error handled → 
   // fallback UI shown → user can retry
   ```

### Edge Cases

1. **Concurrent Operations**
   ```typescript
   // Multiple users joining simultaneously →
   // race conditions handled → data consistency maintained
   ```

2. **Socket Disconnections**
   ```typescript
   // Connection lost during game → reconnection logic →
   // state synchronization → seamless recovery
   ```

3. **Invalid Data**
   ```typescript
   // Malformed API responses → validation catches errors →
   // safe fallbacks → user experience preserved
   ```

## Mock Strategy

### Store Mocks
```typescript
// Game Store Mock
const mockGameStore = {
  prizePoolData: mockPrizePool,
  getPrizePoolData: vi.fn(),
  updatePrizePoolData: vi.fn(),
  // ... other methods
};

// Auth Store Mock
const mockAuthStore = {
  user: mockUser,
  balance: 50.00,
  updateBalance: vi.fn(),
};
```

### API Mocks
```typescript
// Successful API responses
mockApiClient.validateBalance.mockResolvedValue({
  success: true,
  has_sufficient_balance: true,
  current_balance: 50.00,
});

// Error responses
mockApiClient.processEntryFee.mockRejectedValue(
  new Error('Payment processing failed')
);
```

### Socket Mocks
```typescript
// Socket event simulation
const mockSocket = {
  on: vi.fn(),
  emit: vi.fn(),
  off: vi.fn(),
};
```

## Test Data Factories

### User Factory
```typescript
export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  username: 'testuser',
  email: '<EMAIL>',
  balance: 100.00,
  ...overrides,
});
```

### Prize Pool Factory
```typescript
export const createMockPrizePool = (overrides = {}) => ({
  id: 'test-pool-id',
  total_pool: 100.00,
  entry_fee_per_player: 10.00,
  status: 'accumulating',
  ...overrides,
});
```

## Debugging Tests

### Common Issues

1. **Async Operations**
   ```typescript
   // Use waitFor for async operations
   await waitFor(() => {
     expect(screen.getByText('Expected Text')).toBeInTheDocument();
   });
   ```

2. **Mock Cleanup**
   ```typescript
   // Clear mocks between tests
   beforeEach(() => {
     vi.clearAllMocks();
   });
   ```

3. **State Persistence**
   ```typescript
   // Reset component state
   cleanup();
   ```

### Debug Commands

```bash
# Run single test with debug output
npm test -- --reporter=verbose PrizePoolDisplay.test.tsx

# Run tests with console output
npm test -- --silent=false

# Debug specific test
npm test -- --grep "specific test name" --reporter=verbose
```

## Continuous Integration

### GitHub Actions Integration
```yaml
- name: Run Tests
  run: npm test -- --coverage --reporter=junit

- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### Quality Gates
- All tests must pass
- Coverage thresholds must be met
- No console errors in tests
- Performance tests within limits

## Best Practices

### Test Writing
1. **Descriptive Names** - Test names should clearly describe what is being tested
2. **Single Responsibility** - Each test should test one specific behavior
3. **Arrange-Act-Assert** - Structure tests clearly
4. **Mock Minimally** - Only mock what's necessary

### Test Maintenance
1. **Regular Updates** - Keep tests updated with feature changes
2. **Refactor Tests** - Remove duplicate test code
3. **Review Coverage** - Ensure important paths are tested
4. **Performance** - Keep tests fast and reliable

## Future Enhancements

### Planned Test Additions
1. **Visual Regression Tests** - Screenshot comparison testing
2. **Performance Tests** - Load and stress testing
3. **Accessibility Tests** - A11y compliance testing
4. **Cross-browser Tests** - Multi-browser compatibility

### Test Automation
1. **Auto-generated Tests** - Property-based testing
2. **Mutation Testing** - Test quality validation
3. **Contract Testing** - API contract validation
4. **End-to-End Tests** - Full user journey testing

This comprehensive testing strategy ensures the Prize Wheel integration is robust, reliable, and maintainable.
