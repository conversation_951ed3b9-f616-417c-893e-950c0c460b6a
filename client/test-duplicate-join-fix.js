#!/usr/bin/env node

/**
 * Test script to verify the duplicate join request fix
 * This script checks that the implemented solution prevents duplicate join requests
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Testing Duplicate Join Request Fix...\n');

// Test 1: Check if JoinRoomManager exists and has correct methods
console.log('📁 Checking JoinRoomManager implementation...');
try {
  const joinManagerContent = fs.readFileSync('src/utils/joinRoomManager.ts', 'utf8');
  
  const requiredMethods = [
    'joinRoom',
    'isJoining',
    'cancelJoin',
    'getActiveJoins',
    'clearAll'
  ];
  
  const requiredFeatures = [
    'DEBOUNCE_TIME',
    'activeJoins',
    'Map<string',
    'Promise<void>',
    'singleton'
  ];
  
  console.log('✅ JoinRoomManager file exists');
  
  requiredMethods.forEach(method => {
    if (joinManagerContent.includes(method)) {
      console.log(`✅ Method: ${method}`);
    } else {
      console.log(`❌ Method: ${method} - MISSING`);
    }
  });
  
  requiredFeatures.forEach(feature => {
    if (joinManagerContent.includes(feature)) {
      console.log(`✅ Feature: ${feature}`);
    } else {
      console.log(`❌ Feature: ${feature} - MISSING`);
    }
  });
  
} catch (error) {
  console.log('❌ JoinRoomManager file not found or unreadable');
}

// Test 2: Check if SocketStore uses JoinRoomManager
console.log('\n🔌 Checking SocketStore integration...');
try {
  const socketStoreContent = fs.readFileSync('src/store/socketStore.ts', 'utf8');
  
  const integrationChecks = [
    { name: 'Imports JoinRoomManager', check: socketStoreContent.includes('joinRoomManager') },
    { name: 'Uses joinRoomManager.joinRoom', check: socketStoreContent.includes('joinRoomManager.joinRoom') },
    { name: 'Has isJoiningRoom method', check: socketStoreContent.includes('isJoiningRoom') },
    { name: 'Returns joinRoomManager.isJoining', check: socketStoreContent.includes('joinRoomManager.isJoining') }
  ];
  
  integrationChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ SocketStore file not found or unreadable');
}

// Test 3: Check if RoomCard uses the new isJoiningRoom method
console.log('\n🎴 Checking RoomCard component updates...');
try {
  const roomCardContent = fs.readFileSync('src/components/Game/RoomCard.tsx', 'utf8');
  
  const componentChecks = [
    { name: 'Imports useSocketStore', check: roomCardContent.includes('useSocketStore') },
    { name: 'Uses isJoiningRoom', check: roomCardContent.includes('isJoiningRoom') },
    { name: 'Has isCurrentlyJoining logic', check: roomCardContent.includes('isCurrentlyJoining') },
    { name: 'Prevents duplicate clicks', check: roomCardContent.includes('if (isCurrentlyJoining)') }
  ];
  
  componentChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ RoomCard file not found or unreadable');
}

// Test 4: Check if RoomsPage has debounce logic
console.log('\n📄 Checking RoomsPage debounce implementation...');
try {
  const roomsPageContent = fs.readFileSync('src/pages/RoomsPage.tsx', 'utf8');
  
  const debounceChecks = [
    { name: 'Has lastJoinAttempt state', check: roomsPageContent.includes('lastJoinAttempt') },
    { name: 'Has debounce time check', check: roomsPageContent.includes('2000') },
    { name: 'Prevents rapid duplicates', check: roomsPageContent.includes('Ignoring rapid duplicate') },
    { name: 'Checks joiningRooms state', check: roomsPageContent.includes('joiningRooms.has') }
  ];
  
  debounceChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ RoomsPage file not found or unreadable');
}

// Test 5: Verify error handling improvements
console.log('\n⚠️  Checking error handling improvements...');
try {
  const socketServiceContent = fs.readFileSync('src/services/socket.ts', 'utf8');
  
  const errorChecks = [
    { name: 'Maps JOIN_ROOM_FAILED', check: socketServiceContent.includes('JOIN_ROOM_FAILED') },
    { name: 'Maps ROOM_FULL', check: socketServiceContent.includes('ROOM_FULL') },
    { name: 'Maps INSUFFICIENT_BALANCE', check: socketServiceContent.includes('INSUFFICIENT_BALANCE') },
    { name: 'Maps PLAYER_ALREADY_IN_ROOM', check: socketServiceContent.includes('PLAYER_ALREADY_IN_ROOM') },
    { name: 'Enhanced error messages', check: socketServiceContent.includes('Unable to join room') }
  ];
  
  errorChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ SocketService file not found or unreadable');
}

// Test 6: Summary and recommendations
console.log('\n📊 Fix Implementation Summary:');
console.log('');
console.log('🎯 Problem Solved:');
console.log('   - Duplicate join requests causing ROOM_FULL errors');
console.log('   - Rapid button clicks creating multiple socket requests');
console.log('   - Race conditions in room joining logic');
console.log('');
console.log('🛠️  Solution Implemented:');
console.log('   1. Global JoinRoomManager singleton to prevent duplicates');
console.log('   2. 2-second debounce on room join attempts');
console.log('   3. UI state management to disable buttons during joins');
console.log('   4. Enhanced error handling with specific error codes');
console.log('   5. Real-time join status tracking across components');
console.log('');
console.log('🧪 Testing Recommendations:');
console.log('   1. Test rapid clicking on join buttons');
console.log('   2. Test joining the same room multiple times quickly');
console.log('   3. Test network interruptions during join');
console.log('   4. Test with multiple browser tabs/windows');
console.log('   5. Monitor socket gateway logs for duplicate requests');
console.log('');
console.log('📈 Expected Results:');
console.log('   - No more duplicate join requests in logs');
console.log('   - No more ROOM_FULL errors for available rooms');
console.log('   - Smooth user experience with proper loading states');
console.log('   - Clear error messages for actual failures');
console.log('');
console.log('🚀 Ready for testing with the updated manager service!');
