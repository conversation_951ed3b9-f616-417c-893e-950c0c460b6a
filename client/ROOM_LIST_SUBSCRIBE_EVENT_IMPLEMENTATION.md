# Room List Subscribe Event Implementation

## Overview

This document describes the implementation of enhanced room list event handling for the `room_list_updated` socket event, specifically adding support for the new "subscribe" and "unsubscribe" action types.

## Event Structure

The enhanced `room_list_updated` event now supports the following structure:

```typescript
{
  action: 'subscribe' | 'unsubscribe',
  count: number,
  rooms: RoomUpdateData[],
  socketId?: string,
  userId?: string,
  username?: string,
  source?: string,
  timestamp?: string,
  error?: string
}
```

### Real Server Event Example

```json
{
  "action": "subscribe",
  "count": 4,
  "rooms": [
    {
      "id": "68412c9af494b684c1c18ecf",
      "name": "czxcwee2",
      "game_type": "prizewheel",
      "status": "waiting",
      "current_players": 1,
      "max_players": 2,
      "min_players": 2,
      "bet_amount": 10,
      "currency": "USD",
      "prize_pool": 0,
      "is_private": false,
      "has_password": false,
      "has_space": true,
      "is_featured": false,
      "creator_id": "68333053f035ba37b20bf3a4",
      "created_at": "2025-06-05T05:35:22.488Z",
      "updated_at": "2025-06-11T18:22:12.456Z"
    }
  ],
  "socketId": "sP9VW05z2XQ2TSaDAAAB",
  "userId": "683b07882d7dbd11e92bf29d",
  "username": "res2",
  "source": "manager-service",
  "timestamp": "2025-06-12T15:15:16.831Z"
}
```

## Implementation Details

### 1. Type Definitions Enhanced ✅

**File:** `src/types/socket.ts`

- Added support for `subscribe` and `unsubscribe` actions
- Added new fields: `count`, `socketId`, `userId`, `username`, `source`
- Maintains backward compatibility with existing action types

### 2. Socket Service Enhanced ✅

**File:** `src/services/socket.ts`

- Enhanced logging for subscribe/unsubscribe events
- Detailed debugging information for new event types
- Proper event emission and handling

### 3. Lobby Store Enhanced ✅

**File:** `src/store/lobbyStore.ts`

- Enhanced `handleRoomListUpdate` function to process subscribe events
- Added detailed logging with room data normalization
- Handles both camelCase and snake_case field formats
- Enhanced `convertUpdateDataToRoom` function for field mapping

### 4. Game Store Enhanced ✅

**File:** `src/store/gameStore.ts`

- Added support for subscribe/unsubscribe actions in `handleRoomListUpdate`
- Eliminates "Unknown room list update action" warnings
- Proper room list updates for subscription events

### 5. Field Mapping Support ✅

The implementation handles both server formats:

**Server Format (snake_case):**
```json
{
  "game_type": "prizewheel",
  "current_players": 1,
  "max_players": 2,
  "bet_amount": 10,
  "is_private": false,
  "created_at": "2025-06-05T05:35:22.488Z"
}
```

**Client Format (camelCase):**
```typescript
{
  gameType: 'PRIZEWHEEL',
  playerCount: 1,
  maxPlayers: 2,
  betAmount: 10,
  isPrivate: false,
  createdAt: '2025-06-05T05:35:22.488Z'
}
```

## Testing Implementation

### 1. Unit Tests ✅

**File:** `src/__tests__/roomEventHandling.test.ts`

- 9 comprehensive tests covering all scenarios
- Type validation for subscribe/unsubscribe actions
- Event processing logic validation
- Error handling and edge cases
- Integration test scenarios
- Real server format validation

### 2. Interactive Test Component ✅

**Files:** 
- `src/components/Test/RoomListEventTest.tsx`
- `src/pages/TestRoomEvents.tsx`

**Access:** `http://localhost:3005/debug/room-events`

Features:
- Real-time event monitoring
- Subscription controls
- Detailed event logging
- Connection status monitoring
- Event history with expandable details

### 3. Test Script ✅

**File:** `test-room-list-subscribe-event.js`

- Validates exact server event structure
- Tests field mapping functionality
- Provides comprehensive event analysis

## Key Features

### 1. Smart Field Mapping
- Handles both `playerCount`/`current_players`
- Supports both `gameType`/`game_type`
- Graceful fallbacks for missing data

### 2. Enhanced Logging
```javascript
// Lobby subscribe event logged with:
{
  action: 'subscribe',
  roomCount: 4,
  userId: '683b07882d7dbd11e92bf29d',
  username: 'res2',
  source: 'manager-service',
  timestamp: '2025-06-12T15:15:16.831Z',
  rooms: [/* detailed room data */]
}
```

### 3. Backward Compatibility
- All existing action types still supported
- No breaking changes to existing functionality
- Seamless integration with current room management

### 4. Error Handling
- Graceful handling of malformed data
- Error propagation and logging
- Fallback values for missing fields

## Usage Examples

### 1. Listening to Subscribe Events

```typescript
import { useRoomListUpdates } from '@/store/lobbyStore';

const MyComponent = () => {
  useRoomListUpdates((data) => {
    if (data.action === 'subscribe') {
      console.log(`Received ${data.count} rooms from ${data.source}`);
      console.log('Rooms:', data.rooms);
    }
  });
};
```

### 2. Processing Room Data

```typescript
import { convertUpdateDataToRoom } from '@/store/lobbyStore';

const processSubscribeEvent = (data) => {
  if (data.action === 'subscribe') {
    const rooms = data.rooms.map(convertUpdateDataToRoom);
    // rooms now have normalized camelCase fields
  }
};
```

## Verification

### ✅ All Tests Passing
- 9/9 unit tests pass
- Real server event format validated
- Field mapping tested and working

### ✅ No Console Warnings
- "Unknown room list update action" warning eliminated
- Clean event processing without errors

### ✅ Real-time Functionality
- Events properly received and processed
- UI updates correctly with new room data
- Subscription management working as expected

## Conclusion

The room list subscribe event handling is now fully implemented and tested. The system can properly handle the `room_list_updated` event with action "subscribe" that contains room data in snake_case format from the server, convert it to the appropriate client format, and update the UI accordingly.

The implementation is robust, well-tested, and maintains full backward compatibility while adding the new functionality seamlessly.
