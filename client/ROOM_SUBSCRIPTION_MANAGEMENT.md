# Room Subscription Management

This document describes the implementation of automatic room subscription functionality that establishes real-time updates for specific rooms after a player successfully joins.

## Overview

The room subscription management system ensures that:

1. **After successful room join**: Automatically subscribe to room-specific real-time updates
2. **Replace lobby subscription**: Room subscription takes precedence over lobby subscription
3. **Handle failures gracefully**: Proper error logging and recovery mechanisms
4. **Clean up on room leave**: Automatically unsubscribe when leaving the room
5. **Prevent duplicates**: Guard against multiple subscriptions to the same room

## Implementation Details

### 1. Socket Service - Room Subscription Methods

Added new methods to `src/services/socket.ts` for room subscription management:

```typescript
// Room subscription management
async subscribeRoom(roomId: string): Promise<SocketResponse> {
  return new Promise((resolve, reject) => {
    if (!this.socket?.connected) {
      reject(new Error('Socket not connected'));
      return;
    }

    if (!roomId) {
      reject(new Error('Room ID is required for room subscription'));
      return;
    }

    this.socket.emit('subscribe_room', { roomId }, (response: SocketResponse) => {
      if (response.success) {
        resolve(response);
      } else {
        reject(new Error(response.error || 'Failed to subscribe to room'));
      }
    });

    // Timeout fallback
    setTimeout(() => {
      reject(new Error('Subscribe room timeout'));
    }, config.timeouts.gameAction);
  });
}

async unsubscribeRoom(roomId: string): Promise<SocketResponse> {
  // Similar implementation for unsubscription
}
```

### 2. Room Subscription Store

Created `src/store/roomSubscriptionStore.ts` to manage room subscription state:

```typescript
interface RoomSubscriptionState {
  isSubscribed: boolean;
  subscribedRoomId: string | null;
  subscriptionLoading: boolean;
  subscriptionError: string | null;

  subscribeRoom: (roomId: string) => Promise<void>;
  unsubscribeRoom: (roomId?: string) => Promise<void>;
  clearError: () => void;
  reset: () => void;
}
```

#### Key Features:
- **Duplicate Prevention**: Blocks multiple subscriptions to the same room
- **Room Switching**: Automatically unsubscribes from previous room when subscribing to new one
- **Error Handling**: Comprehensive error state management
- **Loading States**: Prevents concurrent operations during subscription/unsubscription

### 3. Automatic Room Subscription on Join

Enhanced the `room_joined` event handler in `src/store/socketStore.ts`:

```typescript
socketService.on('room_joined', (data: any) => {
  const { room, player } = data;

  // Update room state
  useSocketStore.setState({
    currentRoom: room,
    currentGame: null,
    playerPosition: player?.position,
    playerBalance: player?.balance,
  });

  // Server automatically subscribes client during join process
  // Update local subscription state to reflect server-side subscription
  const roomSubscriptionStore = require('@/store/roomSubscriptionStore').useRoomSubscriptionStore.getState();
  roomSubscriptionStore.markAsSubscribed(room.id);
});
```

### 4. Automatic Room Unsubscription on Leave

Enhanced the `room_left` event handler:

```typescript
socketService.on('room_left', (data: any) => {
  const { roomId, reason, previousRoom, autoLeave } = data;

  // Unsubscribe from room updates
  const roomSubscriptionStore = require('@/store/roomSubscriptionStore').useRoomSubscriptionStore.getState();
  if (roomSubscriptionStore.isSubscribed && roomSubscriptionStore.subscribedRoomId === roomId) {
    console.log('SocketStore: Unsubscribing from room after leaving');
    roomSubscriptionStore.unsubscribeRoom(roomId).catch(error => {
      console.warn('SocketStore: Failed to unsubscribe from room after leaving:', error);
    });
  }

  // Clear room state and re-subscribe to lobby
  // ... rest of room leave logic
});
```

## Flow Diagram

```
Player joins room successfully
         ↓
Server automatically subscribes client
         ↓
room_joined event received
         ↓
Update room state in stores
         ↓
Mark client as subscribed locally
         ↓
Real-time room updates active

Player leaves room
         ↓
room_left event received
         ↓
Unsubscribe from room updates
         ↓
Clear room state
         ↓
Re-subscribe to lobby
```

## Socket Events

### Outgoing Events
- `subscribe_room` - Subscribe to room-specific updates
  - Payload: `{ roomId: string }`
  - Response: `SocketResponse`

- `unsubscribe_room` - Unsubscribe from room updates
  - Payload: `{ roomId: string }`
  - Response: `SocketResponse`

### Incoming Events
- `room_subscribed` - Confirmation of successful room subscription
- `room_unsubscribed` - Confirmation of successful room unsubscription

## Error Handling

### Subscription Failures
- **Socket not connected**: Wait for connection or fail gracefully
- **Invalid room ID**: Validate room ID before attempting subscription
- **Server errors**: Log error and show user notification
- **Timeout**: Handle subscription timeouts with appropriate messaging

### Unsubscription Failures
- **Not subscribed**: Skip unsubscription if not currently subscribed
- **Server errors**: Log warning but continue with room leave process
- **Timeout**: Handle gracefully without blocking room leave

## Benefits

1. **Real-time Updates**: Players receive immediate room state changes
2. **Automatic Management**: No manual subscription management required
3. **Error Resilience**: Graceful handling of subscription failures
4. **Resource Efficiency**: Single subscription per room, automatic cleanup
5. **State Consistency**: Proper state management prevents subscription conflicts
6. **User Experience**: Seamless transition from lobby to room context

## Testing

Comprehensive test suite in `src/__tests__/roomSubscriptionManagement.test.ts`:

- ✅ **13 tests passing**
- Tests subscription/unsubscription flows
- Tests duplicate prevention
- Tests error handling
- Tests state management
- Tests concurrent operations

Run tests with:
```bash
npm test -- roomSubscriptionManagement.test.ts
```

## Usage Example

```typescript
import { useRoomSubscriptionStore } from '@/store/roomSubscriptionStore';

const MyRoomComponent = () => {
  const { isSubscribed, subscribedRoomId, subscriptionError } = useRoomSubscriptionStore();

  // Subscription is handled automatically on room join
  // Manual subscription only needed for special cases
  const handleManualSubscribe = async (roomId: string) => {
    try {
      await useRoomSubscriptionStore.getState().subscribeRoom(roomId);
    } catch (error) {
      console.error('Manual subscription failed:', error);
    }
  };

  return (
    <div>
      <p>Subscribed: {isSubscribed ? 'Yes' : 'No'}</p>
      <p>Room: {subscribedRoomId || 'None'}</p>
      {subscriptionError && <p>Error: {subscriptionError}</p>}
    </div>
  );
};
```

## Comprehensive Room Subscription Flow Implementation

### Complete Join Room Flow with Subscription Management

The implementation now includes a comprehensive room subscription flow manager that handles the complete lifecycle:

#### 1. Pre-Join Flow
```typescript
// Before attempting to join a room:
// 1. Track previous lobby subscription state
// 2. Unsubscribe from lobby subscription
// 3. Handle unsubscription failures gracefully
```

#### 2. Successful Room Join Flow
```typescript
// When room_joined event is received:
// 1. Update room state in stores
// 2. Mark client as subscribed locally (server already subscribed during join)
// 3. Real-time room updates are already active from server-side subscription
// 4. No additional subscription calls needed
```

#### 3. Failed Room Join Flow
```typescript
// When room join fails:
// 1. Automatically re-subscribe to lobby if previously subscribed
// 2. Handle re-subscription failures with user notifications
// 3. Maintain consistent state even when operations fail
```

### Room Subscription Flow Manager

Created `src/utils/roomSubscriptionFlow.ts` with comprehensive flow management:

```typescript
class RoomSubscriptionFlowManager {
  // Execute complete join room flow with subscription management
  async executeJoinRoomFlow(options: JoinRoomFlowOptions): Promise<void>

  // Execute complete leave room flow with subscription management
  async executeLeaveRoomFlow(options: LeaveRoomFlowOptions): Promise<void>

  // Handle room subscription after successful join
  async handlePostJoinRoomSubscription(roomId: string): Promise<void>

  // Handle room unsubscription and lobby re-subscription after leave
  async handlePostLeaveSubscriptionCleanup(roomId: string, autoLeave: boolean): Promise<void>
}
```

### Enhanced RoomsPage Integration

Updated `src/pages/RoomsPage.tsx` to use the comprehensive flow:

```typescript
const handleJoinRoom = async (roomId: string, password?: string) => {
  await roomSubscriptionFlowManager.executeJoinRoomFlow({
    roomId,
    password,
    joinFunction: async () => {
      await joinRoom(roomId, password);
    },
    onSuccess: () => {
      navigate(`/rooms/${roomId}`);
      toast.success('Successfully joined room!');
    },
    onFailure: (error) => {
      // Error handling is done in the flow manager
    }
  });
};
```

### Enhanced Socket Store Integration

Updated `src/store/socketStore.ts` event handlers:

```typescript
// room_joined event
socketService.on('room_joined', (data: any) => {
  // Update room state
  useSocketStore.setState({ currentRoom: room, ... });

  // Automatically subscribe to room using comprehensive flow
  roomSubscriptionFlowManager.handlePostJoinRoomSubscription(room.id);
});

// room_left event
socketService.on('room_left', (data: any) => {
  // Clear room state
  useSocketStore.setState({ currentRoom: null, ... });

  // Handle comprehensive subscription cleanup
  roomSubscriptionFlowManager.handlePostLeaveSubscriptionCleanup(roomId, autoLeave);
});
```

## Testing Coverage

Comprehensive test coverage across **3 test suites with 34 tests**:

### 1. Lobby Subscription Management (8 tests)
- `src/__tests__/lobbySubscriptionManagement.test.ts`
- Tests lobby subscription/unsubscription flows
- Tests error handling and state management

### 2. Room Subscription Management (13 tests)
- `src/__tests__/roomSubscriptionManagement.test.ts`
- Tests room subscription/unsubscription flows
- Tests duplicate prevention and error handling

### 3. Room Subscription Flow (13 tests)
- `src/__tests__/roomSubscriptionFlow.test.ts`
- Tests comprehensive join/leave flows
- Tests post-join/post-leave subscription management
- Tests concurrent operation prevention

Run all subscription tests:
```bash
npm test -- src/__tests__/lobbySubscriptionManagement.test.ts src/__tests__/roomSubscriptionManagement.test.ts src/__tests__/roomSubscriptionFlow.test.ts
```

## Integration with Existing Systems

- **Lobby Subscription**: Room subscription replaces lobby subscription context
- **Game Store**: Room updates integrate with existing game state management
- **Socket Store**: Seamless integration with existing room/game event handlers
- **Error Handling**: Consistent with existing error handling patterns
- **Flow Management**: Centralized subscription lifecycle management
- **State Consistency**: Proper state tracking and duplicate prevention guards
