#!/usr/bin/env node

/**
 * Simple test script to verify client functionality
 * This script tests the basic client setup and configuration
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 Testing XZ Game Client Setup...\n');

// Test 1: Check if essential files exist
console.log('📁 Checking essential files...');
const essentialFiles = [
  'package.json',
  'src/main.tsx',
  'src/App.tsx',
  'src/config/env.ts',
  'src/services/api.ts',
  'src/services/socket.ts',
  'src/store/authStore.ts',
  'src/store/socketStore.ts',
  'src/store/gameStore.ts',
  'src/store/lobbyStore.ts',
  'src/pages/LoginPage.tsx',
  'src/pages/RoomsPage.tsx',
  'src/pages/RoomPage.tsx',
  'src/components/UI/ConnectionStatus.tsx',
  'src/components/UI/JoinRoomButton.tsx',
  'src/vite-env.d.ts',
  'CLIENT_UPDATES.md'
];

let missingFiles = [];
essentialFiles.forEach(file => {
  if (fs.existsSync(path.join(__dirname, file))) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - MISSING`);
    missingFiles.push(file);
  }
});

if (missingFiles.length > 0) {
  console.log(`\n⚠️  Missing ${missingFiles.length} essential files!`);
  process.exit(1);
}

// Test 2: Check package.json scripts
console.log('\n📦 Checking package.json scripts...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredScripts = ['dev', 'dev:debug', 'build', 'type-check'];

requiredScripts.forEach(script => {
  if (packageJson.scripts[script]) {
    console.log(`✅ ${script}: ${packageJson.scripts[script]}`);
  } else {
    console.log(`❌ ${script} - MISSING`);
  }
});

// Test 3: Check TypeScript configuration
console.log('\n🔧 Checking TypeScript configuration...');
try {
  const envFile = fs.readFileSync('src/vite-env.d.ts', 'utf8');
  if (envFile.includes('ImportMetaEnv') && envFile.includes('VITE_API_BASE_URL')) {
    console.log('✅ Vite environment types configured');
  } else {
    console.log('❌ Vite environment types incomplete');
  }
} catch (error) {
  console.log('❌ Failed to read vite-env.d.ts');
}

// Test 4: Check key features in updated files
console.log('\n🚀 Checking key feature implementations...');

// Check socket service enhancements
try {
  const socketService = fs.readFileSync('src/services/socket.ts', 'utf8');
  const features = [
    { name: 'Auto lobby subscription', check: socketService.includes('subscribeLobby().catch') },
    { name: 'Enhanced error handling', check: socketService.includes('JOIN_ROOM_FAILED') },
    { name: 'Retry logic support', check: socketService.includes('errorCode') },
    { name: 'Balance updates', check: socketService.includes('updateBalance') }
  ];
  
  features.forEach(feature => {
    console.log(`${feature.check ? '✅' : '❌'} ${feature.name}`);
  });
} catch (error) {
  console.log('❌ Failed to analyze socket service');
}

// Check rooms page enhancements
try {
  const roomsPage = fs.readFileSync('src/pages/RoomsPage.tsx', 'utf8');
  const features = [
    { name: 'Connection status component', check: roomsPage.includes('ConnectionStatus') },
    { name: 'Enhanced error messages', check: roomsPage.includes('INSUFFICIENT_BALANCE') },
    { name: 'Toast notifications with icons', check: roomsPage.includes('icon:') },
    { name: 'Retry logic integration', check: roomsPage.includes('multiple attempts') }
  ];
  
  features.forEach(feature => {
    console.log(`${feature.check ? '✅' : '❌'} ${feature.name}`);
  });
} catch (error) {
  console.log('❌ Failed to analyze rooms page');
}

// Test 5: Check environment configuration
console.log('\n🌍 Checking environment configuration...');
try {
  const envConfig = fs.readFileSync('src/config/env.ts', 'utf8');
  const configs = [
    { name: 'API base URL', check: envConfig.includes('VITE_API_BASE_URL') },
    { name: 'Socket URL', check: envConfig.includes('VITE_SOCKET_URL') },
    { name: 'Debug mode', check: envConfig.includes('VITE_DEBUG') },
    { name: 'Socket options', check: envConfig.includes('transports') }
  ];
  
  configs.forEach(config => {
    console.log(`${config.check ? '✅' : '❌'} ${config.name}`);
  });
} catch (error) {
  console.log('❌ Failed to analyze environment configuration');
}

console.log('\n🎉 Client setup verification complete!');
console.log('\n📋 Next steps:');
console.log('1. Run `npm install` to install dependencies');
console.log('2. Run `npm run type-check` to verify TypeScript compilation');
console.log('3. Run `npm run dev` to start development server');
console.log('4. Run `npm run dev:debug` for enhanced debugging');
console.log('\n📚 See CLIENT_UPDATES.md for detailed information about the updates.');
