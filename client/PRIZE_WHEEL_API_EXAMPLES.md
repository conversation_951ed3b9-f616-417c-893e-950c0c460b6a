# Prize Wheel API Integration Examples

## Overview

This document provides practical examples of integrating with the Prize Wheel backend API. All examples include error handling, TypeScript types, and best practices.

## Table of Contents

1. [Authentication & Setup](#authentication--setup)
2. [Prize Pool Management](#prize-pool-management)
3. [Balance & Entry Fee Processing](#balance--entry-fee-processing)
4. [Room Management](#room-management)
5. [Real-time Events](#real-time-events)
6. [Error Handling Patterns](#error-handling-patterns)
7. [Testing Examples](#testing-examples)

## Authentication & Setup

### API Client Configuration

```typescript
import { ApiClient } from '@/services/api';

// Initialize API client with authentication
const apiClient = new ApiClient({
  baseURL: process.env.REACT_APP_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add authentication interceptor
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

### Environment Configuration

```typescript
// .env.local
REACT_APP_API_BASE_URL=http://localhost:3000/api
REACT_APP_SOCKET_URL=http://localhost:3000
REACT_APP_DEBUG=true
```

## Prize Pool Management

### Get Prize Pool Information

```typescript
import { PrizePool } from '@/types/api';

export const getPrizePoolExample = async (roomId: string): Promise<PrizePool> => {
  try {
    const prizePool = await apiClient.getRoomPrizePool(roomId);
    
    console.log('Prize Pool Details:', {
      totalPool: prizePool.total_pool,
      netPrize: prizePool.net_prize_amount,
      houseEdge: prizePool.house_edge_percentage,
      playerCount: prizePool.player_count,
      status: prizePool.status,
    });

    return prizePool;
  } catch (error) {
    console.error('Failed to fetch prize pool:', error);
    throw error;
  }
};
```

### Get Potential Winnings

```typescript
import { PotentialWinningsResponse } from '@/types/api';

export const getPotentialWinningsExample = async (
  roomId: string
): Promise<PotentialWinningsResponse> => {
  try {
    const winnings = await apiClient.getPotentialWinnings(roomId);
    
    // Find current user's potential winnings
    const userWinnings = winnings.potential_winnings.find(
      pw => pw.user_id === getCurrentUserId()
    );

    if (userWinnings) {
      console.log('Your Potential Winnings:', {
        amount: userWinnings.potential_winnings,
        probability: (userWinnings.win_probability * 100).toFixed(1) + '%',
        poolShare: (userWinnings.current_pool_share * 100).toFixed(1) + '%',
      });
    }

    return winnings;
  } catch (error) {
    console.error('Failed to fetch potential winnings:', error);
    throw error;
  }
};
```

### Distribute Prizes (Admin)

```typescript
import { PrizeDistributionRequest, PrizeDistributionResult } from '@/types/api';

export const distributePrizesExample = async (
  roomId: string,
  gameResults: GameResult
): Promise<PrizeDistributionResult> => {
  try {
    const distributionRequest: PrizeDistributionRequest = {
      room_id: roomId,
      game_results: {
        winner_user_id: gameResults.winnerId,
        winning_color: gameResults.winningColor,
        participants: gameResults.participants.map(p => ({
          user_id: p.userId,
          selected_color: p.selectedColor,
          is_winner: p.userId === gameResults.winnerId,
        })),
        game_metadata: {
          spin_result: gameResults.spinResult,
          timestamp: new Date().toISOString(),
        },
      },
      distribution_metadata: {
        game_type: 'prize_wheel',
        room_name: gameResults.roomName,
      },
    };

    const result = await apiClient.distributePrizes(distributionRequest);
    
    console.log('Prize Distribution Complete:', {
      totalDistributed: result.total_distributed,
      winnerPrize: result.winner_prize,
      transactionCount: result.transactions.length,
    });

    return result;
  } catch (error) {
    console.error('Failed to distribute prizes:', error);
    throw error;
  }
};
```

## Balance & Entry Fee Processing

### Validate Balance

```typescript
import { BalanceValidationRequest, BalanceValidationResponse } from '@/types/api';

export const validateBalanceExample = async (
  userId: string,
  betAmount: number
): Promise<BalanceValidationResponse> => {
  try {
    const request: BalanceValidationRequest = {
      user_id: userId,
      bet_amount: betAmount,
    };

    const validation = await apiClient.validateBalance(request);
    
    if (!validation.has_sufficient_balance) {
      console.warn('Insufficient Balance:', {
        current: validation.current_balance,
        required: validation.required_amount,
        shortfall: validation.shortfall,
      });
    }

    return validation;
  } catch (error) {
    console.error('Balance validation failed:', error);
    throw error;
  }
};
```

### Process Entry Fee

```typescript
import { EntryFeeRequest, EntryFeeProcessResult } from '@/types/api';

export const processEntryFeeExample = async (
  userId: string,
  roomId: string,
  betAmount: number
): Promise<EntryFeeProcessResult> => {
  try {
    // First validate balance
    const validation = await validateBalanceExample(userId, betAmount);
    if (!validation.has_sufficient_balance) {
      throw new Error('Insufficient balance for entry fee');
    }

    const request: EntryFeeRequest = {
      user_id: userId,
      room_id: roomId,
      bet_amount: betAmount,
      metadata: {
        game_type: 'prize_wheel',
        timestamp: new Date().toISOString(),
      },
    };

    const result = await apiClient.processEntryFee(request);
    
    if (result.success) {
      console.log('Entry Fee Processed:', {
        transactionId: result.transaction.id,
        newBalance: result.current_balance,
        prizePoolTotal: result.prize_pool.total_pool,
      });
    }

    return result;
  } catch (error) {
    console.error('Entry fee processing failed:', error);
    throw error;
  }
};
```

### Process Refund

```typescript
import { RefundRequest, EntryFeeRefundResult } from '@/types/api';

export const processRefundExample = async (
  userId: string,
  roomId: string,
  betAmount: number,
  reason: string = 'player_unready'
): Promise<EntryFeeRefundResult> => {
  try {
    const request: RefundRequest = {
      user_id: userId,
      room_id: roomId,
      bet_amount: betAmount,
      metadata: {
        reason,
        timestamp: new Date().toISOString(),
      },
    };

    const result = await apiClient.processRefund(request);
    
    if (result.success) {
      console.log('Refund Processed:', {
        transactionId: result.transaction.id,
        refundedAmount: result.refunded_amount,
        newBalance: result.current_balance,
      });
    }

    return result;
  } catch (error) {
    console.error('Refund processing failed:', error);
    throw error;
  }
};
```

## Room Management

### Get Enhanced Room Details

```typescript
import { EnhancedRoomDetailsResponse } from '@/types/api';

export const getEnhancedRoomDetailsExample = async (
  roomId: string
): Promise<EnhancedRoomDetailsResponse> => {
  try {
    const roomDetails = await apiClient.getEnhancedRoomDetails(roomId);
    
    console.log('Enhanced Room Details:', {
      basicInfo: {
        name: roomDetails.room.name,
        status: roomDetails.room.status,
        playerCount: roomDetails.room.player_count,
      },
      prizePool: {
        total: roomDetails.prize_pool?.total_pool,
        status: roomDetails.prize_pool?.status,
      },
      players: roomDetails.players.map(p => ({
        username: p.username,
        balance: p.balance,
        entryFeePaid: p.entry_fee_paid,
      })),
    });

    return roomDetails;
  } catch (error) {
    console.error('Failed to fetch enhanced room details:', error);
    throw error;
  }
};
```

### Kick Player (Admin)

```typescript
import { KickPlayerRequest, KickPlayerResponse } from '@/types/api';

export const kickPlayerExample = async (
  roomId: string,
  playerId: string,
  reason: string
): Promise<KickPlayerResponse> => {
  try {
    const request: KickPlayerRequest = {
      user_id: playerId,
      reason,
      notify_player: true,
      refund_entry_fee: true,
    };

    const result = await apiClient.kickPlayer(roomId, request);
    
    console.log('Player Kicked:', {
      playerId,
      reason,
      refundProcessed: result.refund_processed,
      refundAmount: result.refund_amount,
    });

    return result;
  } catch (error) {
    console.error('Failed to kick player:', error);
    throw error;
  }
};
```

## Real-time Events

### Socket Event Handlers

```typescript
import { io, Socket } from 'socket.io-client';
import { 
  UserBalanceUpdatedData,
  UserTransactionCompletedData,
  WheelSpinningData,
  WheelResultData 
} from '@/types/socket';

export class PrizeWheelSocketManager {
  private socket: Socket;

  constructor(socketUrl: string) {
    this.socket = io(socketUrl, {
      transports: ['websocket'],
      autoConnect: false,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers(): void {
    // Balance updates
    this.socket.on('user_balance_updated', (data: UserBalanceUpdatedData) => {
      console.log('Balance Updated:', {
        userId: data.userId,
        previousBalance: data.previousBalance,
        newBalance: data.newBalance,
        changeAmount: data.changeAmount,
        changeType: data.changeType,
      });

      // Update local state
      this.updateUserBalance(data.userId, data.newBalance);
    });

    // Transaction completion
    this.socket.on('user_transaction_completed', (data: UserTransactionCompletedData) => {
      console.log('Transaction Completed:', {
        userId: data.userId,
        transactionId: data.transaction.id,
        amount: data.transaction.amount,
        type: data.transaction.type,
      });

      // Add to transaction history
      this.addTransaction(data.transaction);
    });

    // Wheel spinning
    this.socket.on('wheel_spinning', (data: WheelSpinningData) => {
      console.log('Wheel Spinning:', {
        roomId: data.roomId,
        spinId: data.spinId,
        duration: data.estimatedDuration,
      });

      // Start spin animation
      this.startWheelAnimation(data);
    });

    // Wheel result
    this.socket.on('wheel_result', (data: WheelResultData) => {
      console.log('Wheel Result:', {
        roomId: data.roomId,
        winningColor: data.result.winning_color,
        winner: data.result.winner,
        prizeAmount: data.result.prize_amount,
      });

      // Show winner announcement
      this.showWinnerAnnouncement(data.result);
    });
  }

  connect(): void {
    this.socket.connect();
  }

  disconnect(): void {
    this.socket.disconnect();
  }

  private updateUserBalance(userId: string, newBalance: number): void {
    // Implementation depends on your state management
  }

  private addTransaction(transaction: any): void {
    // Implementation depends on your state management
  }

  private startWheelAnimation(data: WheelSpinningData): void {
    // Implementation depends on your animation system
  }

  private showWinnerAnnouncement(result: any): void {
    // Implementation depends on your UI system
  }
}
```

## Error Handling Patterns

### Retry Logic with Exponential Backoff

```typescript
export const apiCallWithRetry = async <T>(
  apiCall: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await apiCall();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxRetries) {
        break;
      }

      // Exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      console.log(`Attempt ${attempt + 1} failed, retrying in ${delay}ms...`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
};

// Usage example
const prizePool = await apiCallWithRetry(
  () => apiClient.getRoomPrizePool(roomId),
  3,
  1000
);
```

### Comprehensive Error Handler

```typescript
import { PrizeWheelErrorCode } from '@/utils/prizeWheelErrorHandler';

export const handleApiError = (error: any, context: string): void => {
  console.error(`API Error in ${context}:`, error);

  if (error.response?.data?.error_code) {
    const errorCode = error.response.data.error_code as PrizeWheelErrorCode;
    
    switch (errorCode) {
      case PrizeWheelErrorCode.INSUFFICIENT_BALANCE:
        showErrorToast('Insufficient balance. Please add funds to continue.');
        break;
      
      case PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND:
        showErrorToast('Prize pool not found. The room may have been closed.');
        break;
      
      case PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED:
        showErrorToast('Entry fee processing failed. Please try again.');
        break;
      
      default:
        showErrorToast('An unexpected error occurred. Please try again.');
    }
  } else {
    showErrorToast('Network error. Please check your connection.');
  }
};
```

This comprehensive API integration guide provides practical examples for implementing all Prize Wheel backend features with proper error handling and TypeScript support.
