#!/usr/bin/env node

/**
 * Test script to verify comprehensive room capacity display and join prevention functionality
 * This script checks all aspects of the room capacity implementation
 */

const fs = require('fs');
const path = require('path');

console.log('🏠 Testing Room Capacity Display and Join Prevention Functionality...\n');

// Test 1: Check RoomCapacity component
console.log('📊 Checking RoomCapacity component...');
try {
  const roomCapacityContent = fs.readFileSync('src/components/UI/RoomCapacity.tsx', 'utf8');
  
  const capacityChecks = [
    { name: 'Visual capacity indicators', check: roomCapacityContent.includes('isFull') && roomCapacityContent.includes('isNearFull') },
    { name: 'Progress bar visualization', check: roomCapacityContent.includes('progress') && roomCapacityContent.includes('bg-green-500') },
    { name: 'Color-coded status', check: roomCapacityContent.includes('text-red-600') && roomCapacityContent.includes('text-yellow-600') },
    { name: 'Status badges', check: roomCapacityContent.includes('FULL') && roomCapacityContent.includes('ALMOST FULL') },
    { name: 'Configurable display options', check: roomCapacityContent.includes('showIcon') && roomCapacityContent.includes('showBadge') },
    { name: 'Percentage calculation', check: roomCapacityContent.includes('percentage') && roomCapacityContent.includes('Math.round') },
    { name: 'Icon variations', check: roomCapacityContent.includes('XCircle') && roomCapacityContent.includes('AlertTriangle') }
  ];
  
  capacityChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ RoomCapacity component not found or unreadable');
}

// Test 2: Check enhanced JoinRoomButton
console.log('\n🎯 Checking enhanced JoinRoomButton component...');
try {
  const joinButtonContent = fs.readFileSync('src/components/UI/JoinRoomButton.tsx', 'utf8');
  
  const buttonChecks = [
    { name: 'Capacity parameters', check: joinButtonContent.includes('currentPlayers') && joinButtonContent.includes('maxPlayers') },
    { name: 'Full room detection', check: joinButtonContent.includes('isFull = currentPlayers >= maxPlayers') },
    { name: 'Disabled state handling', check: joinButtonContent.includes('getDisabledReason') },
    { name: 'Room full messaging', check: joinButtonContent.includes('Room Full') },
    { name: 'Tooltip functionality', check: joinButtonContent.includes('getTooltipMessage') },
    { name: 'Alternative suggestions', check: joinButtonContent.includes('Try refreshing') && joinButtonContent.includes('Create your own room') },
    { name: 'Enhanced button styling', check: joinButtonContent.includes('bg-red-100') && joinButtonContent.includes('text-red-600') },
    { name: 'Tooltip modal', check: joinButtonContent.includes('showTooltipModal') }
  ];
  
  buttonChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ JoinRoomButton component not found or unreadable');
}

// Test 3: Check RoomCard integration
console.log('\n🃏 Checking RoomCard capacity integration...');
try {
  const roomCardContent = fs.readFileSync('src/components/Game/RoomCard.tsx', 'utf8');
  
  const cardChecks = [
    { name: 'Imports RoomCapacity component', check: roomCardContent.includes('import RoomCapacity') },
    { name: 'Uses RoomCapacity component', check: roomCardContent.includes('<RoomCapacity') },
    { name: 'Imports enhanced JoinRoomButton', check: roomCardContent.includes('import JoinRoomButton') },
    { name: 'Uses enhanced JoinRoomButton', check: roomCardContent.includes('currentPlayers={currentPlayers}') },
    { name: 'Passes capacity props', check: roomCardContent.includes('maxPlayers={maxPlayers}') },
    { name: 'Removed old modal code', check: !roomCardContent.includes('showJoinConfirm') },
    { name: 'Clean component structure', check: !roomCardContent.includes('handleJoin') }
  ];
  
  cardChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ RoomCard component not found or unreadable');
}

// Test 4: Check RoomPage capacity display
console.log('\n📄 Checking RoomPage capacity display...');
try {
  const roomPageContent = fs.readFileSync('src/pages/RoomPage.tsx', 'utf8');
  
  const pageChecks = [
    { name: 'Imports RoomCapacity component', check: roomPageContent.includes('import RoomCapacity') },
    { name: 'Uses RoomCapacity in room details', check: roomPageContent.includes('<RoomCapacity') },
    { name: 'Shows capacity with badges', check: roomPageContent.includes('showBadge={true}') },
    { name: 'Shows capacity percentage', check: roomPageContent.includes('showPercentage={true}') },
    { name: 'Medium size display', check: roomPageContent.includes('size="md"') }
  ];
  
  pageChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ RoomPage file not found or unreadable');
}

// Test 5: Check lobby store capacity tracking
console.log('\n🏪 Checking lobby store capacity tracking...');
try {
  const lobbyStoreContent = fs.readFileSync('src/store/lobbyStore.ts', 'utf8');
  
  const storeChecks = [
    { name: 'Enhanced room list update handler', check: lobbyStoreContent.includes('handleRoomListUpdate') },
    { name: 'Capacity change detection', check: lobbyStoreContent.includes('player_count_changed') },
    { name: 'Full room detection', check: lobbyStoreContent.includes('isFull = room.playerCount >= room.maxPlayers') },
    { name: 'Near full detection', check: lobbyStoreContent.includes('wasNearFull') },
    { name: 'Capacity logging', check: lobbyStoreContent.includes('Room capacity update:') },
    { name: 'Percentage calculation', check: lobbyStoreContent.includes('Math.round((room.playerCount / room.maxPlayers) * 100)') }
  ];
  
  storeChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ Lobby store file not found or unreadable');
}

// Test 6: Summary and expected behavior
console.log('\n📊 Room Capacity Functionality Summary:');
console.log('');
console.log('🎯 Key Features Implemented:');
console.log('   1. Visual capacity indicators with color coding');
console.log('   2. Progress bars showing room fill percentage');
console.log('   3. Status badges (FULL, ALMOST FULL, AVAILABLE, EMPTY)');
console.log('   4. Join prevention for full rooms with clear messaging');
console.log('   5. Tooltip modals with alternative suggestions');
console.log('   6. Real-time capacity updates from socket events');
console.log('');
console.log('🎨 Visual Indicators:');
console.log('   - Green: Available rooms (0-74% full)');
console.log('   - Yellow: Nearly full rooms (75-99% full)');
console.log('   - Red: Full rooms (100% full)');
console.log('   - Gray: Empty rooms (0% full)');
console.log('');
console.log('🚫 Join Prevention Logic:');
console.log('   - Disable join button when room is full');
console.log('   - Show "Room Full" message instead of "Join Room"');
console.log('   - Display tooltip with suggestions when clicked');
console.log('   - Prevent API calls for full rooms');
console.log('');
console.log('🔄 Real-time Updates:');
console.log('   - Immediate capacity updates from socket events');
console.log('   - Visual feedback for capacity changes');
console.log('   - Enhanced logging for debugging');
console.log('   - Automatic refresh of room status');
console.log('');
console.log('💡 User Experience Enhancements:');
console.log('   - Clear visual hierarchy for room availability');
console.log('   - Alternative suggestions for full rooms');
console.log('   - Consistent capacity display across all views');
console.log('   - Loading states during join attempts');
console.log('');
console.log('🧪 Testing Recommendations:');
console.log('   1. Test room capacity display with different fill levels');
console.log('   2. Test join prevention for full rooms');
console.log('   3. Test tooltip functionality and suggestions');
console.log('   4. Test real-time updates when players join/leave');
console.log('   5. Test visual indicators and color coding');
console.log('   6. Test capacity display consistency across pages');
console.log('');
console.log('🚀 Ready for comprehensive room capacity testing!');
