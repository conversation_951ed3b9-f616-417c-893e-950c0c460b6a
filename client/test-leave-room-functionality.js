#!/usr/bin/env node

/**
 * Test script for enhanced leave room functionality
 *
 * This script validates the implementation of:
 * - Enhanced leave room logic with ready state validation
 * - Reconnection support for ready players
 * - Cross-service cleanup and coordination
 * - Position reassignment after player leaves
 * - Color selection cleanup and broadcasting
 * - Real-time event handling and notifications
 * - UI components for enhanced leave room operations
 */

const fs = require('fs');
const path = require('path');

console.log('🚪 Testing Enhanced Leave Room Functionality...\n');

// Test 1: Check Socket Service enhanced leave room implementation
console.log('🔌 Checking Socket Service enhanced leave room implementation...');
try {
  const socketServiceContent = fs.readFileSync('src/services/socket.ts', 'utf8');
  
  const socketChecks = [
    { name: 'Enhanced leaveRoom with reason parameter', check: socketServiceContent.includes('reason: \'voluntary\' | \'disconnected\' | \'kicked\'') },
    { name: 'Emits room_left event', check: socketServiceContent.includes("this.emit('room_left'") },
    { name: 'Tracks player ready state', check: socketServiceContent.includes('this.playerReadyState') },
    { name: '<PERSON><PERSON> disconnect room leave', check: socketServiceContent.includes('handleDisconnectRoomLeave') },
    { name: 'Auto-leave logic for non-ready players', check: socketServiceContent.includes('shouldAutoLeave = !this.playerReadyState') },
    { name: 'Preserves room state for ready players', check: socketServiceContent.includes('Preserving room state during disconnect') },
    { name: 'Enhanced logging for debugging', check: socketServiceContent.includes('Auto-left room due to disconnect') }
  ];
  
  socketChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });

  // Additional enhanced leave room checks
  const enhancedChecks = [
    { name: 'Handles enhanced leave room events', check: socketServiceContent.includes("'player_left_enhanced'") },
    { name: 'Handles reconnection events', check: socketServiceContent.includes("'player_disconnected_ready'") },
    { name: 'Handles position updates', check: socketServiceContent.includes("'positions_updated'") },
    { name: 'Has reconnection support', check: socketServiceContent.includes('checkForPendingReconnection') },
    { name: 'Stores reconnection info', check: socketServiceContent.includes('storeReconnectionInfo') },
    { name: 'Handles CANNOT_LEAVE_WHILE_READY error', check: socketServiceContent.includes('CANNOT_LEAVE_WHILE_READY') },
    { name: 'Handles RECONNECTION_ENABLED response', check: socketServiceContent.includes('RECONNECTION_ENABLED') },
    { name: 'Enhanced disconnect handling', check: socketServiceContent.includes('Enhanced disconnect room leave evaluation') }
  ];

  console.log('\n🔄 Enhanced Leave Room Features:');
  enhancedChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });

} catch (error) {
  console.log('❌ Socket service file not found or unreadable');
}

// Test 2: Check Socket Store leave room integration
console.log('\n📦 Checking Socket Store leave room integration...');
try {
  const socketStoreContent = fs.readFileSync('src/store/socketStore.ts', 'utf8');
  
  const storeChecks = [
    { name: 'Uses join room manager for leave operations', check: socketStoreContent.includes('joinRoomManager.leaveRoom') },
    { name: 'Handles room_left event', check: socketStoreContent.includes("socketService.on('room_left'") },
    { name: 'Clears room state on leave', check: socketStoreContent.includes('currentRoom: null') },
    { name: 'Re-subscribes to lobby after leave', check: socketStoreContent.includes('subscribeLobby()') },
    { name: 'Skips lobby re-subscription for auto-leave', check: socketStoreContent.includes('if (!autoLeave)') },
    { name: 'Has isLeavingRoom method', check: socketStoreContent.includes('isLeavingRoom:') },
    { name: 'Enhanced leave room signature', check: socketStoreContent.includes('reason?: \'voluntary\' | \'disconnected\' | \'kicked\'') }
  ];
  
  storeChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ Socket store file not found or unreadable');
}

// Test 3: Check Join Room Manager leave functionality
console.log('\n🔧 Checking Join Room Manager leave functionality...');
try {
  const joinManagerContent = fs.readFileSync('src/utils/joinRoomManager.ts', 'utf8');
  
  const managerChecks = [
    { name: 'Has activeLeaves map', check: joinManagerContent.includes('activeLeaves = new Map') },
    { name: 'Has leaveRoom method', check: joinManagerContent.includes('async leaveRoom(') },
    { name: 'Has isLeaving method', check: joinManagerContent.includes('isLeaving(roomId: string)') },
    { name: 'Prevents duplicate leave requests', check: joinManagerContent.includes('Duplicate leave attempt') },
    { name: 'Executes leave function', check: joinManagerContent.includes('executeLeave') },
    { name: 'Cleans up after leave', check: joinManagerContent.includes('activeLeaves.delete') }
  ];
  
  managerChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ Join room manager file not found or unreadable');
}

// Test 4: Check LeaveRoomButton component
console.log('\n🎨 Checking LeaveRoomButton component...');
try {
  const leaveButtonContent = fs.readFileSync('src/components/UI/LeaveRoomButton.tsx', 'utf8');
  
  const buttonChecks = [
    { name: 'Has confirmation modal', check: leaveButtonContent.includes('showConfirmModal') },
    { name: 'Shows loading state', check: leaveButtonContent.includes('isLeaving') },
    { name: 'Prevents duplicate clicks', check: leaveButtonContent.includes('if (isLeaving)') },
    { name: 'Has warning messages', check: leaveButtonContent.includes('You will lose your spot') },
    { name: 'Configurable size and variant', check: leaveButtonContent.includes('size?:') && leaveButtonContent.includes('variant?:') },
    { name: 'Uses LogOut icon', check: leaveButtonContent.includes('LogOut') },
    { name: 'Has proper styling', check: leaveButtonContent.includes('bg-red-600') }
  ];
  
  buttonChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ LeaveRoomButton component not found or unreadable');
}

// Test 5: Check RoomPage integration
console.log('\n📄 Checking RoomPage leave room integration...');
try {
  const roomPageContent = fs.readFileSync('src/pages/RoomPage.tsx', 'utf8');
  
  const pageChecks = [
    { name: 'Imports LeaveRoomButton', check: roomPageContent.includes('LeaveRoomButton') },
    { name: 'Uses isLeavingRoom', check: roomPageContent.includes('isLeavingRoom') },
    { name: 'Enhanced leave handler', check: roomPageContent.includes('socketLeaveRoom(roomIdToLeave, \'voluntary\')') },
    { name: 'Navigates after leave', check: roomPageContent.includes('navigate(\'/rooms\')') },
    { name: 'Shows success toast', check: roomPageContent.includes('Successfully left room!') },
    { name: 'Enhanced error handling', check: roomPageContent.includes('Leave room error:') },
    { name: 'Uses new button component', check: roomPageContent.includes('<LeaveRoomButton') }
  ];
  
  pageChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ RoomPage file not found or unreadable');
}

// Test 6: Check type definitions
console.log('\n🏗️  Checking type definitions...');
try {
  const socketTypesContent = fs.readFileSync('src/types/socket.ts', 'utf8');
  
  const typeChecks = [
    { name: 'LeaveRoomData has reason field', check: socketTypesContent.includes('reason?: \'voluntary\' | \'disconnected\' | \'kicked\'') },
    { name: 'Maintains existing fields', check: socketTypesContent.includes('roomId: string') }
  ];
  
  typeChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
  
} catch (error) {
  console.log('❌ Socket types file not found or unreadable');
}

// Test 7: Check Enhanced UI Components
console.log('\n🔄 Checking Enhanced UI Components...');
try {
  const reconnectionModalContent = fs.readFileSync('src/components/UI/ReconnectionModal.tsx', 'utf8');

  const reconnectionChecks = [
    { name: 'ReconnectionModal component exists', check: reconnectionModalContent.includes('ReconnectionModal') },
    { name: 'Has countdown timer', check: reconnectionModalContent.includes('timeRemaining') },
    { name: 'Shows reconnection window', check: reconnectionModalContent.includes('reconnectionWindow') },
    { name: 'Has reconnect button', check: reconnectionModalContent.includes('Reconnect') },
    { name: 'Shows connection status', check: reconnectionModalContent.includes('reconnectionStatus') },
    { name: 'Has progress bar', check: reconnectionModalContent.includes('Progress Bar') },
    { name: 'Handles reconnection attempts', check: reconnectionModalContent.includes('handleReconnect') },
    { name: 'Shows time formatting', check: reconnectionModalContent.includes('formatTime') }
  ];

  reconnectionChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
} catch (error) {
  console.log('❌ ReconnectionModal component not found');
}

// Test 8: Check useReconnection hook
console.log('\n🪝 Checking useReconnection hook...');
try {
  const useReconnectionContent = fs.readFileSync('src/hooks/useReconnection.ts', 'utf8');

  const hookChecks = [
    { name: 'useReconnection hook exists', check: useReconnectionContent.includes('useReconnection') },
    { name: 'Checks for pending reconnection', check: useReconnectionContent.includes('checkForPendingReconnection') },
    { name: 'Attempts reconnection', check: useReconnectionContent.includes('attemptReconnection') },
    { name: 'Handles reconnection events', check: useReconnectionContent.includes('handleReconnectionEnabled') },
    { name: 'Has countdown timer', check: useReconnectionContent.includes('timeRemaining') },
    { name: 'Manages modal state', check: useReconnectionContent.includes('showReconnectionModal') },
    { name: 'Has room-specific hook', check: useReconnectionContent.includes('useRoomReconnection') },
    { name: 'Dismisses reconnection', check: useReconnectionContent.includes('dismissReconnection') }
  ];

  hookChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
} catch (error) {
  console.log('❌ useReconnection hook not found');
}

// Test 9: Enhanced Leave Room Button Features
console.log('\n🛡️  Checking Enhanced Leave Room Button...');
try {
  const leaveButtonContent = fs.readFileSync('src/components/UI/LeaveRoomButton.tsx', 'utf8');

  const enhancedButtonChecks = [
    { name: 'Supports ready state validation', check: leaveButtonContent.includes('isPlayerReady') },
    { name: 'Shows ready warning modal', check: leaveButtonContent.includes('showReadyWarning') },
    { name: 'Handles cannot leave while ready', check: leaveButtonContent.includes('Cannot Leave (Ready)') },
    { name: 'Has Shield icon for ready state', check: leaveButtonContent.includes('Shield') },
    { name: 'Shows enhanced warning message', check: leaveButtonContent.includes('Enhanced Leave Protection') },
    { name: 'Explains reconnection feature', check: leaveButtonContent.includes('5 minutes to reconnect') },
    { name: 'Has canLeaveWhileReady prop', check: leaveButtonContent.includes('canLeaveWhileReady') },
    { name: 'Proper button disabled state', check: leaveButtonContent.includes('isButtonDisabled') }
  ];

  enhancedButtonChecks.forEach(check => {
    console.log(`${check.check ? '✅' : '❌'} ${check.name}`);
  });
} catch (error) {
  console.log('❌ Enhanced LeaveRoomButton features not found');
}

// Test 10: Summary and expected behavior
console.log('\n📊 Leave Room Functionality Summary:');
console.log('');
console.log('🎯 Enhanced Key Features Implemented:');
console.log('   1. Ready state validation - prevents leaving while ready');
console.log('   2. Reconnection support for ready players (5-minute window)');
console.log('   3. Cross-service cleanup and coordination');
console.log('   4. Position reassignment for remaining players');
console.log('   5. Color selection cleanup and broadcasting');
console.log('   6. Real-time event handling and notifications');
console.log('   7. Enhanced UI with ready state warnings');
console.log('   8. Reconnection modal with countdown timer');
console.log('   9. Comprehensive error handling with specific codes');
console.log('   10. Duplicate request prevention and state management');
console.log('');
console.log('🔄 Enhanced Leave Room Flow:');
console.log('   1. User clicks Leave Room button');
console.log('   2. System validates player ready state');
console.log('   3. If ready: Show warning modal, block leave');
console.log('   4. If not ready: Show confirmation modal');
console.log('   5. User confirms leave action');
console.log('   6. Socket service sends enhanced leave_room event');
console.log('   7. Server validates and processes leave with cleanup');
console.log('   8. Position reassignment for remaining players');
console.log('   9. Color selection cleanup and broadcasting');
console.log('   10. Client receives enhanced room_left event');
console.log('   11. Client clears room state and navigates to rooms list');
console.log('   12. Client re-subscribes to lobby for room updates');
console.log('   13. Success toast notification shown');
console.log('');
console.log('🔌 Enhanced Disconnect & Reconnection Handling:');
console.log('   - Non-ready players: Auto-leave room on disconnect');
console.log('   - Ready players: Preserve room state, enable reconnection');
console.log('   - 5-minute reconnection window with countdown timer');
console.log('   - Reconnection modal with status updates');
console.log('   - Automatic reconnection attempt on page reload');
console.log('   - Cross-service coordination for reconnection events');
console.log('   - Enhanced logging for debugging scenarios');
console.log('');
console.log('🧪 Enhanced Testing Recommendations:');
console.log('   1. Test ready state validation (should block leave)');
console.log('   2. Test voluntary leave with confirmation modal');
console.log('   3. Test ready player disconnection (should enable reconnection)');
console.log('   4. Test reconnection modal and countdown timer');
console.log('   5. Test reconnection attempts and success/failure scenarios');
console.log('   6. Test position reassignment after player leaves');
console.log('   7. Test color selection cleanup and broadcasting');
console.log('   8. Test cross-service event coordination');
console.log('   9. Test enhanced error handling with specific codes');
console.log('   10. Test rapid clicking prevention and state management');
console.log('   11. Test lobby re-subscription after leave');
console.log('   12. Test navigation and UI state updates');
console.log('');
console.log('🚀 Ready for comprehensive enhanced leave room testing!');
