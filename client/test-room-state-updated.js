/**
 * Test script to verify room_state_updated event handling
 * This script simulates the room_state_updated event and checks if the client handles it correctly
 */

const io = require('socket.io-client');

// Test configuration
const SERVER_URL = 'http://localhost:3001';
const TEST_ROOM_ID = '68412c9af494b684c1c18ecf';

// Sample room state data from the actual socket message you provided
const SAMPLE_ROOM_STATE = {
  "roomId": "68412c9af494b684c1c18ecf",
  "roomState": {
    "playerCount": 2,
    "readyCount": 0,
    "canStartGame": false,
    "prizePool": 0
  },
  "players": [
    {
      "betAmount": 10,
      "isReady": false,
      "joinedAt": "2025-06-05T07:07:28Z",
      "position": 1,
      "userId": "683b07882d7dbd11e92bf29d",
      "username": "res2"
    },
    {
      "betAmount": 10,
      "isReady": false,
      "joinedAt": "2025-06-05T07:07:50Z",
      "position": 2,
      "userId": "68334427b8ef34dc195f27bd",
      "username": "res"
    }
  ],
  "gameConfig": {
    "betAmount": 10,
    "gameType": "prizewheel",
    "maxPlayers": 2,
    "minPlayers": 2,
    "settings": {}
  },
  "playerColors": {},
  "timestamp": "2025-06-05T07:07:50.185Z"
};

// Test the data structure
console.log('🧪 Testing event data structure:');
console.log('   roomId:', SAMPLE_ROOM_STATE.roomId);
console.log('   roomState:', SAMPLE_ROOM_STATE.roomState);
console.log('   players:', SAMPLE_ROOM_STATE.players);
console.log('   players length:', SAMPLE_ROOM_STATE.players?.length);
console.log('   gameConfig:', SAMPLE_ROOM_STATE.gameConfig);
console.log('   playerColors:', SAMPLE_ROOM_STATE.playerColors);
console.log('   timestamp:', SAMPLE_ROOM_STATE.timestamp);

async function testRoomStateUpdated() {
  console.log('🧪 Testing room_state_updated event handling...\n');

  try {
    // Create a socket connection
    const socket = io(SERVER_URL, {
      auth: {
        token: 'test-token' // You might need a real token for testing
      }
    });

    // Set up event listeners
    socket.on('connect', () => {
      console.log('✅ Connected to server');
      
      // Listen for room_state_updated events
      socket.on('room_state_updated', (data) => {
        console.log('📨 Received room_state_updated event:');
        console.log('   Room ID:', data.roomId);
        console.log('   Timestamp:', data.timestamp);
        console.log('   Player Count (roomState):', data.roomState?.playerCount);
        console.log('   Players Length (root):', data.players?.length);
        console.log('   Ready Count:', data.roomState?.readyCount);
        console.log('   Can Start Game:', data.roomState?.canStartGame);
        console.log('   Prize Pool:', data.roomState?.prizePool);
        console.log('   Game Config:', data.gameConfig);
        console.log('   Player Colors:', data.playerColors);
        console.log('   Players:', data.players?.map(p => ({
          username: p.username,
          position: p.position,
          isReady: p.isReady,
          betAmount: p.betAmount,
          userId: p.userId
        })));
        console.log('\n✅ room_state_updated event received and processed successfully!');
      });

      // Simulate joining a room first (if needed)
      console.log('🔄 Attempting to join room for testing...');
      socket.emit('join_room', {
        roomId: TEST_ROOM_ID,
        enhanced: true,
        timestamp: new Date().toISOString()
      }, (response) => {
        if (response.success) {
          console.log('✅ Successfully joined room for testing');
          
          // Simulate the room_state_updated event after a short delay
          setTimeout(() => {
            console.log('🔄 Simulating room_state_updated event...');
            socket.emit('simulate_room_state_updated', SAMPLE_ROOM_STATE);
          }, 1000);
        } else {
          console.log('⚠️  Failed to join room, but continuing with test...');
          console.log('   Error:', response.error);
          
          // Still try to simulate the event
          setTimeout(() => {
            console.log('🔄 Simulating room_state_updated event...');
            socket.emit('simulate_room_state_updated', SAMPLE_ROOM_STATE);
          }, 1000);
        }
      });
    });

    socket.on('connect_error', (error) => {
      console.log('❌ Connection error:', error.message);
    });

    socket.on('disconnect', (reason) => {
      console.log('🔌 Disconnected:', reason);
    });

    // Clean up after 10 seconds
    setTimeout(() => {
      console.log('\n🧹 Cleaning up test...');
      socket.disconnect();
      process.exit(0);
    }, 10000);

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Alternative test function that directly tests the client-side handling
function testClientSideHandling() {
  console.log('🧪 Testing client-side room_state_updated handling...\n');
  
  // This would require importing the actual client modules
  // For now, we'll just log what should happen
  console.log('📋 Expected behavior when room_state_updated is received:');
  console.log('   1. Socket service should receive the event');
  console.log('   2. Internal room state should be updated');
  console.log('   3. Event should be emitted to other components');
  console.log('   4. Socket store should update current room state');
  console.log('   5. Player slots should re-render with updated data');
  console.log('   6. Player count and ready count should be accurate');
  console.log('\n✅ Client-side handling test completed (manual verification needed)');
}

// Run the appropriate test
if (process.argv.includes('--client-only')) {
  testClientSideHandling();
} else {
  testRoomStateUpdated();
}
