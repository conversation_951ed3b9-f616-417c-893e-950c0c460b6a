# Prize Wheel Backend Integration Documentation

## Overview

This documentation covers the comprehensive client-side integration with the new Prize Wheel backend system. The integration includes prize pool management, real-time balance tracking, entry fee processing, and enhanced user experience features.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Key Features](#key-features)
3. [API Integration](#api-integration)
4. [Real-time Features](#real-time-features)
5. [Component Library](#component-library)
6. [State Management](#state-management)
7. [Error Handling](#error-handling)
8. [Testing Strategy](#testing-strategy)
9. [Usage Examples](#usage-examples)
10. [Migration Guide](#migration-guide)

## Architecture Overview

### System Architecture

```mermaid
graph TB
    A[Prize Wheel UI] --> B[Integration Hook]
    B --> C[Prize Pool Hook]
    B --> D[Balance Hook]
    B --> E[Entry Fee Hook]
    
    C --> F[Prize Pool API]
    D --> G[Balance API]
    E --> H[Entry Fee API]
    
    I[Socket Events] --> J[Real-time Updates]
    J --> K[State Synchronization]
    K --> A
    
    L[Error Handler] --> M[User Notifications]
    L --> N[Logging System]
```

### Data Flow

1. **User Interaction** → Component triggers action
2. **Validation** → Integration hook validates requirements
3. **API Call** → Service layer makes backend request
4. **State Update** → Store updates with new data
5. **UI Refresh** → Components re-render with updated state
6. **Real-time Sync** → Socket events keep data current

## Key Features

### 🏆 Prize Pool Management
- **Real-time Prize Pool Tracking** - Live updates of pool value and participants
- **House Edge Transparency** - Clear display of house edge and net prizes
- **Potential Winnings Calculator** - Dynamic calculation of user's potential returns
- **Prize Pool Growth Visualization** - Charts showing pool growth over time

### 💰 Balance & Entry Fee Processing
- **Automatic Balance Validation** - Real-time balance checking before actions
- **Entry Fee Processing** - Seamless payment processing with retry logic
- **Refund Handling** - Automatic refunds when players leave games
- **Transaction History** - Complete transaction tracking and display

### 🎮 Enhanced Game Experience
- **Participation Validation** - Comprehensive checks before allowing game participation
- **Winner Announcements** - Animated winner celebrations with prize details
- **Real-time Updates** - Live game state and balance updates
- **Error Recovery** - Graceful error handling with user-friendly messages

### 👨‍💼 Admin Dashboard
- **Prize Pool Monitoring** - Administrative oversight of all prize pools
- **Room Management** - Enhanced room administration with player moderation
- **System Analytics** - Performance metrics and health monitoring
- **Player Management** - Tools for managing players and resolving issues

## API Integration

### Prize Pool Endpoints

```typescript
// Get room prize pool
const prizePool = await apiClient.getRoomPrizePool(roomId);

// Get potential winnings
const winnings = await apiClient.getPotentialWinnings(roomId);

// Distribute prizes
const result = await apiClient.distributePrizes(roomId, gameResults);
```

### Entry Fee Processing

```typescript
// Validate balance
const validation = await apiClient.validateBalance({
  user_id: userId,
  bet_amount: amount,
});

// Process entry fee
const result = await apiClient.processEntryFee({
  user_id: userId,
  room_id: roomId,
  bet_amount: amount,
  metadata: { game_type: 'prize_wheel' },
});

// Process refund
const refund = await apiClient.processRefund({
  user_id: userId,
  room_id: roomId,
  bet_amount: amount,
  metadata: { reason: 'player_unready' },
});
```

### Enhanced Room Management

```typescript
// Get enhanced room details
const roomDetails = await apiClient.getEnhancedRoomDetails(roomId);

// Kick player (admin)
const result = await apiClient.kickPlayer(roomId, {
  user_id: playerId,
  reason: 'Inappropriate behavior',
  notify_player: true,
});
```

## Real-time Features

### Socket Events

#### Balance Updates
```typescript
socket.on('user_balance_updated', (data: UserBalanceUpdatedData) => {
  // Handle real-time balance changes
  updateBalance(data.newBalance);
  showBalanceNotification(data);
});
```

#### Transaction Completion
```typescript
socket.on('user_transaction_completed', (data: UserTransactionCompletedData) => {
  // Handle completed transactions
  addTransaction(data.transaction);
  updateBalance(data.newBalance);
});
```

#### Enhanced Wheel Events
```typescript
socket.on('wheel_spinning', (data: WheelSpinningData) => {
  // Handle enhanced wheel spinning
  updateGameState('spinning');
  showSpinAnimation(data.animationData);
});

socket.on('wheel_result', (data: WheelResultData) => {
  // Handle wheel results with prize distribution
  showWinnerAnnouncement(data.winner);
  updatePrizeDistribution(data.prizeDistribution);
});
```

#### Game State Changes
```typescript
socket.on('game_state_changed', (data: GameStateChangedData) => {
  // Handle game state transitions
  updateGamePhase(data.newState);
  handleStateTransition(data);
});
```

## Component Library

### Core Components

#### PrizePoolDisplay
```typescript
<PrizePoolDisplay 
  roomId="room-123"
  compact={false}
  showPotentialWinnings={true}
/>
```

#### BalanceAndEntryFee
```typescript
<BalanceAndEntryFee
  roomId="room-123"
  betAmount={10.00}
  onBalanceValidated={handleValidation}
  showEntryFeeStatus={true}
/>
```

#### TransactionHistory
```typescript
<TransactionHistory
  maxItems={10}
  showRoomTransactionsOnly={false}
  roomId="room-123"
/>
```

### Visualization Components

#### PrizePoolGrowthChart
```typescript
<PrizePoolGrowthChart
  data={growthData}
  currentPool={100.00}
  maxPlayers={8}
/>
```

#### WinnerAnnouncement
```typescript
<WinnerAnnouncement
  winner={winnerData}
  totalPool={100.00}
  participantCount={8}
  winningColor="red"
  onClose={handleClose}
/>
```

#### HouseEdgeInfo
```typescript
<HouseEdgeInfo
  houseEdgePercentage={5}
  totalPool={100.00}
  houseEdgeAmount={5.00}
  netPrizeAmount={95.00}
  playerCount={8}
  entryFee={10.00}
/>
```

#### PotentialWinningsCalculator
```typescript
<PotentialWinningsCalculator
  currentPool={100.00}
  netPrizeAmount={95.00}
  playerCount={8}
  maxPlayers={8}
  entryFee={10.00}
  userPotentialWinnings={userWinnings}
/>
```

### Admin Components

#### AdminDashboard
```typescript
<AdminDashboard />
```

#### PrizePoolDashboard
```typescript
<PrizePoolDashboard />
```

#### RoomManagement
```typescript
<RoomManagement 
  roomId="room-123"
  onClose={handleClose}
/>
```

## State Management

### Game Store Extensions

```typescript
interface GameStore {
  // Prize Pool Management
  prizePoolData: {
    currentPrizePool?: PrizePool;
    prizePoolLoading: boolean;
    prizePoolError: string | null;
    entryFeeStatus: Record<string, EntryFeeStatus>;
    potentialWinnings?: PotentialWinnings;
    transactionHistory: Transaction[];
  };

  // Methods
  loadRoomPrizePool: (roomId: string) => Promise<void>;
  updatePrizePoolData: (data: PrizePool) => void;
  updateEntryFeeStatus: (playerId: string, status: EntryFeeStatus) => void;
  updatePotentialWinnings: (data: PotentialWinnings) => void;
  addTransaction: (transaction: Transaction) => void;
  clearPrizePoolData: () => void;
}
```

### Socket Store Extensions

```typescript
interface SocketStore {
  // Enhanced event handlers
  handleUserBalanceUpdated: (data: UserBalanceUpdatedData) => void;
  handleUserTransactionCompleted: (data: UserTransactionCompletedData) => void;
  handleWheelSpinning: (data: WheelSpinningData) => void;
  handleWheelResult: (data: WheelResultData) => void;
  handleGameStateChanged: (data: GameStateChangedData) => void;
}
```

## Error Handling

### Error Types

```typescript
enum PrizeWheelErrorCode {
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  ENTRY_FEE_PROCESSING_FAILED = 'ENTRY_FEE_PROCESSING_FAILED',
  PRIZE_POOL_NOT_FOUND = 'PRIZE_POOL_NOT_FOUND',
  ROOM_NOT_FOUND = 'ROOM_NOT_FOUND',
  // ... more error codes
}
```

### Error Handler Usage

```typescript
const { handleBalanceError, handleEntryFeeError } = usePrizeWheelErrorHandler();

try {
  await processEntryFee();
} catch (error) {
  handleEntryFeeError(error, userId, roomId, amount);
}
```

### Error Recovery

```typescript
const { handleApiErrorWithRetry } = usePrizeWheelErrorHandler();

const result = await handleApiErrorWithRetry(
  () => apiClient.validateBalance(request),
  3, // max retries
  1000, // retry delay
  { context: 'balance_validation' }
);
```

## Custom Hooks

### usePrizeWheelIntegration

The main integration hook that combines all Prize Wheel features:

```typescript
const prizeWheelIntegration = usePrizeWheelIntegration(roomId, betAmount);

// Access all features
const {
  prizePool,
  balance,
  entryFee,
  canParticipate,
  participationBlockers,
  gamePhase,
  actions: {
    prepareForGame,
    exitGame,
    refreshAllData,
    validateParticipation,
  },
} = prizeWheelIntegration;
```

### Individual Hooks

```typescript
// Prize pool data management
const prizePoolData = usePrizePoolData(roomId);

// Real-time balance tracking
const balanceData = useRealTimeBalance();

// Entry fee processing
const entryFeeData = useEntryFeeProcessing(roomId, betAmount);

// Error handling
const errorHandler = usePrizeWheelErrorHandler();
```

## Usage Examples

### Basic Prize Wheel Setup

```typescript
import React from 'react';
import { PrizeWheelReadySection } from '@/components/Game';

export const GameRoom: React.FC<{ roomId: string }> = ({ roomId }) => {
  return (
    <div className="game-container">
      <PrizeWheelReadySection roomId={roomId} />
    </div>
  );
};
```

### Custom Prize Pool Display

```typescript
import React from 'react';
import { usePrizePoolData } from '@/hooks';
import { PrizePoolDisplay, HouseEdgeInfo } from '@/components/Game';

export const CustomPrizePoolView: React.FC<{ roomId: string }> = ({ roomId }) => {
  const prizePoolData = usePrizePoolData(roomId);

  if (prizePoolData.loading) {
    return <div>Loading prize pool...</div>;
  }

  return (
    <div className="prize-pool-view">
      <PrizePoolDisplay roomId={roomId} compact={false} />

      {prizePoolData.prizePool && (
        <HouseEdgeInfo
          houseEdgePercentage={prizePoolData.houseEdge}
          totalPool={prizePoolData.totalPool}
          houseEdgeAmount={prizePoolData.houseEdgeAmount}
          netPrizeAmount={prizePoolData.netPrize}
          playerCount={prizePoolData.currentPlayers}
          entryFee={prizePoolData.entryFee}
        />
      )}
    </div>
  );
};
```

### Balance Management Integration

```typescript
import React, { useState } from 'react';
import { useRealTimeBalance, usePrizeWheelErrorHandler } from '@/hooks';
import { BalanceAndEntryFee } from '@/components/Game';

export const BalanceManager: React.FC<{ roomId: string; betAmount: number }> = ({
  roomId,
  betAmount
}) => {
  const [balanceValidated, setBalanceValidated] = useState(false);
  const balance = useRealTimeBalance();
  const { handleBalanceError } = usePrizeWheelErrorHandler();

  const handleValidation = async () => {
    try {
      const isValid = await balance.actions.validateBalance(betAmount);
      setBalanceValidated(isValid);
    } catch (error) {
      handleBalanceError(error, balance.currentBalance, betAmount);
    }
  };

  return (
    <div className="balance-manager">
      <BalanceAndEntryFee
        roomId={roomId}
        betAmount={betAmount}
        onBalanceValidated={setBalanceValidated}
        showEntryFeeStatus={true}
      />

      <button
        onClick={handleValidation}
        disabled={balance.isValidating}
        className="validate-button"
      >
        {balance.isValidating ? 'Validating...' : 'Validate Balance'}
      </button>
    </div>
  );
};
```

### Admin Dashboard Implementation

```typescript
import React from 'react';
import { AdminDashboard } from '@/components/Admin';

export const AdminPanel: React.FC = () => {
  return (
    <div className="admin-panel">
      <AdminDashboard />
    </div>
  );
};
```

### Error Handling Example

```typescript
import React from 'react';
import { usePrizeWheelErrorHandler } from '@/hooks';
import { apiClient } from '@/services/api';

export const ErrorHandlingExample: React.FC = () => {
  const {
    handleEntryFeeError,
    handleApiErrorWithRetry,
    isErrorType
  } = usePrizeWheelErrorHandler();

  const processPayment = async (userId: string, roomId: string, amount: number) => {
    try {
      const result = await handleApiErrorWithRetry(
        () => apiClient.processEntryFee({
          user_id: userId,
          room_id: roomId,
          bet_amount: amount,
          metadata: { game_type: 'prize_wheel' },
        }),
        3, // max retries
        2000, // retry delay
        { context: 'entry_fee_processing' }
      );

      console.log('Payment successful:', result);
    } catch (error) {
      handleEntryFeeError(error, userId, roomId, amount);

      if (isErrorType(error, 'INSUFFICIENT_BALANCE')) {
        // Handle insufficient balance specifically
        console.log('User needs to add more funds');
      }
    }
  };

  return (
    <button onClick={() => processPayment('user1', 'room1', 10.00)}>
      Process Payment
    </button>
  );
};
```

## Migration Guide

### From Legacy System

1. **Update Imports**
   ```typescript
   // Old
   import { PrizeWheel } from '@/components/Game/PrizeWheel';

   // New
   import { PrizeWheelReadySection } from '@/components/Game';
   ```

2. **Replace Components**
   ```typescript
   // Old
   <PrizeWheel roomId={roomId} />

   // New
   <PrizeWheelReadySection roomId={roomId} />
   ```

3. **Update State Management**
   ```typescript
   // Old
   const { balance } = useAuthStore();

   // New
   const balance = useRealTimeBalance();
   const prizePool = usePrizePoolData(roomId);
   ```

4. **Add Error Handling**
   ```typescript
   // New
   const { handleBalanceError } = usePrizeWheelErrorHandler();
   ```

### Breaking Changes

1. **API Response Format** - New backend returns different response structures
2. **Socket Events** - Enhanced events with additional data
3. **Error Codes** - New error code system for better error handling
4. **State Structure** - Extended game store with prize pool data

### Compatibility

- **Backward Compatible** - Legacy components still work
- **Progressive Enhancement** - New features can be adopted incrementally
- **Graceful Degradation** - Falls back to basic functionality if new features fail

This comprehensive integration provides a seamless, robust, and user-friendly Prize Wheel gaming experience with full backend feature support.
