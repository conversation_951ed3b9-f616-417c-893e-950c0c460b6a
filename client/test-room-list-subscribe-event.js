/**
 * Test Script for Room List Subscribe Event
 * Simulates the exact event structure received from the server
 */

const testSubscribeEvent = {
    "action": "subscribe",
    "count": 4,
    "rooms": [
        {
            "id": "68412c9af494b684c1c18ecf",
            "name": "czxcwee2",
            "game_type": "prizewheel",
            "status": "waiting",
            "current_players": 1,
            "max_players": 2,
            "min_players": 2,
            "bet_amount": 10,
            "currency": "USD",
            "prize_pool": 0,
            "is_private": false,
            "has_password": false,
            "has_space": true,
            "is_featured": false,
            "creator_id": "68333053f035ba37b20bf3a4",
            "created_at": "2025-06-05T05:35:22.488Z",
            "updated_at": "2025-06-11T18:22:12.456Z"
        },
        {
            "id": "68335cec692398dcafbc4f54",
            "name": "test-room-ami-2",
            "game_type": "amidakuji",
            "status": "waiting",
            "current_players": 0,
            "max_players": 3,
            "min_players": 2,
            "bet_amount": 20,
            "currency": "USD",
            "prize_pool": 0,
            "is_private": false,
            "has_password": false,
            "has_space": true,
            "is_featured": false,
            "creator_id": "68333053f035ba37b20bf3a4",
            "created_at": "2025-05-25T18:09:48.116Z",
            "updated_at": "2025-06-04T21:13:26.575Z"
        },
        {
            "id": "68335cd1692398dcafbc4f53",
            "name": "test-room-ami-1",
            "game_type": "amidakuji",
            "status": "waiting",
            "current_players": 0,
            "max_players": 4,
            "min_players": 2,
            "bet_amount": 10,
            "currency": "USD",
            "prize_pool": 0,
            "is_private": false,
            "has_password": false,
            "has_space": true,
            "is_featured": false,
            "creator_id": "68333053f035ba37b20bf3a4",
            "created_at": "2025-05-25T18:09:21.460Z",
            "updated_at": "2025-06-04T21:13:38.604Z"
        },
        {
            "id": "68335ab9692398dcafbc4f4f",
            "name": "test-room-2",
            "game_type": "prizewheel",
            "status": "waiting",
            "current_players": 0,
            "max_players": 4,
            "min_players": 2,
            "bet_amount": 10,
            "currency": "USD",
            "prize_pool": 0,
            "is_private": false,
            "has_password": false,
            "has_space": true,
            "is_featured": false,
            "creator_id": "68333053f035ba37b20bf3a4",
            "created_at": "2025-05-25T18:00:25.274Z",
            "updated_at": "2025-06-04T22:06:08.708Z"
        }
    ],
    "socketId": "sP9VW05z2XQ2TSaDAAAB",
    "userId": "683b07882d7dbd11e92bf29d",
    "username": "res2",
    "source": "manager-service",
    "timestamp": "2025-06-12T15:15:16.831Z"
};

console.log('🧪 Testing Room List Subscribe Event Processing');
console.log('================================================');

// Test 1: Event Structure Validation
console.log('\n1. Event Structure Validation:');
console.log(`✅ Action: ${testSubscribeEvent.action}`);
console.log(`✅ Count: ${testSubscribeEvent.count}`);
console.log(`✅ Rooms: ${testSubscribeEvent.rooms.length} rooms`);
console.log(`✅ User: ${testSubscribeEvent.username} (${testSubscribeEvent.userId})`);
console.log(`✅ Source: ${testSubscribeEvent.source}`);
console.log(`✅ Timestamp: ${testSubscribeEvent.timestamp}`);

// Test 2: Room Data Processing
console.log('\n2. Room Data Processing:');
testSubscribeEvent.rooms.forEach((room, index) => {
    console.log(`\n   Room ${index + 1}:`);
    console.log(`   - ID: ${room.id}`);
    console.log(`   - Name: ${room.name}`);
    console.log(`   - Game Type: ${room.game_type}`);
    console.log(`   - Players: ${room.current_players}/${room.max_players}`);
    console.log(`   - Bet Amount: ${room.bet_amount} ${room.currency}`);
    console.log(`   - Status: ${room.status}`);
    console.log(`   - Private: ${room.is_private}`);
    console.log(`   - Has Space: ${room.has_space}`);
});

// Test 3: Field Mapping (snake_case to camelCase)
console.log('\n3. Field Mapping Test:');
const convertRoom = (roomData) => {
    return {
        id: roomData.id,
        name: roomData.name,
        gameType: roomData.game_type?.toUpperCase(),
        playerCount: roomData.current_players,
        maxPlayers: roomData.max_players,
        betAmount: roomData.bet_amount,
        currency: roomData.currency,
        status: roomData.status?.toUpperCase(),
        isPrivate: roomData.is_private,
        hasSpace: roomData.has_space,
        createdAt: roomData.created_at,
        updatedAt: roomData.updated_at,
    };
};

const convertedRooms = testSubscribeEvent.rooms.map(convertRoom);
console.log('Converted rooms:', convertedRooms.map(room => ({
    name: room.name,
    gameType: room.gameType,
    players: `${room.playerCount}/${room.maxPlayers}`,
    bet: room.betAmount,
    status: room.status
})));

// Test 4: Event Processing Summary
console.log('\n4. Event Processing Summary:');
console.log(`📊 Total rooms received: ${testSubscribeEvent.count}`);
console.log(`🎮 Game types: ${[...new Set(testSubscribeEvent.rooms.map(r => r.game_type))].join(', ')}`);
console.log(`👥 Total players: ${testSubscribeEvent.rooms.reduce((sum, r) => sum + r.current_players, 0)}`);
console.log(`💰 Bet amounts: ${[...new Set(testSubscribeEvent.rooms.map(r => r.bet_amount))].join(', ')} ${testSubscribeEvent.rooms[0].currency}`);
console.log(`🏠 Available rooms: ${testSubscribeEvent.rooms.filter(r => r.has_space).length}`);
console.log(`🔒 Private rooms: ${testSubscribeEvent.rooms.filter(r => r.is_private).length}`);

console.log('\n✅ All tests completed successfully!');
console.log('The room_list_updated event with action "subscribe" is properly structured and can be processed.');
