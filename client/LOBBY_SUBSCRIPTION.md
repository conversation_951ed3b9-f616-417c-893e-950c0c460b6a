# Lobby Subscription Feature

This document describes the real-time lobby subscription feature implemented in the client application.

## Overview

The lobby subscription feature provides real-time updates for room changes in the game lobby. When users are viewing the rooms list, they automatically receive live updates when:

- New rooms are created
- Existing rooms are updated (settings changed)
- Rooms are deleted
- Player counts change (players join/leave rooms)

## Architecture

### Components

1. **LobbyStore** (`src/store/lobbyStore.ts`)
   - Manages lobby subscription state
   - Handles socket events for room list updates
   - Provides hooks for components to listen to updates

2. **GameStore** (`src/store/gameStore.ts`)
   - Updated to handle real-time room updates
   - Integrates with lobby store for seamless updates

3. **SocketService** (`src/services/socket.ts`)
   - Extended with lobby subscription methods
   - Handles `room_list_updated` events

4. **RoomsPage** (`src/pages/RoomsPage.tsx`)
   - Automatically subscribes to lobby updates
   - Shows real-time status indicator
   - Displays visual feedback for updated rooms

### Event Flow

```
1. User visits RoomsPage
2. <PERSON> subscribes to lobby updates via socket
3. Server publishes room changes to lobby channel
4. Client receives updates and updates room list
5. Visual indicators show recently updated rooms
```

## Features

### Real-time Status Indicator

The rooms page header shows the current connection status:
- 🟢 **Live**: Connected and receiving real-time updates
- 🟡 **Connecting...**: Attempting to connect
- 🔴 **Offline**: Not connected to real-time updates

### Visual Update Indicators

- Recently updated rooms are highlighted with a blue border
- A lightning bolt icon appears next to updated room names
- Highlights automatically fade after 3 seconds

### Smart Notifications

- **Room Created**: Success toast with room name
- **Room Deleted**: Info toast with room name
- **Player Count Changed**: Subtle notification with player count
- **Connection Status**: Notifications for connection/disconnection

### Automatic Subscription Management

- Subscribes to lobby when viewing rooms list
- Unsubscribes when joining a room (to avoid conflicts)
- Re-subscribes when leaving a room
- Cleans up subscriptions when leaving the page

## Usage

### For Users

1. Navigate to the rooms page
2. Look for the "Live" indicator in the header
3. Watch for real-time updates:
   - New rooms appear automatically
   - Player counts update in real-time
   - Deleted rooms disappear automatically
   - Updated rooms are highlighted

### For Developers

#### Using the Lobby Store

```typescript
import { useLobbyStore, useRoomListUpdates } from '@/store/lobbyStore';

const MyComponent = () => {
  const { isSubscribed, subscribeLobby, unsubscribeLobby } = useLobbyStore();

  // Subscribe to updates
  useEffect(() => {
    subscribeLobby();
    return () => unsubscribeLobby();
  }, []);

  // Listen to room updates
  useRoomListUpdates((data) => {
    console.log('Room updated:', data);
  });
};
```

#### Handling Room Updates

```typescript
import { useGameStore } from '@/store/gameStore';

const { handleRoomListUpdate } = useGameStore();

// Process room update
handleRoomListUpdate({
  action: 'player_count_changed',
  room: {
    id: 'room-123',
    playerCount: 3,
    maxPlayers: 8,
    // ... other room data
  }
});
```

## Event Types

### `room_list_updated`

Sent when any room in the lobby changes or when initially subscribing to the lobby:

```typescript
// Base room data structure
interface RoomUpdateData {
  id: string;
  name: string;
  gameType: string;
  playerCount: number;
  maxPlayers: number;
  status: string;
  betAmount: number;
  currency?: string;
  isPrivate?: boolean;
  createdAt: string;
  updatedAt?: string;
}

// Room list update data - supports both single room updates and initial bulk load
type RoomListUpdateData =
  | {
      action: 'initial_load';
      rooms: RoomUpdateData[];
      timestamp?: string;
      error?: string;
    }
  | {
      action: 'created' | 'updated' | 'deleted' | 'player_count_changed';
      room: RoomUpdateData;
      timestamp?: string;
    };
```

### Actions

- **`initial_load`**: Complete room list sent when first subscribing to lobby (published by Game Service via Socket Gateway)
- **`created`**: New room created (published by Game Service)
- **`updated`**: Room settings updated (published by Manager Service) or status changed (published by Game Service)
- **`deleted`**: Room deleted (published by Game Service)
- **`player_count_changed`**: Player joined/left room (published by Game Service)

## Benefits

1. **Real-time Experience**: Users see changes immediately without refreshing
2. **Reduced Server Load**: No need for constant polling
3. **Better UX**: Visual feedback shows what's happening
4. **Automatic Updates**: Room list stays current without user action
5. **Smart Notifications**: Users are informed of relevant changes

## Error Handling

- Connection failures are handled gracefully
- Fallback to manual refresh if real-time updates fail
- Clear error messages for subscription issues
- Automatic retry logic for connection problems

## Performance

- Efficient event-driven updates (no polling)
- Minimal data transfer (only changed rooms)
- Smart subscription management (subscribe/unsubscribe as needed)
- Debounced visual updates to prevent UI flicker
