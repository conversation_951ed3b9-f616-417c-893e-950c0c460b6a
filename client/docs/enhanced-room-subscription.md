# Enhanced Room Subscription Management

This document explains the new client-side room subscription management system that provides automatic lobby fallback and real-time room updates.

## Overview

The enhanced room subscription management system implements the following behavior:

### 1. **On Join Failure**
- If a room join request fails (due to room full, invalid room ID, insufficient balance, etc.)
- The client automatically re-subscribes to the lobby to receive updated room listings
- Ensures the client receives fresh lobby data to show current available rooms

### 2. **On Join Success**
- If the room join request succeeds
- The client subscribes to room-specific events for that room ID
- This replaces the lobby subscription with room-specific event listening

### 3. **Room Info Updates**
- After successfully subscribing to room events, the client receives `room_info_updated` events
- These events contain real-time updates about room state (player count, game status, etc.)
- Both newly joined players AND existing players in the room receive these updates

## Technical Implementation

### Core Components

1. **Enhanced Subscription Manager** (`enhancedSubscriptionManager`)
   - Manages subscription state transitions
   - Handles automatic lobby fallback on join failure
   - Tracks subscription history for debugging

2. **Room Subscription Hook** (`useRoomSubscription`)
   - React hook that provides easy-to-use interface
   - Handles real-time room info updates
   - Manages subscription state in React components

3. **Socket Store Integration**
   - Updated to use enhanced subscription management
   - <PERSON>les `room_info_updated` events
   - Provides backward compatibility

### Socket Events

**Emitted Events:**
- `join_room` - Join a specific room
- `subscribe_lobby` - Subscribe to lobby updates
- `subscribe_room` - Subscribe to room-specific events

**Listened Events:**
- `room_joined` - Room join successful
- `room_join_failed` - Room join failed
- `room_info_updated` - Real-time room state updates
- `lobby_updated` - Fresh lobby room listings

## Usage Examples

### Basic Room Join with Automatic Fallback

```typescript
import { useRoomSubscription } from '@/hooks/useRoomSubscription';

function RoomJoinComponent() {
  const {
    joinRoom,
    isJoining,
    currentSubscription,
    error
  } = useRoomSubscription({
    // Automatic lobby fallback on join failure
    autoLobbyFallback: true,
    
    // Handle real-time room updates
    onRoomInfoUpdate: (update) => {
      console.log('Room updated:', update);
      if (update.action === 'player_joined') {
        toast.success(`${update.playerName} joined!`);
      }
    },
    
    // Handle subscription changes
    onSubscriptionChange: (type, roomId) => {
      if (type === 'lobby') {
        console.log('Back in lobby - showing room list');
      } else if (type === 'room') {
        console.log(`Subscribed to room ${roomId}`);
      }
    }
  });

  const handleJoinRoom = async (roomId: string) => {
    try {
      await joinRoom(roomId);
      // Success - now subscribed to room events
    } catch (error) {
      // Error handled automatically
      // Client is back in lobby if autoLobbyFallback is true
    }
  };

  return (
    <div>
      <button 
        onClick={() => handleJoinRoom('room-123')}
        disabled={isJoining}
      >
        {isJoining ? 'Joining...' : 'Join Room'}
      </button>
      
      <div>
        Status: {currentSubscription || 'Not subscribed'}
      </div>
      
      {error && <div>Error: {error.message}</div>}
    </div>
  );
}
```

### Advanced Usage with Manual Subscription Control

```typescript
import { useRoomSubscription } from '@/hooks/useRoomSubscription';

function AdvancedRoomComponent() {
  const {
    joinRoom,
    leaveRoom,
    subscribeToLobby,
    unsubscribeFromLobby,
    currentSubscription,
    currentRoom,
    getSubscriptionState,
    getTransitionHistory
  } = useRoomSubscription();

  const handleManualLobbySubscription = async () => {
    try {
      await subscribeToLobby();
      console.log('Manually subscribed to lobby');
    } catch (error) {
      console.error('Failed to subscribe to lobby:', error);
    }
  };

  const handleLeaveRoom = async () => {
    if (currentRoom) {
      try {
        await leaveRoom(currentRoom.id, 'voluntary');
        // Automatically returns to lobby subscription
      } catch (error) {
        console.error('Failed to leave room:', error);
      }
    }
  };

  // Debug information
  const debugInfo = {
    state: getSubscriptionState(),
    history: getTransitionHistory()
  };

  return (
    <div>
      <div>Current: {currentSubscription}</div>
      <div>Room: {currentRoom?.name || 'None'}</div>
      
      <button onClick={handleManualLobbySubscription}>
        Subscribe to Lobby
      </button>
      
      <button onClick={handleLeaveRoom}>
        Leave Room
      </button>
      
      <details>
        <summary>Debug Info</summary>
        <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
      </details>
    </div>
  );
}
```

### Real-time Room Updates

```typescript
import { useRoomSubscription } from '@/hooks/useRoomSubscription';

function RoomUpdatesComponent() {
  const [roomUpdates, setRoomUpdates] = useState([]);

  const { currentRoom } = useRoomSubscription({
    onRoomInfoUpdate: (update) => {
      // Handle different types of room updates
      switch (update.action) {
        case 'player_joined':
          toast.success(`${update.playerName} joined the room`);
          break;
        case 'player_left':
          toast.info(`${update.playerName} left the room`);
          break;
        case 'game_started':
          toast.success('Game has started!');
          break;
        case 'room_updated':
          console.log('Room state updated:', update);
          break;
      }

      // Keep track of updates for display
      setRoomUpdates(prev => [update, ...prev].slice(0, 10));
    }
  });

  return (
    <div>
      {currentRoom && (
        <div>
          <h3>{currentRoom.name}</h3>
          <p>Players: {currentRoom.playerCount}/{currentRoom.maxPlayers}</p>
          <p>Status: {currentRoom.status}</p>
        </div>
      )}

      <div>
        <h4>Recent Updates:</h4>
        {roomUpdates.map((update, index) => (
          <div key={index}>
            {update.action}: {update.playerName || 'System'}
            {update.playerCount && ` (${update.playerCount}/${update.maxPlayers})`}
          </div>
        ))}
      </div>
    </div>
  );
}
```

## Error Handling

The system provides robust error handling:

1. **Join Failures**: Automatically handled with lobby fallback
2. **Network Errors**: Graceful degradation with user feedback
3. **Subscription Errors**: Continues operation even if some subscriptions fail
4. **Concurrent Operations**: Prevents duplicate subscription attempts

## Debugging

### Subscription State

```typescript
const { getSubscriptionState } = useRoomSubscription();

// Get current subscription state
const state = getSubscriptionState();
console.log('Current subscription:', state.currentSubscription);
console.log('Room ID:', state.roomId);
console.log('Is transitioning:', state.isTransitioning);
```

### Transition History

```typescript
const { getTransitionHistory } = useRoomSubscription();

// Get subscription transition history
const history = getTransitionHistory();
history.forEach(transition => {
  console.log(`${transition.from} -> ${transition.to} (${transition.reason})`);
});
```

## Migration Guide

### From Legacy Socket Store

**Before:**
```typescript
const { joinRoom, currentRoom } = useSocketStore();

await joinRoom(roomId);
```

**After:**
```typescript
const { joinRoom, currentRoom } = useRoomSubscription({
  autoLobbyFallback: true
});

await joinRoom(roomId);
// Automatic lobby fallback on failure
```

### Key Benefits

1. **Automatic Fallback**: No manual lobby re-subscription needed
2. **Real-time Updates**: Automatic room info updates for all participants
3. **Clean State Management**: Prevents duplicate subscriptions
4. **Better Error Handling**: Graceful degradation and user feedback
5. **Debugging Support**: Comprehensive state tracking and history

## Best Practices

1. **Always use `autoLobbyFallback: true`** for better user experience
2. **Handle `onRoomInfoUpdate`** to show real-time changes to users
3. **Use `onSubscriptionChange`** to update UI based on subscription state
4. **Check `isJoining` and `isLeaving`** to show loading states
5. **Handle errors gracefully** - the system provides automatic fallback but you should still show user feedback
