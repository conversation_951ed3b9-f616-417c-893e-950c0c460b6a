# Player Slots Integration Guide

This guide shows how to integrate real-time player slots that automatically update from `room_info_updated` events.

## Quick Start

### 1. Simple Integration

For most use cases, use the `SimplePlayerSlots` component:

```tsx
import { SimplePlayerSlots } from '@/components/Room/RoomPlayerDisplay';

function RoomPage() {
  return (
    <div>
      <h2>Current Players</h2>
      <SimplePlayerSlots />
    </div>
  );
}
```

### 2. Compact Layout

For headers or sidebars, use the compact version:

```tsx
import { CompactPlayerSlots } from '@/components/Room/RoomPlayerDisplay';

function RoomHeader() {
  return (
    <div className="flex justify-between items-center">
      <h1>Game Room</h1>
      <CompactPlayerSlots />
    </div>
  );
}
```

### 3. Full Featured

For complete control, use the main component:

```tsx
import { EnhancedPlayerSlots } from '@/components/Room/EnhancedPlayerSlots';

function FullRoomView() {
  return (
    <div>
      <EnhancedPlayerSlots
        showColorSelection={true}
        showReadyStatus={true}
        showBetAmount={true}
        showJoinTime={true}
      />
    </div>
  );
}
```

## Event Data Structure

The components automatically handle `room_info_updated` events with this structure:

```json
{
  "room": {
    "id": "68412c9af494b684c1c18ecf",
    "name": "czxcwee2",
    "gameType": "prizewheel",
    "status": "waiting",
    "playerCount": 1,
    "maxPlayers": 2,
    "betAmount": 10,
    "prizePool": 0,
    "isPrivate": false,
    "createdAt": "2025-06-09T04:31:20Z",
    "updatedAt": "2025-06-09T07:55:20.700Z"
  },
  "roomState": {
    "playerCount": 1,
    "readyCount": 0,
    "canStartGame": false,
    "prizePool": 0,
    "gameInProgress": false,
    "countdown": null
  },
  "players": [
    {
      "betAmount": 10,
      "isReady": false,
      "joinedAt": "2025-06-09T07:55:19Z",
      "position": 0,
      "userId": "68334427b8ef34dc195f27bd",
      "username": "res"
    }
  ],
  "gameSpecificData": {
    "gameType": "prizewheel",
    "colorSelections": {},
    "availableColors": ["red", "blue", "green", "yellow", "purple", "orange", "pink", "teal"],
    "playerColorMappings": {},
    "colorSelectionTimestamps": {}
  },
  "timestamp": "2025-06-09T07:55:20.700Z"
}
```

## Component Options

### RoomPlayerDisplay Props

```tsx
interface RoomPlayerDisplayProps {
  /** Maximum number of player slots to show */
  maxSlots?: number;
  /** Show ready status indicators */
  showReady?: boolean;
  /** Show player colors (for games like Prize Wheel) */
  showColors?: boolean;
  /** Compact layout for smaller spaces */
  compact?: boolean;
  /** Custom CSS classes */
  className?: string;
}
```

### EnhancedPlayerSlots Props

```tsx
interface EnhancedPlayerSlotsProps {
  showColorSelection?: boolean;
  showReadyStatus?: boolean;
  showBetAmount?: boolean;
  showJoinTime?: boolean;
  className?: string;
}
```

## Real-time Updates

The components automatically:

1. **Listen for room_info_updated events** via the enhanced subscription system
2. **Update player positions** based on the `position` field
3. **Show ready status** with visual indicators
4. **Display color selections** for Prize Wheel games
5. **Handle player join/leave** events seamlessly

## Styling Examples

### Custom Styling

```tsx
<SimplePlayerSlots className="bg-gray-100 p-4 rounded-lg" />
```

### Grid Layout

```tsx
<RoomPlayerDisplay
  maxSlots={6}
  className="grid-cols-3 md:grid-cols-6"
/>
```

### Compact Header

```tsx
<CompactPlayerSlots className="flex-shrink-0" />
```

## Integration with Room Subscription

The components work seamlessly with the enhanced room subscription system:

```tsx
import { useRoomSubscription } from '@/hooks/useRoomSubscription';
import { SimplePlayerSlots } from '@/components/Room/RoomPlayerDisplay';

function RoomPage() {
  const { currentRoom, joinRoom } = useRoomSubscription({
    autoLobbyFallback: true,
    onRoomInfoUpdate: (update) => {
      console.log('Players updated:', update.players);
    },
  });

  return (
    <div>
      {currentRoom ? (
        <div>
          <h2>{currentRoom.name}</h2>
          <SimplePlayerSlots />
        </div>
      ) : (
        <button onClick={() => joinRoom('room-id')}>
          Join Room
        </button>
      )}
    </div>
  );
}
```

## Game-Specific Features

### Prize Wheel Color Selection

For Prize Wheel games, the components automatically show:
- Available colors
- Player color selections
- Color selection status

```tsx
<EnhancedPlayerSlots
  showColorSelection={true} // Shows color indicators
/>
```

### Ready Status

Shows visual indicators for player ready status:
- ✓ Green checkmark for ready players
- ⏳ Yellow indicator for not ready

```tsx
<RoomPlayerDisplay
  showReady={true} // Shows ready indicators
/>
```

## Testing

Use the demo component to test the integration:

```tsx
import { PlayerSlotsDemo } from '@/components/examples/PlayerSlotsDemo';

// Add to your routes for testing
<Route path="/demo/player-slots" component={PlayerSlotsDemo} />
```

## Common Use Cases

### 1. Game Room Page

```tsx
function GameRoomPage() {
  return (
    <div className="container mx-auto p-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          {/* Game area */}
          <GameCanvas />
        </div>
        <div>
          {/* Player slots sidebar */}
          <div className="bg-white rounded-lg p-4">
            <h3 className="font-semibold mb-4">Players</h3>
            <SimplePlayerSlots />
          </div>
        </div>
      </div>
    </div>
  );
}
```

### 2. Lobby Room Card

```tsx
function RoomCard({ room }) {
  return (
    <div className="border rounded-lg p-4">
      <div className="flex justify-between items-start mb-3">
        <h3 className="font-semibold">{room.name}</h3>
        <span className="text-sm text-gray-500">
          {room.playerCount}/{room.maxPlayers}
        </span>
      </div>
      
      <CompactPlayerSlots />
      
      <button className="w-full mt-3 bg-blue-500 text-white py-2 rounded">
        Join Room
      </button>
    </div>
  );
}
```

### 3. Room Header

```tsx
function RoomHeader() {
  return (
    <header className="bg-white shadow-sm border-b p-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold">Prize Wheel Room</h1>
          <p className="text-gray-600">Waiting for players...</p>
        </div>
        
        <div className="flex items-center gap-4">
          <CompactPlayerSlots />
          <button className="bg-red-500 text-white px-4 py-2 rounded">
            Leave Room
          </button>
        </div>
      </div>
    </header>
  );
}
```

## Troubleshooting

### No Players Showing

1. Ensure you're subscribed to room events:
   ```tsx
   const { currentSubscription } = useRoomSubscription();
   console.log('Subscription:', currentSubscription); // Should be 'room'
   ```

2. Check if room_info_updated events are being received:
   ```tsx
   useRoomSubscription({
     onRoomInfoUpdate: (update) => {
       console.log('Room update:', update);
     },
   });
   ```

### Colors Not Showing

Ensure the game type supports colors and the event includes `gameSpecificData`:

```tsx
<EnhancedPlayerSlots
  showColorSelection={true}
/>
```

### Ready Status Not Updating

Check that the `isReady` field is included in the player data:

```json
{
  "players": [
    {
      "userId": "123",
      "username": "Player1",
      "isReady": true,  // This field is required
      "position": 0
    }
  ]
}
```

## Performance Notes

- Components only re-render when player data changes
- Automatic cleanup when component unmounts
- Efficient position-based player mapping
- Minimal DOM updates for smooth animations

The player slots components are designed to be drop-in replacements that automatically handle all the complexity of real-time room updates while providing a clean, responsive UI.
