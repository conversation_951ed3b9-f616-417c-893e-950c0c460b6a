# Prize Wheel Game - Complete Documentation

## 📋 Overview

This comprehensive documentation covers the complete Prize Wheel game implementation, including API endpoints, Socket.IO events, implementation examples, and integration guides for the XZ Game Manager system.

## 🎯 What's Included

The Prize Wheel game system includes:

- **Prize Pool Management**: Automatic prize pool creation, management, and distribution
- **Entry Fee Processing**: Balance validation and transaction handling
- **Real-time Events**: Socket.IO integration for live game updates
- **Dashboard UI**: Complete management interface for administrators
- **Multi-service Architecture**: Seamless integration across all microservices

---

## 📚 Documentation Structure

### 1. [API Documentation](./prize-wheel-comprehensive-api-documentation.md)

**Complete API reference covering:**
- Prize Pool Management API (GET, POST, PUT endpoints)
- Entry Fee Processing API (validation, processing, refunds)
- Room Management API (enhanced room details, player management)
- Authentication and authorization patterns
- Request/response formats with real examples

**Key Endpoints:**
```
GET    /api/prize-pools              # List all prize pools
GET    /api/prize-pools/:id          # Get specific prize pool
GET    /api/rooms/:id/prize-pool     # Get room's prize pool
POST   /api/rooms/:id/distribute-prizes  # Distribute prizes
POST   /api/v1/entry_fees/validate_balance  # Validate user balance
POST   /api/v1/entry_fees/process_ready_fee # Process entry fee
POST   /api/v1/entry_fees/process_refund    # Process refund
```

### 2. [Socket.IO Event Documentation](./prize-wheel-comprehensive-api-documentation.md#event-documentation)

**Real-time event specifications:**
- Client-to-server events (join_room, select_wheel_color, player_ready)
- Server-to-client events (room_info_updated, game_state_changed, wheel_result)
- Event payload structures with realistic data examples
- Complete game cycle event flows
- Error handling and notification events

**Key Events:**
```javascript
// Client Events
socket.emit('join_room', { roomId })
socket.emit('select_wheel_color', { roomId, color })
socket.emit('player_ready', { roomId })

// Server Events
socket.on('room_info_updated', (data) => {})
socket.on('game_state_changed', (data) => {})
socket.on('wheel_result', (data) => {})
socket.on('user_balance_updated', (data) => {})
```

### 3. [Implementation Examples](./prize-wheel-implementation-examples.md)

**Practical code examples:**
- React Prize Wheel component with full game logic
- Dashboard integration for prize pool management
- Node.js backend service integration
- Real-time event handling patterns
- Error handling and recovery scenarios

**Featured Examples:**
- Complete React game component (300+ lines)
- Dashboard prize pool management interface
- Backend service integration class
- Socket.IO event handling patterns

### 4. [Response Schemas & Integration Guide](./prize-wheel-response-schemas.md)

**Complete integration specifications:**
- TypeScript interfaces for all data structures
- Data validation rules and constraints
- HTTP status codes and error handling
- Step-by-step integration guide
- Testing and debugging procedures

**Key Schemas:**
```typescript
interface PrizePool {
  id: string;
  room_id: string;
  total_pool: number;
  entry_fee_per_player: number;
  contributing_players: string[];
  house_edge_percentage: number;
  net_prize_amount: number;
  status: 'accumulating' | 'locked' | 'distributed' | 'cancelled';
  // ... more fields
}
```

---

## 🚀 Quick Start Guide

### Prerequisites

1. **Services Running:**
   ```bash
   # Infrastructure
   docker-compose up -d mongodb redis
   
   # Core services
   cd services/manager-service && npm start
   cd services/socket-gateway && npm start
   cd services/game-service && npm start
   cd services/room-service && npm start
   
   # Dashboard
   cd services/dashboard-service && npm run dev
   ```

2. **Authentication:**
   ```javascript
   const response = await fetch('http://localhost:3002/auth/login', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({ username: 'admin', password: 'password123' })
   });
   const { token } = await response.json();
   ```

### Basic Implementation

1. **Connect to Socket Gateway:**
   ```javascript
   const socket = io('ws://localhost:3001', {
     query: { token: 'your-jwt-token' }
   });
   ```

2. **Join a Room:**
   ```javascript
   socket.emit('join_room', { roomId: 'room_123' }, (response) => {
     console.log('Joined room:', response);
   });
   ```

3. **Select Color and Get Ready:**
   ```javascript
   socket.emit('select_wheel_color', { roomId: 'room_123', color: 'red' });
   socket.emit('player_ready', { roomId: 'room_123' });
   ```

4. **Handle Game Events:**
   ```javascript
   socket.on('wheel_result', (data) => {
     if (data.winner.userId === currentUserId) {
       console.log(`You won $${data.winner.prizeAmount}!`);
     }
   });
   ```

---

## 🏗️ Enhanced Architecture Overview

### Current Service Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                             │
├─────────────────┬─────────────────┬─────────────────────────────┤
│   React Client  │  Admin Dashboard│     Mobile App (Future)     │
│  (Prize Wheel)  │  (Management)   │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                     API Gateway Layer                          │
├─────────────────┬─────────────────┬─────────────────────────────┤
│  Socket Gateway │   REST API      │    Authentication          │
│  (Real-time)    │  (HTTP/HTTPS)   │    (JWT/OAuth)              │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Business Logic Layer                        │
├─────────────────┬─────────────────┬─────────────────────────────┤
│ Manager Service │  Game Service   │    Room Service             │
│ (Prize Pools,   │ (Game Logic,    │   (Room Management,         │
│  Entry Fees,    │  Wheel Spin,    │    Player States)           │
│  Transactions)  │  Results)       │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Data & Cache Layer                         │
├─────────────────┬─────────────────┬─────────────────────────────┤
│    MongoDB      │     Redis       │    File Storage             │
│ (Persistent     │ (Cache, Pub/Sub,│   (Logs, Backups)           │
│  Data Storage)  │  Session Store) │                             │
└─────────────────┴─────────────────┴─────────────────────────────┘
```

### Enhanced Data Flow for Prize Wheel Game

#### 1. Player Onboarding Flow
```
Client → Socket Gateway → Room Service → Manager Service
  │                                           │
  │                                           ▼
  │                                    Balance Validation
  │                                           │
  │                                           ▼
  └─────────────── Real-time Updates ◄─── Prize Pool Check
```

#### 2. Game Participation Flow
```
Color Selection → Entry Fee Processing → Ready State → Game Start
      │                    │                  │            │
      ▼                    ▼                  ▼            ▼
  Game Service      Manager Service    Socket Gateway  Game Service
      │                    │                  │            │
      ▼                    ▼                  ▼            ▼
  State Sync        Prize Pool Update   Real-time Sync  Wheel Spin
```

#### 3. Prize Distribution Flow
```
Game End → Winner Calculation → Prize Distribution → Balance Updates
    │             │                      │                │
    ▼             ▼                      ▼                ▼
Game Service  Game Service        Manager Service   Socket Gateway
    │             │                      │                │
    ▼             ▼                      ▼                ▼
State Update  Result Broadcast    Transaction Log   Real-time Notify
```

---

## 🔧 Enhanced Key Features

### Advanced Prize Pool Management
- ✅ Automatic prize pool creation and lifecycle management
- ✅ Real-time pool value tracking with live updates
- ✅ Configurable house edge with transparent calculations
- ✅ Multi-player contribution tracking and analytics
- ✅ Automatic prize distribution with audit trails
- ✅ Potential winnings calculator for players
- ✅ Prize pool growth visualization and charts
- ✅ House edge transparency and breakdown display

### Comprehensive Entry Fee System
- ✅ Pre-validation balance checking with real-time updates
- ✅ Automatic fee processing with retry logic
- ✅ Intelligent refund processing with state management
- ✅ Complete transaction logging and history
- ✅ Advanced error handling with user-friendly messages
- ✅ Balance validation before color selection
- ✅ Entry fee status tracking and notifications

### Enhanced Real-time Features
- ✅ Live room updates with enhanced player data
- ✅ Real-time color selection with conflict resolution
- ✅ Advanced game state management (waiting → ready → spinning → result)
- ✅ Live balance updates with transaction notifications
- ✅ Animated winner announcements with prize details
- ✅ Real-time prize pool growth tracking
- ✅ Enhanced socket events with comprehensive data

### Advanced Dashboard & Admin Features
- ✅ Comprehensive prize pool monitoring dashboard
- ✅ Advanced entry fee management with analytics
- ✅ Enhanced room management with player moderation
- ✅ Detailed transaction history and reporting
- ✅ Player management tools with kick/ban functionality
- ✅ System health monitoring and performance metrics
- ✅ Real-time statistics and analytics dashboard

### Client-Side Enhancements
- ✅ Custom React hooks for prize wheel integration
- ✅ Comprehensive error handling with specific error codes
- ✅ Real-time balance tracking and validation
- ✅ Prize pool visualization components
- ✅ Winner announcement animations
- ✅ House edge information display
- ✅ Potential winnings calculator
- ✅ Transaction history components

---

## 📊 Enhanced Business Logic

### Advanced Prize Pool Calculation
```
Total Pool = Sum of all entry fees from contributing players
House Edge Amount = Total Pool × House Edge Percentage (default 5%)
Net Prize Amount = Total Pool - House Edge Amount
Winner Prize = Net Prize Amount (winner takes all)

Potential Winnings = Net Prize Amount (if player wins)
Win Probability = 1 / Number of Players
Expected Value = Potential Winnings × Win Probability
```

### Enhanced Game Flow States
1. **Setup**: Room creation, prize pool initialization
2. **Waiting**: Players join, balance validation, color selection
3. **Ready**: Entry fee processing, final player confirmations
4. **Countdown**: Minimum players ready, countdown to game start
5. **Spinning**: Wheel spins, no player actions, real-time updates
6. **Result**: Winner determined, prizes distributed, notifications sent
7. **Reset**: Room cleanup, prize pool reset for next game

### Comprehensive Entry Fee Processing
1. **Pre-Validation**: Real-time balance checking before actions
2. **Color Selection Validation**: Ensure sufficient balance before color selection
3. **Entry Fee Processing**:
   - Validate final balance
   - Process transaction with retry logic
   - Update prize pool
   - Send real-time notifications
4. **Refund Processing**:
   - Automatic refund on player unready
   - Transaction logging
   - Prize pool adjustment
   - Balance update notifications

### Error Handling & Recovery
1. **Balance Validation Errors**: Clear messaging, retry options
2. **Payment Processing Errors**: Automatic retry with exponential backoff
3. **Network Errors**: Graceful degradation, offline state management
4. **Game State Conflicts**: Automatic synchronization and conflict resolution

---

## 🧪 Comprehensive Testing

### API Testing
```bash
# Test enhanced prize pool endpoints
curl -X GET http://localhost:3002/api/rooms/room1/prize-pool \
  -H "Authorization: Bearer TOKEN"

curl -X GET http://localhost:3002/api/rooms/room1/potential-winnings \
  -H "Authorization: Bearer TOKEN"

# Test entry fee processing
curl -X POST http://localhost:3002/api/v1/entry_fees/process_entry_fee \
  -H "Authorization: Bearer TOKEN" \
  -d '{"user_id":"user1","room_id":"room1","bet_amount":10.00,"metadata":{"game_type":"prize_wheel"}}'

# Test balance validation
curl -X POST http://localhost:3002/api/v1/entry_fees/validate_balance \
  -H "Authorization: Bearer TOKEN" \
  -d '{"user_id":"user1","bet_amount":10.00}'

# Test refund processing
curl -X POST http://localhost:3002/api/v1/entry_fees/process_refund \
  -H "Authorization: Bearer TOKEN" \
  -d '{"user_id":"user1","room_id":"room1","bet_amount":10.00,"metadata":{"reason":"player_unready"}}'

# Test enhanced room details
curl -X GET http://localhost:3002/admin/rooms/room1 \
  -H "Authorization: Bearer TOKEN"
```

### Enhanced Socket.IO Testing
```javascript
// Test complete enhanced game flow
const socket = io('ws://localhost:3001', { query: { token } });

// Join room and listen for enhanced events
socket.emit('join_room', { roomId: 'test_room' });

socket.on('user_balance_updated', (data) => {
  console.log('Balance updated:', data);
});

socket.on('user_transaction_completed', (data) => {
  console.log('Transaction completed:', data);
});

socket.on('wheel_spinning', (data) => {
  console.log('Wheel spinning:', data);
});

socket.on('wheel_result', (data) => {
  console.log('Game result:', data);
});

// Test game flow
socket.emit('select_wheel_color', { roomId: 'test_room', color: 'red' });
socket.emit('player_ready', { roomId: 'test_room' });
```

### Client-Side Testing
```bash
# Run comprehensive test suite
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test categories
npm test -- --grep "prize pool"
npm test -- --grep "entry fee"
npm test -- --grep "balance validation"

# Run integration tests
npm run test:integration

# Run visual regression tests
npm run test:visual
```

### Testing Coverage Areas
- **Unit Tests**: Individual components and utilities (70%+ coverage)
- **Integration Tests**: Feature interaction testing
- **Hook Tests**: Custom React hooks testing
- **Error Handling Tests**: Edge cases and error scenarios
- **Socket Event Tests**: Real-time event handling
- **API Integration Tests**: Backend service integration

---

## 🔍 Enhanced Troubleshooting

### Common Issues & Solutions

1. **"INSUFFICIENT_BALANCE" Error**
   - **Cause**: User balance is less than required entry fee
   - **Solution**: Check user balance before entry fee processing
   - **Debug**: Use balance validation endpoint to verify current balance
   - **Prevention**: Implement real-time balance validation in UI

2. **"ENTRY_FEE_PROCESSING_FAILED" Error**
   - **Cause**: Payment processing failed due to network or service issues
   - **Solution**: Retry with exponential backoff
   - **Debug**: Check transaction logs and service health
   - **Prevention**: Implement retry logic with proper error handling

3. **"PRIZE_POOL_NOT_FOUND" Error**
   - **Cause**: Prize pool doesn't exist for the room
   - **Solution**: Ensure room exists and prize pool is initialized
   - **Debug**: Check room status and prize pool creation logs
   - **Prevention**: Validate room existence before prize pool operations

4. **Socket Connection Failures**
   - **Cause**: Invalid JWT token or network issues
   - **Solution**: Refresh token and retry connection
   - **Debug**: Verify token validity and socket gateway status
   - **Prevention**: Implement automatic token refresh and reconnection logic

5. **Real-time Update Delays**
   - **Cause**: Redis pub/sub issues or high server load
   - **Solution**: Check Redis connection and server performance
   - **Debug**: Monitor Redis logs and connection status
   - **Prevention**: Implement fallback polling for critical updates

6. **Prize Distribution Failures**
   - **Cause**: Concurrent access or insufficient funds in system
   - **Solution**: Retry distribution with proper locking
   - **Debug**: Check transaction logs and system balance
   - **Prevention**: Implement proper concurrency control

### Enhanced Debug Commands
```bash
# Check service health
curl http://localhost:3002/health
curl http://localhost:3001/health

# Check specific prize pool
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:3002/api/rooms/ROOM_ID/prize-pool

# Validate user balance
curl -X POST -H "Authorization: Bearer TOKEN" \
  -d '{"user_id":"USER_ID","bet_amount":10.00}' \
  http://localhost:3002/api/v1/entry_fees/validate_balance

# View enhanced logs
docker logs xzgame-manager-service --tail 100
docker logs xzgame-socket-gateway --tail 100
docker logs xzgame-game-service --tail 100

# Check Redis connection
redis-cli ping
redis-cli monitor

# Monitor real-time events
wscat -c ws://localhost:3001 -H "Authorization: Bearer TOKEN"
```

### Performance Monitoring
```bash
# Monitor API response times
curl -w "@curl-format.txt" -o /dev/null -s \
  http://localhost:3002/api/rooms/ROOM_ID/prize-pool

# Check database performance
mongo --eval "db.stats()"

# Monitor memory usage
docker stats xzgame-manager-service
docker stats xzgame-socket-gateway
```

### Error Code Reference
- `INSUFFICIENT_BALANCE`: User balance too low
- `ENTRY_FEE_PROCESSING_FAILED`: Payment processing error
- `PRIZE_POOL_NOT_FOUND`: Prize pool doesn't exist
- `ROOM_NOT_FOUND`: Room doesn't exist
- `ROOM_FULL`: Room at maximum capacity
- `GAME_IN_PROGRESS`: Game already started
- `UNAUTHORIZED`: Invalid or expired token
- `VALIDATION_ERROR`: Invalid request data
- `NETWORK_ERROR`: Connection or timeout issues

---

## 📞 Support & Resources

For technical support or questions about the enhanced Prize Wheel implementation:

1. **Documentation Review**:
   - Check the comprehensive API documentation
   - Review TypeScript interfaces and schemas
   - Consult integration examples and testing guides

2. **Testing & Debugging**:
   - Use provided curl examples for API testing
   - Test Socket.IO events with JavaScript examples
   - Run comprehensive test suite for validation
   - Use error codes and troubleshooting guide

3. **Development Resources**:
   - Custom React hooks for easy integration
   - TypeScript interfaces for type safety
   - Error handling utilities and patterns
   - Real-time event handling examples

4. **Performance & Monitoring**:
   - Health check endpoints for service monitoring
   - Performance monitoring commands
   - Log analysis and debugging tools
   - Real-time metrics and analytics

### Additional Resources
- **API Documentation**: Complete endpoint reference with examples
- **Socket Events**: Real-time event specifications and payloads
- **TypeScript Types**: Comprehensive interface definitions
- **Testing Guide**: Unit, integration, and end-to-end testing
- **Error Handling**: Specific error codes and recovery patterns

---

**Last Updated**: December 2024
**Version**: 2.0.0 (Enhanced Integration)
**Compatibility**: XZ Game Manager v2.0+ with Prize Wheel Backend Integration
**Client Features**: React hooks, TypeScript interfaces, comprehensive error handling
**Testing Coverage**: 70%+ with unit, integration, and visual regression tests
