# Prize Wheel Game - Response Schemas & TypeScript Interfaces

## Table of Contents
1. [Core Response Schemas](#core-response-schemas)
2. [Prize Pool Schemas](#prize-pool-schemas)
3. [Entry Fee & Transaction Schemas](#entry-fee--transaction-schemas)
4. [Socket Event Schemas](#socket-event-schemas)
5. [Enhanced Room Schemas](#enhanced-room-schemas)
6. [<PERSON>rror Handling Schemas](#error-handling-schemas)
7. [Validation Rules](#validation-rules)
8. [Integration Examples](#integration-examples)

---

## Core Response Schemas

### Standard API Response Format

All API responses follow this consistent structure:

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: Record<string, any>;
  };
  timestamp: string;
  request_id?: string;
}
```

### Paginated Response Format

```typescript
interface PaginatedResponse<T> {
  success: boolean;
  data: {
    items: T[];
    pagination: {
      page: number;
      per_page: number;
      total_count: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  };
  timestamp: string;
}
```

## Prize Pool Schemas

### Core Prize Pool Interface

```typescript
interface PrizePool {
  id: string;
  room_id: string;
  total_pool: number;
  entry_fee_per_player: number;
  contributing_players: string[];
  house_edge_percentage: number;
  house_edge_amount: number;
  net_prize_amount: number;
  status: 'accumulating' | 'locked' | 'distributed' | 'cancelled';
  game_type: 'prize_wheel';
  room_name: string;
  max_players: number;
  player_count: number;
  is_full: boolean;
  locked_at?: string;
  distributed_at?: string;
  metadata: Record<string, any>;
  created_at: string;
  updated_at: string;
}
```

### Potential Winnings Response

```typescript
interface PotentialWinningsResponse {
  potential_winnings: PotentialWinning[];
  calculated_at: string;
  room_id: string;
  total_pool: number;
}

interface PotentialWinning {
  user_id: string;
  potential_winnings: number;
  win_probability: number;
  current_pool_share: number;
}
```

### Prize Distribution Request

```typescript
interface PrizeDistributionRequest {
  room_id: string;
  game_results: {
    winner_user_id: string;
    winning_color: string;
    participants: Array<{
      user_id: string;
      selected_color: string;
      is_winner: boolean;
    }>;
    game_metadata: {
      spin_result: number;
      timestamp: string;
      [key: string]: any;
    };
  };
  distribution_metadata: {
    game_type: 'prize_wheel';
    room_name: string;
    [key: string]: any;
  };
}
```

### Prize Distribution Result

```typescript
interface PrizeDistributionResult {
  success: boolean;
  total_distributed: number;
  winner_prize: number;
  house_edge_taken: number;
  transactions: Transaction[];
  distribution_id: string;
  distributed_at: string;
}
```

## Entry Fee & Transaction Schemas

### Transaction Interface

```typescript
interface Transaction {
  id: string;
  user_id: string;
  type: 'entry_fee' | 'refund' | 'prize_win' | 'deposit' | 'withdrawal' | 'house_edge';
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string;
  metadata: {
    room_id?: string;
    room_name?: string;
    game_type?: string;
    prize_pool_id?: string;
    distribution_id?: string;
    processed_at?: string;
    [key: string]: any;
  };
  created_at: string;
  updated_at: string;
}
```

### Balance Validation Request

```typescript
interface BalanceValidationRequest {
  user_id: string;
  bet_amount: number;
}
```

### Balance Validation Response

```typescript
interface BalanceValidationResponse {
  success: boolean;
  has_sufficient_balance: boolean;
  current_balance: number;
  required_amount: number;
  shortfall?: number;
  error_message?: string;
  checked_at: string;
}
```

### Entry Fee Request

```typescript
interface EntryFeeRequest {
  user_id: string;
  room_id: string;
  bet_amount: number;
  metadata: {
    room_name?: string;
    game_type: 'prize_wheel';
    max_players?: number;
    timestamp: string;
    [key: string]: any;
  };
}
```

### Entry Fee Process Result

```typescript
interface EntryFeeProcessResult {
  success: boolean;
  transaction: Transaction;
  prize_pool: {
    room_id: string;
    total_pool: number;
    contributing_players: string[];
    player_count: number;
  };
  current_balance: number;
  error_message?: string;
}
```

### Refund Request

```typescript
interface RefundRequest {
  user_id: string;
  room_id: string;
  bet_amount: number;
  metadata: {
    reason: string;
    timestamp: string;
    [key: string]: any;
  };
}
```

### Entry Fee Refund Result

```typescript
interface EntryFeeRefundResult {
  success: boolean;
  transaction: Transaction;
  refunded_amount: number;
  current_balance: number;
  error_message?: string;
}
```

## Socket Event Schemas

### User Balance Updated Event

```typescript
interface UserBalanceUpdatedData {
  userId: string;
  previousBalance: number;
  newBalance: number;
  changeAmount: number;
  changeType: string;
  transactionId?: string;
  timestamp: string;
}
```

### User Transaction Completed Event

```typescript
interface UserTransactionCompletedData {
  userId: string;
  transaction: Transaction;
  newBalance: number;
  timestamp: string;
}
```

### Wheel Spinning Event

```typescript
interface WheelSpinningData {
  roomId: string;
  spinId: string;
  estimatedDuration: number;
  animationData?: {
    startAngle: number;
    endAngle: number;
    rotations: number;
  };
  timestamp: string;
}
```

### Wheel Result Event

```typescript
interface WheelResultData {
  roomId: string;
  spinId: string;
  result: {
    winning_color: string;
    winner: {
      userId: string;
      username: string;
      prizeAmount: number;
    };
    participants: Array<{
      userId: string;
      username: string;
      selectedColor: string;
      isWinner: boolean;
      prizeAmount?: number;
    }>;
    prize_amount: number;
    house_edge_amount: number;
  };
  timestamp: string;
}
```

### Game State Changed Event

```typescript
interface GameStateChangedData {
  roomId: string;
  previousState: string;
  newState: string;
  stateData?: Record<string, any>;
  timestamp: string;
}
```

### Enhanced Room Info Updated Event

```typescript
interface EnhancedRoomInfoUpdatedData {
  room: Room;
  players: Player[];
  prizePool?: PrizePool;
  gameState?: {
    status: string;
    countdown?: number;
    spinData?: any;
  };
  timestamp: string;
}
```

## Enhanced Room Schemas

### Room Interface

```typescript
interface Room {
  id: string;
  name: string;
  game_type: 'prize_wheel';
  status: 'waiting' | 'countdown' | 'spinning' | 'result';
  player_count: number;
  max_players: number;
  min_players: number;
  bet_amount: number;
  prize_pool: number;
  currency: string;
  is_private: boolean;
  creator: {
    id: string;
    username: string;
  };
  configuration: {
    game_duration: number;
    spin_duration: number;
    result_display_duration: number;
    wheel_sections: number;
    colors: string[];
  };
  created_at: string;
  updated_at: string;
}
```

### Enhanced Room Details Response

```typescript
interface EnhancedRoomDetailsResponse {
  room: Room;
  players: Player[];
  prize_pool?: PrizePool;
  game_state: {
    status: string;
    countdown?: number;
    current_round?: number;
    last_winner?: {
      userId: string;
      username: string;
      winningColor: string;
      prizeAmount: number;
    };
  };
  statistics: {
    total_games_played: number;
    total_prizes_distributed: number;
    average_prize_amount: number;
    house_edge_collected: number;
  };
}
```

### Player Interface

```typescript
interface Player {
  user_id: string;
  username: string;
  position: number;
  is_ready: boolean;
  bet_amount: number;
  selected_color?: string;
  balance: number;
  entry_fee_paid: boolean;
  session_count: number;
  status: 'pending' | 'active' | 'completed' | 'cancelled';
  joined_at: string;
  last_activity: string;
}
```

### Kick Player Request

```typescript
interface KickPlayerRequest {
  user_id: string;
  reason: string;
  notify_player: boolean;
  refund_entry_fee: boolean;
}
```

### Kick Player Response

```typescript
interface KickPlayerResponse {
  success: boolean;
  kicked_player: {
    user_id: string;
    username: string;
  };
  refund_processed: boolean;
  refund_amount?: number;
  refund_transaction_id?: string;
  message: string;
}
```

## Error Handling Schemas

### Prize Wheel Error Codes

```typescript
enum PrizeWheelErrorCode {
  // Balance & Payment Errors
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  BALANCE_VALIDATION_FAILED = 'BALANCE_VALIDATION_FAILED',
  ENTRY_FEE_PROCESSING_FAILED = 'ENTRY_FEE_PROCESSING_FAILED',
  REFUND_PROCESSING_FAILED = 'REFUND_PROCESSING_FAILED',
  PAYMENT_GATEWAY_ERROR = 'PAYMENT_GATEWAY_ERROR',

  // Prize Pool Errors
  PRIZE_POOL_NOT_FOUND = 'PRIZE_POOL_NOT_FOUND',
  PRIZE_POOL_LOCKED = 'PRIZE_POOL_LOCKED',
  PRIZE_POOL_ALREADY_DISTRIBUTED = 'PRIZE_POOL_ALREADY_DISTRIBUTED',
  PRIZE_DISTRIBUTION_FAILED = 'PRIZE_DISTRIBUTION_FAILED',

  // Room & Game Errors
  ROOM_NOT_FOUND = 'ROOM_NOT_FOUND',
  ROOM_FULL = 'ROOM_FULL',
  ROOM_CLOSED = 'ROOM_CLOSED',
  GAME_IN_PROGRESS = 'GAME_IN_PROGRESS',
  GAME_NOT_STARTED = 'GAME_NOT_STARTED',
  INVALID_GAME_STATE = 'INVALID_GAME_STATE',

  // Player Errors
  PLAYER_NOT_FOUND = 'PLAYER_NOT_FOUND',
  PLAYER_ALREADY_READY = 'PLAYER_ALREADY_READY',
  PLAYER_NOT_READY = 'PLAYER_NOT_READY',
  COLOR_ALREADY_SELECTED = 'COLOR_ALREADY_SELECTED',
  INVALID_COLOR_SELECTION = 'INVALID_COLOR_SELECTION',

  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',

  // System Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_TIMEOUT = 'API_TIMEOUT',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}
```

### Prize Wheel Error Interface

```typescript
interface PrizeWheelError {
  code: PrizeWheelErrorCode;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  context?: string;
}
```

### Error Response Examples

```typescript
// Insufficient Balance Error
interface InsufficientBalanceError extends PrizeWheelError {
  code: PrizeWheelErrorCode.INSUFFICIENT_BALANCE;
  details: {
    currentBalance: number;
    requiredAmount: number;
    shortfall: number;
    userId: string;
  };
}

// Prize Pool Not Found Error
interface PrizePoolNotFoundError extends PrizeWheelError {
  code: PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND;
  details: {
    roomId: string;
    prizePoolId?: string;
  };
}

// Room Full Error
interface RoomFullError extends PrizeWheelError {
  code: PrizeWheelErrorCode.ROOM_FULL;
  details: {
    roomId: string;
    currentPlayers: number;
    maxPlayers: number;
  };
}
```

---

## Validation Rules

### Prize Pool Validation

```typescript
const prizePoolValidation = {
  room_id: {
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    pattern: '^[a-zA-Z0-9_-]+$'
  },
  entry_fee_per_player: {
    required: true,
    type: 'number',
    minimum: 0.01,
    maximum: 10000.00,
    precision: 2
  },
  house_edge_percentage: {
    required: false,
    type: 'number',
    minimum: 0.0,
    maximum: 50.0,
    default: 5.0
  },
  max_players: {
    required: false,
    type: 'integer',
    minimum: 2,
    maximum: 16,
    default: 8
  },
  status: {
    required: false,
    type: 'string',
    enum: ['accumulating', 'locked', 'distributed', 'cancelled'],
    default: 'accumulating'
  },
  game_type: {
    required: true,
    type: 'string',
    enum: ['prize_wheel']
  }
};
```

### Entry Fee Validation

```typescript
const entryFeeValidation = {
  user_id: {
    required: true,
    type: 'string',
    pattern: '^[a-zA-Z0-9_-]+$',
    minLength: 1,
    maxLength: 50
  },
  room_id: {
    required: true,
    type: 'string',
    pattern: '^[a-zA-Z0-9_-]+$',
    minLength: 1,
    maxLength: 100
  },
  bet_amount: {
    required: true,
    type: 'number',
    minimum: 0.01,
    maximum: 10000.00,
    precision: 2
  },
  metadata: {
    required: true,
    type: 'object',
    properties: {
      room_name: { type: 'string', maxLength: 200 },
      game_type: { type: 'string', enum: ['prize_wheel'], required: true },
      max_players: { type: 'integer', minimum: 2, maximum: 16 },
      timestamp: { type: 'string', format: 'date-time', required: true }
    }
  }
};
```

### Color Selection Validation

```typescript
const colorValidation = {
  color: {
    required: true,
    type: 'string',
    enum: ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan']
  },
  room_id: {
    required: true,
    type: 'string',
    pattern: '^[a-zA-Z0-9_-]+$',
    minLength: 1,
    maxLength: 100
  },
  user_id: {
    required: true,
    type: 'string',
    pattern: '^[a-zA-Z0-9_-]+$',
    minLength: 1,
    maxLength: 50
  }
};
```

---

## Integration Examples

### TypeScript Client Implementation

```typescript
import {
  PrizePool,
  EntryFeeRequest,
  BalanceValidationRequest,
  PrizeWheelError,
  PrizeWheelErrorCode
} from './types';

class PrizeWheelApiClient {
  private baseUrl: string;
  private authToken: string;

  constructor(baseUrl: string, authToken: string) {
    this.baseUrl = baseUrl;
    this.authToken = authToken;
  }

  // Prize Pool Management
  async getRoomPrizePool(roomId: string): Promise<PrizePool> {
    const response = await this.makeRequest<PrizePool>(
      'GET',
      `/api/rooms/${roomId}/prize-pool`
    );
    return response.data!;
  }

  async getPotentialWinnings(roomId: string): Promise<PotentialWinningsResponse> {
    const response = await this.makeRequest<PotentialWinningsResponse>(
      'GET',
      `/api/rooms/${roomId}/potential-winnings`
    );
    return response.data!;
  }

  // Balance & Entry Fee Processing
  async validateBalance(request: BalanceValidationRequest): Promise<BalanceValidationResponse> {
    const response = await this.makeRequest<BalanceValidationResponse>(
      'POST',
      '/api/v1/entry_fees/validate_balance',
      request
    );
    return response.data!;
  }

  async processEntryFee(request: EntryFeeRequest): Promise<EntryFeeProcessResult> {
    const response = await this.makeRequest<EntryFeeProcessResult>(
      'POST',
      '/api/v1/entry_fees/process_entry_fee',
      request
    );
    return response.data!;
  }

  async processRefund(request: RefundRequest): Promise<EntryFeeRefundResult> {
    const response = await this.makeRequest<EntryFeeRefundResult>(
      'POST',
      '/api/v1/entry_fees/process_refund',
      request
    );
    return response.data!;
  }

  // Enhanced Room Management
  async getEnhancedRoomDetails(roomId: string): Promise<EnhancedRoomDetailsResponse> {
    const response = await this.makeRequest<EnhancedRoomDetailsResponse>(
      'GET',
      `/admin/rooms/${roomId}`
    );
    return response.data!;
  }

  async kickPlayer(roomId: string, request: KickPlayerRequest): Promise<KickPlayerResponse> {
    const response = await this.makeRequest<KickPlayerResponse>(
      'POST',
      `/admin/rooms/${roomId}/kick-player`,
      request
    );
    return response.data!;
  }

  // Prize Distribution
  async distributePrizes(request: PrizeDistributionRequest): Promise<PrizeDistributionResult> {
    const response = await this.makeRequest<PrizeDistributionResult>(
      'POST',
      `/api/rooms/${request.room_id}/distribute-prizes`,
      request
    );
    return response.data!;
  }

  private async makeRequest<T>(
    method: string,
    endpoint: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseUrl}${endpoint}`;
    const options: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.authToken}`,
      },
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);
      const result = await response.json();

      if (!response.ok) {
        throw this.createPrizeWheelError(result);
      }

      return result;
    } catch (error) {
      if (error instanceof Error && error.name === 'PrizeWheelError') {
        throw error;
      }
      throw this.createPrizeWheelError({
        error: {
          code: 'NETWORK_ERROR',
          message: error instanceof Error ? error.message : 'Network request failed'
        }
      });
    }
  }

  private createPrizeWheelError(response: any): PrizeWheelError {
    const error = response.error || {};
    return {
      code: error.code || PrizeWheelErrorCode.UNKNOWN_ERROR,
      message: error.message || 'An unexpected error occurred',
      details: error.details,
      timestamp: new Date().toISOString(),
      context: 'api_client'
    };
  }
}
```

### React Hook Integration Example

```typescript
import { useState, useEffect } from 'react';
import { PrizeWheelApiClient } from './api-client';
import { PrizePool, PrizeWheelError } from './types';

export const usePrizePoolData = (roomId: string) => {
  const [prizePool, setPrizePool] = useState<PrizePool | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<PrizeWheelError | null>(null);

  const apiClient = new PrizeWheelApiClient(
    process.env.REACT_APP_API_BASE_URL!,
    localStorage.getItem('authToken')!
  );

  const loadPrizePool = async () => {
    setLoading(true);
    setError(null);

    try {
      const data = await apiClient.getRoomPrizePool(roomId);
      setPrizePool(data);
    } catch (err) {
      setError(err as PrizeWheelError);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (roomId) {
      loadPrizePool();
    }
  }, [roomId]);

  return {
    prizePool,
    loading,
    error,
    refetch: loadPrizePool,
  };
};
```

This comprehensive schema documentation provides all the TypeScript interfaces and integration patterns needed for the Prize Wheel backend integration.

### Success Codes

| Code | Description | Usage |
|------|-------------|-------|
| `200` | OK | Successful GET, PUT, DELETE requests |
| `201` | Created | Successful POST requests that create resources |
| `202` | Accepted | Asynchronous operations accepted |
| `204` | No Content | Successful DELETE with no response body |

### Client Error Codes

| Code | Description | Common Scenarios |
|------|-------------|------------------|
| `400` | Bad Request | Invalid request parameters, malformed JSON |
| `401` | Unauthorized | Missing or invalid authentication token |
| `403` | Forbidden | Insufficient permissions for operation |
| `404` | Not Found | Resource doesn't exist (room, user, prize pool) |
| `409` | Conflict | Resource already exists, state conflict |
| `422` | Unprocessable Entity | Validation errors, business logic violations |
| `429` | Too Many Requests | Rate limiting exceeded |

### Server Error Codes

| Code | Description | Common Scenarios |
|------|-------------|------------------|
| `500` | Internal Server Error | Unexpected server errors |
| `502` | Bad Gateway | Service communication failures |
| `503` | Service Unavailable | Service temporarily down |
| `504` | Gateway Timeout | Service response timeout |

### Error Response Examples

```json
// 400 Bad Request
{
  "success": false,
  "error": {
    "code": "INVALID_REQUEST",
    "message": "Invalid request parameters",
    "details": {
      "bet_amount": ["must be a positive number"],
      "room_id": ["is required"]
    }
  },
  "timestamp": "2024-01-15T12:00:00Z"
}

// 422 Unprocessable Entity
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_BALANCE",
    "message": "User has insufficient balance for entry fee",
    "details": {
      "required": 100.00,
      "available": 50.00,
      "shortfall": 50.00,
      "user_id": "user123"
    }
  },
  "timestamp": "2024-01-15T12:00:00Z"
}

// 404 Not Found
{
  "success": false,
  "error": {
    "code": "PRIZE_POOL_NOT_FOUND",
    "message": "Prize pool not found for room",
    "details": {
      "room_id": "room_123"
    }
  },
  "timestamp": "2024-01-15T12:00:00Z"
}
```

---

## Integration Guide

### Step 1: Environment Setup

#### 1.1 Service Configuration

Create environment configuration for all services:

```bash
# Manager Service (.env)
PORT=3002
MONGODB_URI=*******************************************************************
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-jwt-secret
HOUSE_EDGE_PERCENTAGE=5.0

# Socket Gateway (.env)
PORT=3001
REDIS_URL=redis://localhost:6379
MANAGER_SERVICE_URL=http://localhost:3002
GAME_SERVICE_URL=http://localhost:3003

# Dashboard Service (.env)
NEXT_PUBLIC_API_BASE_URL=http://localhost:3002
NEXT_PUBLIC_SOCKET_URL=ws://localhost:3001
```

#### 1.2 Service Startup Sequence

```bash
# 1. Start infrastructure
docker-compose up -d mongodb redis

# 2. Start core services
cd services/manager-service && npm start &
cd services/socket-gateway && npm start &

# 3. Start game services
cd services/game-service && npm start &
cd services/room-service && npm start &

# 4. Start dashboard
cd services/dashboard-service && npm run dev
```

### Step 2: Authentication Setup

#### 2.1 Obtain Authentication Token

```javascript
// Login to get authentication token
const loginResponse = await fetch('http://localhost:3002/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'your-username',
    password: 'your-password'
  })
});

const { token } = await loginResponse.json();
```

#### 2.2 Configure API Client

```javascript
// Set up API client with authentication
const apiClient = axios.create({
  baseURL: 'http://localhost:3002',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});
```

### Step 3: Prize Wheel Game Implementation

#### 3.1 Basic Game Flow Implementation

```javascript
class PrizeWheelGameManager {
  constructor(config) {
    this.apiClient = config.apiClient;
    this.socket = config.socket;
    this.roomId = config.roomId;
    this.userId = config.userId;
    
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    // Room updates
    this.socket.on('room_info_updated', (data) => {
      this.handleRoomUpdate(data);
    });

    // Game state changes
    this.socket.on('game_state_changed', (data) => {
      this.handleGameStateChange(data);
    });

    // Game results
    this.socket.on('wheel_result', (data) => {
      this.handleGameResult(data);
    });

    // Balance updates
    this.socket.on('user_balance_updated', (data) => {
      if (data.userId === this.userId) {
        this.handleBalanceUpdate(data);
      }
    });
  }

  async joinRoom() {
    return new Promise((resolve, reject) => {
      this.socket.emit('join_room', {
        roomId: this.roomId
      }, (response) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }

  async selectColor(color) {
    return new Promise((resolve, reject) => {
      this.socket.emit('select_wheel_color', {
        roomId: this.roomId,
        color: color
      }, (response) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }

  async setReady() {
    // First validate balance
    const validation = await this.validateBalance();
    if (!validation.has_sufficient_balance) {
      throw new Error(`Insufficient balance: ${validation.error_message}`);
    }

    return new Promise((resolve, reject) => {
      this.socket.emit('player_ready', {
        roomId: this.roomId
      }, (response) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }

  async validateBalance() {
    try {
      const response = await this.apiClient.post('/api/v1/entry_fees/validate_balance', {
        user_id: this.userId,
        bet_amount: this.betAmount
      });
      return response.data.data;
    } catch (error) {
      console.error('Balance validation failed:', error);
      return {
        success: false,
        has_sufficient_balance: false,
        error_message: 'Validation failed'
      };
    }
  }

  handleRoomUpdate(data) {
    console.log('Room updated:', data);
    // Update UI with new room state
    this.onRoomUpdate?.(data);
  }

  handleGameStateChange(data) {
    console.log('Game state changed:', data);
    // Update UI based on game state
    this.onGameStateChange?.(data);
  }

  handleGameResult(data) {
    console.log('Game result:', data);
    // Show game result to user
    this.onGameResult?.(data);
  }

  handleBalanceUpdate(data) {
    console.log('Balance updated:', data);
    // Update user balance display
    this.onBalanceUpdate?.(data);
  }
}

// Usage
const gameManager = new PrizeWheelGameManager({
  apiClient: apiClient,
  socket: socket,
  roomId: 'room_123',
  userId: 'user_456'
});

// Set up event handlers
gameManager.onRoomUpdate = (data) => {
  // Update room UI
};

gameManager.onGameResult = (data) => {
  // Show win/loss result
};

// Start playing
await gameManager.joinRoom();
await gameManager.selectColor('red');
await gameManager.setReady();
```

### Step 4: Testing and Debugging

#### 4.1 API Testing with curl

```bash
# Test authentication
curl -X POST http://localhost:3002/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"password123"}'

# Test balance validation
curl -X POST http://localhost:3002/api/v1/entry_fees/validate_balance \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"user_id":"user123","bet_amount":100.00}'

# Test prize pool creation
curl -X POST http://localhost:3002/api/v1/entry_fees/process_ready_fee \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id":"user123",
    "room_id":"room_456",
    "bet_amount":100.00,
    "metadata":{"game_type":"prize_wheel","room_name":"Test Room"}
  }'

# Test prize pool retrieval
curl -X GET http://localhost:3002/api/rooms/room_456/prize-pool \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test prize distribution
curl -X POST http://localhost:3002/api/rooms/room_456/distribute-prizes \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "game_results":{
      "winning_color":"red",
      "winner_user_id":"user123",
      "participants":[{"user_id":"user123","selected_color":"red","is_winner":true}]
    }
  }'
```

#### 4.2 Socket.IO Testing

```javascript
// Test Socket.IO connection and events
const io = require('socket.io-client');

const socket = io('ws://localhost:3001', {
  query: { token: 'YOUR_JWT_TOKEN' }
});

socket.on('connect', () => {
  console.log('Connected to socket server');

  // Test room join
  socket.emit('join_room', { roomId: 'room_123' }, (response) => {
    console.log('Join room response:', response);
  });

  // Test color selection
  socket.emit('select_wheel_color', {
    roomId: 'room_123',
    color: 'red'
  }, (response) => {
    console.log('Color selection response:', response);
  });

  // Test ready state
  socket.emit('player_ready', { roomId: 'room_123' }, (response) => {
    console.log('Ready response:', response);
  });
});

// Listen for events
socket.on('room_info_updated', (data) => {
  console.log('Room info updated:', data);
});

socket.on('game_state_changed', (data) => {
  console.log('Game state changed:', data);
});

socket.on('wheel_result', (data) => {
  console.log('Wheel result:', data);
});

socket.on('user_balance_updated', (data) => {
  console.log('Balance updated:', data);
});
```

#### 4.3 Common Issues and Solutions

**Issue: "INSUFFICIENT_BALANCE" Error**
```javascript
// Solution: Check user balance before attempting entry fee
const validation = await apiClient.post('/api/v1/entry_fees/validate_balance', {
  user_id: userId,
  bet_amount: betAmount
});

if (!validation.data.data.has_sufficient_balance) {
  // Handle insufficient balance
  console.log('Shortfall:', validation.data.data.shortfall);
}
```

**Issue: "ROOM_FULL" Error**
```javascript
// Solution: Check room capacity before joining
const roomInfo = await apiClient.get(`/admin/rooms/${roomId}`);
if (roomInfo.data.data.room.current_players >= roomInfo.data.data.room.max_players) {
  // Handle room full scenario
}
```

**Issue: Socket Connection Failures**
```javascript
// Solution: Implement reconnection logic
socket.on('disconnect', () => {
  console.log('Disconnected from server');
  setTimeout(() => {
    socket.connect();
  }, 5000);
});

socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
  // Check token validity
});
```

#### 4.4 Monitoring and Logging

```javascript
// Enable debug logging for Socket.IO
localStorage.debug = 'socket.io-client:socket';

// API request logging
apiClient.interceptors.request.use(request => {
  console.log('API Request:', request.method?.toUpperCase(), request.url, request.data);
  return request;
});

apiClient.interceptors.response.use(
  response => {
    console.log('API Response:', response.status, response.data);
    return response;
  },
  error => {
    console.error('API Error:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);
```

#### 4.5 Performance Testing

```javascript
// Load testing example
const loadTest = async () => {
  const promises = [];
  const userCount = 10;

  for (let i = 0; i < userCount; i++) {
    promises.push(simulateUser(`user_${i}`));
  }

  await Promise.all(promises);
};

const simulateUser = async (userId) => {
  try {
    // Validate balance
    const validation = await apiClient.post('/api/v1/entry_fees/validate_balance', {
      user_id: userId,
      bet_amount: 100
    });

    if (validation.data.data.has_sufficient_balance) {
      // Process entry fee
      await apiClient.post('/api/v1/entry_fees/process_ready_fee', {
        user_id: userId,
        room_id: 'load_test_room',
        bet_amount: 100,
        metadata: { game_type: 'prize_wheel' }
      });

      console.log(`User ${userId} successfully joined`);
    }
  } catch (error) {
    console.error(`User ${userId} failed:`, error.response?.data);
  }
};
```

---

## Troubleshooting Guide

### Common Error Scenarios

1. **Authentication Issues**
   - Verify JWT token is valid and not expired
   - Check token format: `Bearer <token>`
   - Ensure user has required permissions

2. **Balance Validation Failures**
   - Check user exists in system
   - Verify user has sufficient balance
   - Ensure bet amount is within valid range

3. **Prize Pool Issues**
   - Verify room exists before creating prize pool
   - Check room capacity limits
   - Ensure game type is supported

4. **Socket Connection Problems**
   - Verify socket gateway is running
   - Check network connectivity
   - Validate authentication token in query params

5. **Game State Synchronization**
   - Ensure all services are running
   - Check Redis connectivity
   - Verify event publishing/subscription

### Debug Commands

```bash
# Check service health
curl http://localhost:3002/health
curl http://localhost:3001/health

# Check Redis connectivity
redis-cli ping

# Check MongoDB connectivity
mongosh *******************************************************************

# View service logs
docker logs xzgame-manager-service
docker logs xzgame-socket-gateway
```

This comprehensive documentation provides everything needed to integrate with the Prize Wheel game system, including schemas, validation rules, implementation examples, and thorough testing procedures.
