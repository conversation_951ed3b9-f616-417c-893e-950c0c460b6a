# Prize Wheel Deployment Guide

## Overview

This comprehensive deployment guide covers the deployment process for the Prize Wheel client application with backend integration. It includes development setup, staging deployment, production deployment, and monitoring configurations.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Development Deployment](#development-deployment)
4. [Staging Deployment](#staging-deployment)
5. [Production Deployment](#production-deployment)
6. [Docker Deployment](#docker-deployment)
7. [CI/CD Pipeline](#cicd-pipeline)
8. [Monitoring and Health Checks](#monitoring-and-health-checks)
9. [Troubleshooting](#troubleshooting)

---

## Prerequisites

### System Requirements

#### Development Environment
- **Node.js**: v18.0.0 or higher
- **npm**: v8.0.0 or higher
- **Git**: Latest version
- **Docker**: v20.0.0 or higher (optional)

#### Production Environment
- **Server**: Linux (Ubuntu 20.04+ recommended)
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 20GB minimum, 50GB recommended
- **Network**: Stable internet connection with SSL support

### Backend Services Required

The Prize Wheel client requires the following backend services to be running:

| Service | Port | Purpose |
|---------|------|---------|
| Manager Service | 3002 | Prize pools, entry fees, transactions |
| Socket Gateway | 3001 | Real-time events, WebSocket connections |
| Game Service | 3003 | Game logic, wheel spinning |
| Room Service | 3004 | Room management, player states |

---

## Environment Configuration

### Environment Variables

#### Development (.env.local)
```bash
# API Configuration
REACT_APP_API_BASE_URL=http://localhost:3002
REACT_APP_SOCKET_URL=http://localhost:3001
REACT_APP_GAME_SERVICE_URL=http://localhost:3003
REACT_APP_ROOM_SERVICE_URL=http://localhost:3004

# Authentication
REACT_APP_JWT_SECRET=dev-jwt-secret-key
REACT_APP_AUTH_ENDPOINT=/api/auth

# Prize Wheel Configuration
REACT_APP_DEFAULT_HOUSE_EDGE=5.0
REACT_APP_MAX_PLAYERS_PER_ROOM=8
REACT_APP_MIN_ENTRY_FEE=0.01
REACT_APP_MAX_ENTRY_FEE=1000.00

# Feature Flags
REACT_APP_ENABLE_PRIZE_POOL_VISUALIZATION=true
REACT_APP_ENABLE_REAL_TIME_BALANCE=true
REACT_APP_ENABLE_ENHANCED_ERROR_HANDLING=true
REACT_APP_ENABLE_ADMIN_DASHBOARD=true

# Debug Settings
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=debug
REACT_APP_ENABLE_REDUX_DEVTOOLS=true

# Performance
REACT_APP_API_TIMEOUT=10000
REACT_APP_SOCKET_TIMEOUT=5000
REACT_APP_RETRY_ATTEMPTS=3
```

#### Staging (.env.staging)
```bash
# API Configuration
REACT_APP_API_BASE_URL=https://api-staging.yourdomain.com
REACT_APP_SOCKET_URL=https://socket-staging.yourdomain.com
REACT_APP_GAME_SERVICE_URL=https://game-staging.yourdomain.com
REACT_APP_ROOM_SERVICE_URL=https://rooms-staging.yourdomain.com

# Authentication
REACT_APP_JWT_SECRET=staging-jwt-secret-key
REACT_APP_AUTH_ENDPOINT=/api/auth

# Prize Wheel Configuration
REACT_APP_DEFAULT_HOUSE_EDGE=5.0
REACT_APP_MAX_PLAYERS_PER_ROOM=8
REACT_APP_MIN_ENTRY_FEE=0.01
REACT_APP_MAX_ENTRY_FEE=1000.00

# Feature Flags
REACT_APP_ENABLE_PRIZE_POOL_VISUALIZATION=true
REACT_APP_ENABLE_REAL_TIME_BALANCE=true
REACT_APP_ENABLE_ENHANCED_ERROR_HANDLING=true
REACT_APP_ENABLE_ADMIN_DASHBOARD=true

# Debug Settings
REACT_APP_DEBUG=false
REACT_APP_LOG_LEVEL=info
REACT_APP_ENABLE_REDUX_DEVTOOLS=false

# Security
REACT_APP_ENABLE_SSL=true
REACT_APP_CORS_ORIGIN=https://staging.yourdomain.com

# Performance
REACT_APP_API_TIMEOUT=15000
REACT_APP_SOCKET_TIMEOUT=8000
REACT_APP_RETRY_ATTEMPTS=3

# Monitoring
REACT_APP_SENTRY_DSN=your-staging-sentry-dsn
REACT_APP_ANALYTICS_ID=your-staging-analytics-id
```

#### Production (.env.production)
```bash
# API Configuration
REACT_APP_API_BASE_URL=https://api.yourdomain.com
REACT_APP_SOCKET_URL=https://socket.yourdomain.com
REACT_APP_GAME_SERVICE_URL=https://game.yourdomain.com
REACT_APP_ROOM_SERVICE_URL=https://rooms.yourdomain.com

# Authentication
REACT_APP_JWT_SECRET=production-jwt-secret-key
REACT_APP_AUTH_ENDPOINT=/api/auth

# Prize Wheel Configuration
REACT_APP_DEFAULT_HOUSE_EDGE=5.0
REACT_APP_MAX_PLAYERS_PER_ROOM=8
REACT_APP_MIN_ENTRY_FEE=0.01
REACT_APP_MAX_ENTRY_FEE=1000.00

# Feature Flags
REACT_APP_ENABLE_PRIZE_POOL_VISUALIZATION=true
REACT_APP_ENABLE_REAL_TIME_BALANCE=true
REACT_APP_ENABLE_ENHANCED_ERROR_HANDLING=true
REACT_APP_ENABLE_ADMIN_DASHBOARD=true

# Debug Settings
REACT_APP_DEBUG=false
REACT_APP_LOG_LEVEL=error
REACT_APP_ENABLE_REDUX_DEVTOOLS=false

# Security
REACT_APP_ENABLE_SSL=true
REACT_APP_CORS_ORIGIN=https://yourdomain.com
REACT_APP_CSP_ENABLED=true

# Performance
REACT_APP_API_TIMEOUT=20000
REACT_APP_SOCKET_TIMEOUT=10000
REACT_APP_RETRY_ATTEMPTS=5

# Monitoring
REACT_APP_SENTRY_DSN=your-production-sentry-dsn
REACT_APP_ANALYTICS_ID=your-production-analytics-id
REACT_APP_ERROR_REPORTING_ENABLED=true

# CDN Configuration
REACT_APP_CDN_URL=https://cdn.yourdomain.com
REACT_APP_STATIC_ASSETS_URL=https://assets.yourdomain.com
```

---

## Development Deployment

### Local Development Setup

```bash
# Clone the repository
git clone https://github.com/yourusername/prize-wheel-client.git
cd prize-wheel-client

# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env.local

# Edit environment variables
nano .env.local

# Start development server
npm run dev

# Run tests
npm test

# Run with coverage
npm run test:coverage

# Build for production testing
npm run build
npm run preview
```

### Development Scripts

```json
{
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "test:e2e": "playwright test",
    "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext ts,tsx --fix",
    "type-check": "tsc --noEmit",
    "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"",
    "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html"
  }
}
```

### Development Health Checks

```bash
# Check all services are running
curl http://localhost:3002/health  # Manager Service
curl http://localhost:3001/health  # Socket Gateway
curl http://localhost:3003/health  # Game Service
curl http://localhost:3004/health  # Room Service

# Test API connectivity
curl -X POST http://localhost:3002/api/v1/entry_fees/validate_balance \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"user_id":"test","bet_amount":10.00}'

# Test Socket.IO connectivity
wscat -c ws://localhost:3001 -H "Authorization: Bearer YOUR_TOKEN"
```

---

## Staging Deployment

### Staging Environment Setup

```bash
# Server setup (Ubuntu 20.04+)
sudo apt update
sudo apt install -y nginx nodejs npm git certbot python3-certbot-nginx

# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Clone and setup application
git clone https://github.com/yourusername/prize-wheel-client.git
cd prize-wheel-client

# Install dependencies
npm ci --production

# Copy staging environment
cp .env.staging .env.local

# Build application
npm run build

# Setup nginx configuration
sudo cp deployment/nginx/staging.conf /etc/nginx/sites-available/prize-wheel-staging
sudo ln -s /etc/nginx/sites-available/prize-wheel-staging /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# Setup SSL certificate
sudo certbot --nginx -d staging.yourdomain.com

# Setup PM2 for process management
npm install -g pm2
pm2 start ecosystem.config.js --env staging
pm2 save
pm2 startup
```

### Nginx Configuration (staging.conf)

```nginx
server {
    listen 80;
    server_name staging.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name staging.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/staging.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/staging.yourdomain.com/privkey.pem;

    root /var/www/prize-wheel-client/dist;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API proxy
    location /api/ {
        proxy_pass https://api-staging.yourdomain.com;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Socket.IO proxy
    location /socket.io/ {
        proxy_pass https://socket-staging.yourdomain.com;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # React Router
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### PM2 Configuration (ecosystem.config.js)

```javascript
module.exports = {
  apps: [{
    name: 'prize-wheel-client-staging',
    script: 'serve',
    args: '-s dist -l 3000',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'staging',
      PORT: 3000
    },
    env_staging: {
      NODE_ENV: 'staging',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
};
```

---

## Production Deployment

### Production Server Setup

```bash
# Server preparation (Ubuntu 20.04+)
sudo apt update && sudo apt upgrade -y
sudo apt install -y nginx nodejs npm git ufw fail2ban

# Configure firewall
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# Install Node.js 18+ LTS
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# Create application user
sudo adduser --system --group --home /var/www/prize-wheel prize-wheel
sudo usermod -aG www-data prize-wheel

# Setup application directory
sudo mkdir -p /var/www/prize-wheel-client
sudo chown prize-wheel:prize-wheel /var/www/prize-wheel-client
```

### Production Build and Deployment

```bash
# Switch to application user
sudo su - prize-wheel

# Clone repository
cd /var/www
git clone https://github.com/yourusername/prize-wheel-client.git
cd prize-wheel-client

# Install dependencies
npm ci --production

# Copy production environment
cp .env.production .env.local

# Build optimized production bundle
NODE_ENV=production npm run build

# Verify build
ls -la dist/
npm run preview &
curl http://localhost:4173
pkill -f preview

# Install PM2 globally
sudo npm install -g pm2

# Start application with PM2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup

# Setup log rotation
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

### Production Nginx Configuration

```nginx
# /etc/nginx/sites-available/prize-wheel-production
upstream prize_wheel_app {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    keepalive 32;
}

# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://yourdomain.com$request_uri;
}

server {
    listen 443 ssl http2;
    server_name www.yourdomain.com;
    return 301 https://yourdomain.com$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' wss: https:; font-src 'self' data:;" always;

    root /var/www/prize-wheel-client/dist;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        application/xml
        image/svg+xml;

    # Static assets with long cache
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # API endpoints with rate limiting
    location /api/ {
        limit_req zone=api burst=20 nodelay;

        proxy_pass https://api.yourdomain.com;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 60s;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
    }

    # Login endpoint with stricter rate limiting
    location /api/auth/login {
        limit_req zone=login burst=5 nodelay;

        proxy_pass https://api.yourdomain.com;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Socket.IO with WebSocket support
    location /socket.io/ {
        proxy_pass https://socket.yourdomain.com;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 86400;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # React Router fallback
    location / {
        try_files $uri $uri/ /index.html;

        # Cache HTML files for short time
        location ~* \.html$ {
            expires 5m;
            add_header Cache-Control "public, no-cache";
        }
    }

    # Security: Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|config|log)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
```

### SSL Certificate Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Setup automatic renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet

# Test renewal
sudo certbot renew --dry-run
```

---

## Docker Deployment

### Dockerfile

```dockerfile
# Multi-stage build for production
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY deployment/nginx/docker.conf /etc/nginx/conf.d/default.conf

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose Configuration

```yaml
# docker-compose.yml
version: '3.8'

services:
  prize-wheel-client:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./logs:/var/log/nginx
    restart: unless-stopped
    depends_on:
      - manager-service
      - socket-gateway
    networks:
      - prize-wheel-network

  manager-service:
    image: prize-wheel/manager-service:latest
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
    restart: unless-stopped
    networks:
      - prize-wheel-network

  socket-gateway:
    image: prize-wheel/socket-gateway:latest
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - REDIS_URL=${REDIS_URL}
    restart: unless-stopped
    networks:
      - prize-wheel-network

networks:
  prize-wheel-network:
    driver: bridge

volumes:
  ssl-certs:
  nginx-logs:
```

### Docker Deployment Commands

```bash
# Build and deploy
docker-compose build
docker-compose up -d

# View logs
docker-compose logs -f prize-wheel-client

# Update deployment
docker-compose pull
docker-compose up -d --force-recreate

# Health checks
docker-compose ps
curl http://localhost/health

# Cleanup
docker-compose down
docker system prune -f
```

---

## CI/CD Pipeline

### GitHub Actions Workflow

```yaml
# .github/workflows/deploy.yml
name: Deploy Prize Wheel Client

on:
  push:
    branches: [main, staging]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run type check
        run: npm run type-check

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm run test:coverage

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/staging'

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/staging'

    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: dist
          path: dist/

      - name: Deploy to staging
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.STAGING_HOST }}
          username: ${{ secrets.STAGING_USER }}
          key: ${{ secrets.STAGING_SSH_KEY }}
          script: |
            cd /var/www/prize-wheel-client
            git pull origin staging
            npm ci --production
            npm run build
            pm2 reload ecosystem.config.js --env staging

  deploy-production:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: dist
          path: dist/

      - name: Deploy to production
        uses: appleboy/ssh-action@v0.1.5
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            cd /var/www/prize-wheel-client
            git pull origin main
            npm ci --production
            npm run build
            pm2 reload ecosystem.config.js --env production

      - name: Health check
        run: |
          sleep 30
          curl -f https://yourdomain.com/health || exit 1
```

---

## Monitoring and Health Checks

### Application Monitoring

```typescript
// src/utils/monitoring.ts
export class ApplicationMonitoring {
  private static instance: ApplicationMonitoring;

  static getInstance(): ApplicationMonitoring {
    if (!this.instance) {
      this.instance = new ApplicationMonitoring();
    }
    return this.instance;
  }

  // Performance monitoring
  trackPageLoad(pageName: string): void {
    const loadTime = performance.now();

    if (process.env.NODE_ENV === 'production') {
      // Send to analytics service
      // analytics.track('page_load', { page: pageName, loadTime });
    }
  }

  // Error monitoring
  trackError(error: Error, context?: string): void {
    if (process.env.NODE_ENV === 'production') {
      // Send to error reporting service
      // Sentry.captureException(error, { tags: { context } });
    }
  }

  // User interaction monitoring
  trackUserAction(action: string, data?: any): void {
    if (process.env.NODE_ENV === 'production') {
      // Send to analytics service
      // analytics.track(action, data);
    }
  }

  // API performance monitoring
  trackApiCall(endpoint: string, duration: number, status: number): void {
    if (process.env.NODE_ENV === 'production') {
      // Send to monitoring service
      // monitoring.recordApiCall(endpoint, duration, status);
    }
  }
}
```

### Health Check Endpoints

```typescript
// Health check implementation
export const healthCheck = {
  // Basic health check
  basic: async (): Promise<{ status: string; timestamp: string }> => {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
    };
  },

  // Detailed health check
  detailed: async (): Promise<{
    status: string;
    timestamp: string;
    services: Record<string, any>;
    version: string;
  }> => {
    const services = await Promise.allSettled([
      checkApiService(),
      checkSocketService(),
      checkGameService(),
      checkRoomService(),
    ]);

    return {
      status: services.every(s => s.status === 'fulfilled') ? 'healthy' : 'degraded',
      timestamp: new Date().toISOString(),
      services: {
        api: services[0].status === 'fulfilled' ? 'healthy' : 'unhealthy',
        socket: services[1].status === 'fulfilled' ? 'healthy' : 'unhealthy',
        game: services[2].status === 'fulfilled' ? 'healthy' : 'unhealthy',
        room: services[3].status === 'fulfilled' ? 'healthy' : 'unhealthy',
      },
      version: process.env.REACT_APP_VERSION || '1.0.0',
    };
  },
};

const checkApiService = async (): Promise<void> => {
  const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}/health`);
  if (!response.ok) throw new Error('API service unhealthy');
};

const checkSocketService = async (): Promise<void> => {
  const response = await fetch(`${process.env.REACT_APP_SOCKET_URL}/health`);
  if (!response.ok) throw new Error('Socket service unhealthy');
};

const checkGameService = async (): Promise<void> => {
  const response = await fetch(`${process.env.REACT_APP_GAME_SERVICE_URL}/health`);
  if (!response.ok) throw new Error('Game service unhealthy');
};

const checkRoomService = async (): Promise<void> => {
  const response = await fetch(`${process.env.REACT_APP_ROOM_SERVICE_URL}/health`);
  if (!response.ok) throw new Error('Room service unhealthy');
};
```

### Monitoring Scripts

```bash
#!/bin/bash
# monitoring/health-check.sh

# Health check script for production monitoring
DOMAIN="yourdomain.com"
ENDPOINTS=(
  "https://$DOMAIN/health"
  "https://api.$DOMAIN/health"
  "https://socket.$DOMAIN/health"
  "https://game.$DOMAIN/health"
  "https://rooms.$DOMAIN/health"
)

for endpoint in "${ENDPOINTS[@]}"; do
  echo "Checking $endpoint..."

  response=$(curl -s -o /dev/null -w "%{http_code}" "$endpoint")

  if [ "$response" -eq 200 ]; then
    echo "✅ $endpoint is healthy"
  else
    echo "❌ $endpoint is unhealthy (HTTP $response)"
    # Send alert
    # curl -X POST "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK" \
    #   -H 'Content-type: application/json' \
    #   --data "{\"text\":\"🚨 Health check failed for $endpoint (HTTP $response)\"}"
  fi
done
```

### Log Monitoring

```bash
#!/bin/bash
# monitoring/log-monitor.sh

# Monitor application logs for errors
LOG_FILE="/var/log/nginx/access.log"
ERROR_LOG="/var/log/nginx/error.log"
PM2_LOG="/home/<USER>/.pm2/logs/prize-wheel-client-production-error.log"

# Monitor for 5xx errors
tail -f "$LOG_FILE" | while read line; do
  if echo "$line" | grep -q " 5[0-9][0-9] "; then
    echo "$(date): 5xx error detected: $line"
    # Send alert
  fi
done &

# Monitor for application errors
tail -f "$PM2_LOG" | while read line; do
  if echo "$line" | grep -q "ERROR"; then
    echo "$(date): Application error detected: $line"
    # Send alert
  fi
done &

# Monitor nginx errors
tail -f "$ERROR_LOG" | while read line; do
  echo "$(date): Nginx error: $line"
  # Send alert
done &
```

---

## Troubleshooting

### Common Deployment Issues

#### 1. Build Failures

**Symptoms:**
- `npm run build` fails
- TypeScript compilation errors
- Missing dependencies

**Solutions:**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm cache clean --force
npm install

# Check Node.js version
node --version  # Should be 18+

# Run type check
npm run type-check

# Check for missing environment variables
cat .env.local
```

#### 2. SSL Certificate Issues

**Symptoms:**
- HTTPS not working
- Certificate expired warnings
- Mixed content errors

**Solutions:**
```bash
# Check certificate status
sudo certbot certificates

# Renew certificate
sudo certbot renew

# Test SSL configuration
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# Check nginx SSL configuration
sudo nginx -t
```

#### 3. API Connection Issues

**Symptoms:**
- API calls failing
- CORS errors
- Network timeouts

**Solutions:**
```bash
# Test API connectivity
curl -v https://api.yourdomain.com/health

# Check nginx proxy configuration
sudo nginx -t
sudo systemctl reload nginx

# Verify environment variables
echo $REACT_APP_API_BASE_URL

# Check firewall rules
sudo ufw status
```

#### 4. Socket.IO Connection Issues

**Symptoms:**
- Real-time features not working
- WebSocket connection failures
- Frequent disconnections

**Solutions:**
```bash
# Test WebSocket connectivity
wscat -c wss://socket.yourdomain.com

# Check nginx WebSocket configuration
grep -A 10 "location /socket.io/" /etc/nginx/sites-enabled/prize-wheel-production

# Monitor socket connections
netstat -an | grep :3001

# Check socket service logs
pm2 logs socket-gateway
```

#### 5. Performance Issues

**Symptoms:**
- Slow page loads
- High memory usage
- CPU spikes

**Solutions:**
```bash
# Monitor system resources
htop
free -h
df -h

# Check PM2 process status
pm2 status
pm2 monit

# Analyze bundle size
npm run analyze

# Enable gzip compression
grep -A 5 "gzip" /etc/nginx/sites-enabled/prize-wheel-production
```

### Deployment Checklist

#### Pre-deployment
- [ ] All tests passing
- [ ] Code reviewed and approved
- [ ] Environment variables configured
- [ ] SSL certificates valid
- [ ] Database migrations completed
- [ ] Backup created

#### Deployment
- [ ] Application built successfully
- [ ] Static assets deployed
- [ ] Nginx configuration updated
- [ ] PM2 processes restarted
- [ ] Health checks passing

#### Post-deployment
- [ ] Functionality testing completed
- [ ] Performance monitoring active
- [ ] Error monitoring configured
- [ ] Logs being collected
- [ ] Alerts configured
- [ ] Documentation updated

### Emergency Procedures

#### Rollback Process
```bash
# Quick rollback to previous version
cd /var/www/prize-wheel-client
git log --oneline -5  # Find previous commit
git checkout PREVIOUS_COMMIT_HASH
npm ci --production
npm run build
pm2 reload ecosystem.config.js --env production

# Verify rollback
curl https://yourdomain.com/health
```

#### Service Recovery
```bash
# Restart all services
sudo systemctl restart nginx
pm2 restart all

# Check service status
sudo systemctl status nginx
pm2 status

# Monitor logs
tail -f /var/log/nginx/error.log
pm2 logs --lines 50
```

This comprehensive deployment guide provides complete instructions for deploying, monitoring, and maintaining the Prize Wheel client application in production environments.
