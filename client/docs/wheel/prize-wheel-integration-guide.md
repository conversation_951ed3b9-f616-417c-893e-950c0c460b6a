# Prize Wheel Integration Guide

## Overview

This comprehensive integration guide provides step-by-step instructions for integrating the enhanced Prize Wheel system into your application. It covers setup, configuration, authentication, and deployment for both development and production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Authentication Configuration](#authentication-configuration)
4. [Service URLs and Endpoints](#service-urls-and-endpoints)
5. [Client Integration Steps](#client-integration-steps)
6. [Socket.IO Configuration](#socketio-configuration)
7. [Error Handling Setup](#error-handling-setup)
8. [Testing Integration](#testing-integration)
9. [Production Deployment](#production-deployment)
10. [Troubleshooting](#troubleshooting)

---

## Prerequisites

### System Requirements
- **Node.js**: v18.0.0 or higher
- **React**: v18.0.0 or higher
- **TypeScript**: v4.9.0 or higher
- **Socket.IO Client**: v4.7.0 or higher

### Backend Services Required
- **Manager Service**: Prize pool and entry fee processing
- **Socket Gateway**: Real-time event handling
- **Game Service**: Game logic and state management
- **Room Service**: Room management and player states

### Development Tools
```bash
npm install -g typescript
npm install -g @types/node
npm install -g concurrently
```

---

## Environment Setup

### 1. Environment Variables

Create a `.env.local` file in your client root:

```bash
# API Configuration
REACT_APP_API_BASE_URL=http://localhost:3002
REACT_APP_SOCKET_URL=http://localhost:3001

# Authentication
REACT_APP_JWT_SECRET=your-jwt-secret-key
REACT_APP_AUTH_ENDPOINT=/api/auth

# Prize Wheel Configuration
REACT_APP_DEFAULT_HOUSE_EDGE=5.0
REACT_APP_MAX_PLAYERS_PER_ROOM=8
REACT_APP_MIN_ENTRY_FEE=0.01
REACT_APP_MAX_ENTRY_FEE=1000.00

# Feature Flags
REACT_APP_ENABLE_PRIZE_POOL_VISUALIZATION=true
REACT_APP_ENABLE_REAL_TIME_BALANCE=true
REACT_APP_ENABLE_ENHANCED_ERROR_HANDLING=true

# Debug Settings
REACT_APP_DEBUG=true
REACT_APP_LOG_LEVEL=info
```

### 2. Production Environment

For production, update the URLs:

```bash
# Production API Configuration
REACT_APP_API_BASE_URL=https://api.yourdomain.com
REACT_APP_SOCKET_URL=https://socket.yourdomain.com

# Security
REACT_APP_JWT_SECRET=your-production-jwt-secret
REACT_APP_ENABLE_SSL=true
REACT_APP_CORS_ORIGIN=https://yourdomain.com

# Performance
REACT_APP_API_TIMEOUT=10000
REACT_APP_SOCKET_TIMEOUT=5000
REACT_APP_RETRY_ATTEMPTS=3
```

---

## Authentication Configuration

### 1. JWT Token Management

```typescript
// src/utils/auth.ts
export class AuthManager {
  private static readonly TOKEN_KEY = 'authToken';
  private static readonly REFRESH_TOKEN_KEY = 'refreshToken';

  static getToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  static setToken(token: string): void {
    localStorage.setItem(this.TOKEN_KEY, token);
  }

  static getRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  static setRefreshToken(token: string): void {
    localStorage.setItem(this.REFRESH_TOKEN_KEY, token);
  }

  static clearTokens(): void {
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  static isTokenValid(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000 > Date.now();
    } catch {
      return false;
    }
  }

  static async refreshToken(): Promise<string | null> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) return null;

    try {
      const response = await fetch(`${process.env.REACT_APP_API_BASE_URL}/api/auth/refresh`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken }),
      });

      const data = await response.json();
      if (data.success) {
        this.setToken(data.token);
        return data.token;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    return null;
  }
}
```

### 2. API Client Authentication

```typescript
// src/services/api.ts
import { AuthManager } from '../utils/auth';

export class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor() {
    this.baseURL = process.env.REACT_APP_API_BASE_URL!;
    this.timeout = parseInt(process.env.REACT_APP_API_TIMEOUT || '10000');
  }

  private async makeRequest<T>(
    method: string,
    endpoint: string,
    data?: any,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const token = AuthManager.getToken();

    const config: RequestInit = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    if (data) {
      config.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, config);
      
      // Handle token expiration
      if (response.status === 401) {
        const newToken = await AuthManager.refreshToken();
        if (newToken) {
          // Retry with new token
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${newToken}`,
          };
          return this.makeRequest(method, endpoint, data, options);
        } else {
          // Redirect to login
          window.location.href = '/login';
          throw new Error('Authentication required');
        }
      }

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.error?.message || 'API request failed');
      }

      return result;
    } catch (error) {
      console.error(`API Error [${method} ${endpoint}]:`, error);
      throw error;
    }
  }

  // Prize Pool API Methods
  async getRoomPrizePool(roomId: string): Promise<PrizePool> {
    return this.makeRequest('GET', `/api/rooms/${roomId}/prize-pool`);
  }

  async getPotentialWinnings(roomId: string): Promise<PotentialWinningsResponse> {
    return this.makeRequest('GET', `/api/rooms/${roomId}/potential-winnings`);
  }

  // Entry Fee API Methods
  async validateBalance(request: BalanceValidationRequest): Promise<BalanceValidationResponse> {
    return this.makeRequest('POST', '/api/v1/entry_fees/validate_balance', request);
  }

  async processEntryFee(request: EntryFeeRequest): Promise<EntryFeeProcessResult> {
    return this.makeRequest('POST', '/api/v1/entry_fees/process_entry_fee', request);
  }

  async processRefund(request: RefundRequest): Promise<EntryFeeRefundResult> {
    return this.makeRequest('POST', '/api/v1/entry_fees/process_refund', request);
  }

  // Enhanced Room Management
  async getEnhancedRoomDetails(roomId: string): Promise<EnhancedRoomDetailsResponse> {
    return this.makeRequest('GET', `/admin/rooms/${roomId}`);
  }

  async kickPlayer(roomId: string, request: KickPlayerRequest): Promise<KickPlayerResponse> {
    return this.makeRequest('POST', `/admin/rooms/${roomId}/kick-player`, request);
  }
}

export const apiClient = new ApiClient();
```

---

## Service URLs and Endpoints

### Development Environment

| Service | URL | Port | Purpose |
|---------|-----|------|---------|
| Manager Service | http://localhost:3002 | 3002 | Prize pools, entry fees, transactions |
| Socket Gateway | http://localhost:3001 | 3001 | Real-time events, WebSocket connections |
| Game Service | http://localhost:3003 | 3003 | Game logic, wheel spinning |
| Room Service | http://localhost:3004 | 3004 | Room management, player states |

### Production Environment

| Service | URL | Purpose |
|---------|-----|---------|
| Manager Service | https://api.yourdomain.com | Prize pools, entry fees, transactions |
| Socket Gateway | https://socket.yourdomain.com | Real-time events, WebSocket connections |
| Game Service | https://game.yourdomain.com | Game logic, wheel spinning |
| Room Service | https://rooms.yourdomain.com | Room management, player states |

### Key API Endpoints

#### Prize Pool Management
- `GET /api/rooms/{roomId}/prize-pool` - Get room prize pool
- `GET /api/rooms/{roomId}/potential-winnings` - Get potential winnings
- `POST /api/rooms/{roomId}/distribute-prizes` - Distribute prizes (admin)

#### Entry Fee Processing
- `POST /api/v1/entry_fees/validate_balance` - Validate user balance
- `POST /api/v1/entry_fees/process_entry_fee` - Process entry fee payment
- `POST /api/v1/entry_fees/process_refund` - Process entry fee refund

#### Enhanced Room Management
- `GET /admin/rooms/{roomId}` - Get enhanced room details
- `POST /admin/rooms/{roomId}/kick-player` - Kick player (admin)

#### Health Checks
- `GET /health` - Service health status
- `GET /api/health` - API health status

---

## Client Integration Steps

### 1. Install Dependencies

```bash
npm install socket.io-client
npm install react-hot-toast
npm install @types/socket.io-client
npm install lucide-react
```

### 2. Setup Store Configuration

```typescript
// src/store/index.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface AppStore {
  // Auth state
  user: User | null;
  isAuthenticated: boolean;
  
  // Game state
  currentRoom: Room | null;
  prizePoolData: PrizePoolData;
  
  // Actions
  setUser: (user: User | null) => void;
  setCurrentRoom: (room: Room | null) => void;
  updatePrizePoolData: (data: PrizePool) => void;
}

export const useAppStore = create<AppStore>()(
  devtools(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      currentRoom: null,
      prizePoolData: {
        currentPrizePool: null,
        prizePoolLoading: false,
        prizePoolError: null,
        entryFeeStatus: {},
        potentialWinnings: null,
        transactionHistory: [],
      },

      setUser: (user) => set({ user, isAuthenticated: !!user }),
      setCurrentRoom: (room) => set({ currentRoom: room }),
      updatePrizePoolData: (data) => set((state) => ({
        prizePoolData: {
          ...state.prizePoolData,
          currentPrizePool: data,
        },
      })),
    }),
    { name: 'app-store' }
  )
);
```

### 3. Initialize Socket Connection

```typescript
// src/services/socket.ts
import { io, Socket } from 'socket.io-client';
import { AuthManager } from '../utils/auth';

class SocketManager {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect(): Socket {
    if (this.socket?.connected) {
      return this.socket;
    }

    const token = AuthManager.getToken();
    if (!token) {
      throw new Error('Authentication token required for socket connection');
    }

    this.socket = io(process.env.REACT_APP_SOCKET_URL!, {
      auth: { token },
      transports: ['websocket'],
      timeout: parseInt(process.env.REACT_APP_SOCKET_TIMEOUT || '5000'),
    });

    this.setupEventHandlers();
    return this.socket;
  }

  private setupEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('Socket connected');
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnection();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      this.handleReconnection();
    });

    // Prize Wheel specific events
    this.socket.on('user_balance_updated', this.handleBalanceUpdate);
    this.socket.on('user_transaction_completed', this.handleTransactionCompleted);
    this.socket.on('wheel_spinning', this.handleWheelSpinning);
    this.socket.on('wheel_result', this.handleWheelResult);
  }

  private handleReconnection(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = Math.pow(2, this.reconnectAttempts) * 1000; // Exponential backoff
      
      setTimeout(() => {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
      // Notify user of connection issues
    }
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  getSocket(): Socket | null {
    return this.socket;
  }

  // Event handlers
  private handleBalanceUpdate = (data: UserBalanceUpdatedData) => {
    // Handle balance updates
    console.log('Balance updated:', data);
  };

  private handleTransactionCompleted = (data: UserTransactionCompletedData) => {
    // Handle transaction completion
    console.log('Transaction completed:', data);
  };

  private handleWheelSpinning = (data: WheelSpinningData) => {
    // Handle wheel spinning
    console.log('Wheel spinning:', data);
  };

  private handleWheelResult = (data: WheelResultData) => {
    // Handle wheel result
    console.log('Wheel result:', data);
  };
}

export const socketManager = new SocketManager();
```

### 4. Component Integration

```typescript
// src/components/PrizeWheelGame.tsx
import React from 'react';
import { usePrizeWheelIntegration } from '../hooks';
import {
  PrizePoolDisplay,
  BalanceAndEntryFee,
  WinnerAnnouncement
} from './Game';

interface PrizeWheelGameProps {
  roomId: string;
}

export const PrizeWheelGame: React.FC<PrizeWheelGameProps> = ({ roomId }) => {
  const prizeWheelIntegration = usePrizeWheelIntegration(roomId, 10.00);

  const {
    prizePool,
    balance,
    entryFee,
    canParticipate,
    participationBlockers,
    actions: { prepareForGame, validateParticipation }
  } = prizeWheelIntegration;

  return (
    <div className="prize-wheel-game">
      {/* Prize Pool Information */}
      <PrizePoolDisplay
        roomId={roomId}
        showPotentialWinnings={true}
      />

      {/* Balance and Entry Fee Management */}
      <BalanceAndEntryFee
        roomId={roomId}
        betAmount={10.00}
        onBalanceValidated={(isValid) => console.log('Balance valid:', isValid)}
        showEntryFeeStatus={true}
      />

      {/* Participation Status */}
      {!canParticipate && (
        <div className="participation-blockers">
          <h3>Requirements to participate:</h3>
          <ul>
            {participationBlockers.map((blocker, index) => (
              <li key={index}>{blocker}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Game Controls */}
      <div className="game-controls">
        <button
          onClick={prepareForGame}
          disabled={!canParticipate}
          className="prepare-button"
        >
          {entryFee.isPaid ? 'Ready to Play' : 'Pay Entry Fee & Play'}
        </button>
      </div>
    </div>
  );
};
```

---

## Socket.IO Configuration

### 1. Socket Store Setup

```typescript
// src/store/socketStore.ts
import { create } from 'zustand';
import { Socket } from 'socket.io-client';
import { socketManager } from '../services/socket';

interface SocketStore {
  socket: Socket | null;
  isConnected: boolean;
  connectionError: string | null;

  // Actions
  connect: () => void;
  disconnect: () => void;
  joinRoom: (roomId: string) => Promise<void>;
  leaveRoom: (roomId: string) => Promise<void>;
  selectWheelColor: (roomId: string, color: string) => Promise<void>;
  setPlayerReady: (roomId: string, isReady: boolean) => Promise<void>;
}

export const useSocketStore = create<SocketStore>((set, get) => ({
  socket: null,
  isConnected: false,
  connectionError: null,

  connect: () => {
    try {
      const socket = socketManager.connect();
      set({ socket, isConnected: true, connectionError: null });
    } catch (error) {
      set({
        connectionError: error instanceof Error ? error.message : 'Connection failed',
        isConnected: false
      });
    }
  },

  disconnect: () => {
    socketManager.disconnect();
    set({ socket: null, isConnected: false });
  },

  joinRoom: async (roomId: string) => {
    const { socket } = get();
    if (!socket) throw new Error('Socket not connected');

    return new Promise((resolve, reject) => {
      socket.emit('join_room', { roomId }, (response: any) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(new Error(response.error || 'Failed to join room'));
        }
      });
    });
  },

  leaveRoom: async (roomId: string) => {
    const { socket } = get();
    if (!socket) return;

    return new Promise((resolve) => {
      socket.emit('leave_room', { roomId }, resolve);
    });
  },

  selectWheelColor: async (roomId: string, color: string) => {
    const { socket } = get();
    if (!socket) throw new Error('Socket not connected');

    return new Promise((resolve, reject) => {
      socket.emit('select_wheel_color', { roomId, color }, (response: any) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(new Error(response.error || 'Failed to select color'));
        }
      });
    });
  },

  setPlayerReady: async (roomId: string, isReady: boolean) => {
    const { socket } = get();
    if (!socket) throw new Error('Socket not connected');

    return new Promise((resolve, reject) => {
      socket.emit('player_ready', { roomId, isReady }, (response: any) => {
        if (response.success) {
          resolve(response);
        } else {
          reject(new Error(response.error || 'Failed to update ready state'));
        }
      });
    });
  },
}));
```

### 2. Socket Event Handling

```typescript
// src/hooks/useSocketEvents.ts
import { useEffect } from 'react';
import { useSocketStore } from '../store/socketStore';
import { useAppStore } from '../store';
import toast from 'react-hot-toast';

export const useSocketEvents = () => {
  const { socket } = useSocketStore();
  const { updatePrizePoolData } = useAppStore();

  useEffect(() => {
    if (!socket) return;

    // Room information updates
    const handleRoomInfoUpdated = (data: any) => {
      console.log('Room info updated:', data);
      // Update local state with room information
    };

    // Enhanced room info with prize pool
    const handleEnhancedRoomInfoUpdated = (data: EnhancedRoomInfoUpdatedData) => {
      console.log('Enhanced room info updated:', data);
      if (data.prizePool) {
        updatePrizePoolData(data.prizePool);
      }
    };

    // Balance updates
    const handleUserBalanceUpdated = (data: UserBalanceUpdatedData) => {
      console.log('User balance updated:', data);
      toast.success(`Balance updated: ${data.changeAmount > 0 ? '+' : ''}$${data.changeAmount.toFixed(2)}`);
    };

    // Transaction completion
    const handleUserTransactionCompleted = (data: UserTransactionCompletedData) => {
      console.log('Transaction completed:', data);
      toast.success(`Transaction completed: ${data.transaction.type}`);
    };

    // Wheel spinning
    const handleWheelSpinning = (data: WheelSpinningData) => {
      console.log('Wheel spinning:', data);
      // Start wheel animation
    };

    // Wheel result
    const handleWheelResult = (data: WheelResultData) => {
      console.log('Wheel result:', data);
      // Show winner announcement
    };

    // Game state changes
    const handleGameStateChanged = (data: GameStateChangedData) => {
      console.log('Game state changed:', data);
      // Update game state
    };

    // Register event listeners
    socket.on('room_info_updated', handleRoomInfoUpdated);
    socket.on('enhanced_room_info_updated', handleEnhancedRoomInfoUpdated);
    socket.on('user_balance_updated', handleUserBalanceUpdated);
    socket.on('user_transaction_completed', handleUserTransactionCompleted);
    socket.on('wheel_spinning', handleWheelSpinning);
    socket.on('wheel_result', handleWheelResult);
    socket.on('game_state_changed', handleGameStateChanged);

    // Cleanup
    return () => {
      socket.off('room_info_updated', handleRoomInfoUpdated);
      socket.off('enhanced_room_info_updated', handleEnhancedRoomInfoUpdated);
      socket.off('user_balance_updated', handleUserBalanceUpdated);
      socket.off('user_transaction_completed', handleUserTransactionCompleted);
      socket.off('wheel_spinning', handleWheelSpinning);
      socket.off('wheel_result', handleWheelResult);
      socket.off('game_state_changed', handleGameStateChanged);
    };
  }, [socket, updatePrizePoolData]);
};
```

---

## Error Handling Setup

### 1. Global Error Boundary

```typescript
// src/components/ErrorBoundary.tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);

    // Log to error reporting service
    if (process.env.NODE_ENV === 'production') {
      // Send to error reporting service
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened.</p>
          <button onClick={() => window.location.reload()}>
            Reload Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### 2. Prize Wheel Error Handler Integration

```typescript
// src/hooks/usePrizeWheelErrorHandler.ts
import { useMemo } from 'react';
import { PrizeWheelErrorHandler } from '../utils/prizeWheelErrorHandler';

export const usePrizeWheelErrorHandler = () => {
  const errorHandler = useMemo(() => PrizeWheelErrorHandler.getInstance(), []);

  const handleBalanceError = (error: any, currentBalance: number, requiredAmount: number) => {
    return errorHandler.handleApiError(error, 'balance_validation');
  };

  const handleEntryFeeError = (error: any, userId: string, roomId: string, amount: number) => {
    return errorHandler.handleApiError(error, 'entry_fee_processing');
  };

  const handlePrizePoolError = (error: any, userId?: string, roomId?: string) => {
    return errorHandler.handleApiError(error, 'prize_pool_management');
  };

  const handleApiErrorWithRetry = async <T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    retryDelay: number = 1000,
    options: { context?: string; showToast?: boolean } = {}
  ): Promise<T> => {
    return errorHandler.handleApiErrorWithRetry(apiCall, maxRetries, retryDelay, options);
  };

  const isErrorType = (error: any, errorCode: string): boolean => {
    return error?.code === errorCode;
  };

  return {
    handleBalanceError,
    handleEntryFeeError,
    handlePrizePoolError,
    handleApiErrorWithRetry,
    isErrorType,
    handleError: errorHandler.handleApiError.bind(errorHandler),
  };
};
```

---

## Testing Integration

### 1. Environment Setup for Testing

```bash
# Test environment variables
REACT_APP_API_BASE_URL=http://localhost:3002
REACT_APP_SOCKET_URL=http://localhost:3001
REACT_APP_TEST_MODE=true
REACT_APP_MOCK_API=false
```

### 2. Integration Test Example

```typescript
// src/__tests__/integration/prizeWheelIntegration.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { PrizeWheelGame } from '../../components/PrizeWheelGame';
import { TestWrapper } from '../utils/TestWrapper';

describe('Prize Wheel Integration', () => {
  beforeEach(() => {
    // Setup test environment
    process.env.REACT_APP_API_BASE_URL = 'http://localhost:3002';
    process.env.REACT_APP_SOCKET_URL = 'http://localhost:3001';
  });

  it('should connect to services and load prize pool data', async () => {
    render(
      <TestWrapper>
        <PrizeWheelGame roomId="test-room" />
      </TestWrapper>
    );

    // Wait for prize pool to load
    await waitFor(() => {
      expect(screen.getByText(/prize pool/i)).toBeInTheDocument();
    });

    // Verify balance display
    expect(screen.getByText(/current balance/i)).toBeInTheDocument();

    // Verify entry fee section
    expect(screen.getByText(/entry fee/i)).toBeInTheDocument();
  });

  it('should handle socket connection and events', async () => {
    const { container } = render(
      <TestWrapper>
        <PrizeWheelGame roomId="test-room" />
      </TestWrapper>
    );

    // Wait for socket connection
    await waitFor(() => {
      expect(container.querySelector('.socket-connected')).toBeInTheDocument();
    });

    // Test socket events
    // ... additional socket event tests
  });
});
```

This integration guide provides comprehensive setup instructions for implementing the enhanced Prize Wheel system with proper authentication, real-time features, and error handling.
