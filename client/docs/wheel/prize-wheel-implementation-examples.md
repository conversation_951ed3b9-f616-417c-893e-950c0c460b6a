# Prize Wheel Game - Enhanced Implementation Examples

## Table of Contents
1. [Enhanced Frontend Integration](#enhanced-frontend-integration)
2. [Custom React Hooks Examples](#custom-react-hooks-examples)
3. [Prize Pool Visualization Components](#prize-pool-visualization-components)
4. [Backend Service Integration](#backend-service-integration)
5. [Real-time Event Handling](#real-time-event-handling)
6. [Error Handling <PERSON>s](#error-handling-patterns)
7. [Testing Examples](#testing-examples)
8. [Admin Dashboard Examples](#admin-dashboard-examples)

---

## Enhanced Frontend Integration

### 1. Enhanced React Prize Wheel Component with Hooks

```typescript
import React, { useState } from 'react';
import { usePrizeWheelIntegration } from '@/hooks';
import {
  PrizePoolDisplay,
  BalanceAndEntryFee,
  WinnerAnnouncement,
  PrizePoolGrowthChart,
  HouseEdgeInfo
} from '@/components/Game';

interface EnhancedPrizeWheelProps {
  roomId: string;
}

const EnhancedPrizeWheelGame: React.FC<EnhancedPrizeWheelProps> = ({ roomId }) => {
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [showWinnerModal, setShowWinnerModal] = useState(false);
  const [winnerData, setWinnerData] = useState<any>(null);

  // Use the comprehensive integration hook
  const prizeWheelIntegration = usePrizeWheelIntegration(roomId, 10.00);

  const {
    prizePool,
    balance,
    entryFee,
    canParticipate,
    participationBlockers,
    gamePhase,
    actions: {
      prepareForGame,
      exitGame,
      validateParticipation,
      refreshAllData
    }
  } = prizeWheelIntegration;

  const handleColorSelect = async (color: string) => {
    if (!canParticipate) {
      const validation = validateParticipation();
      console.warn('Cannot select color:', validation.blockers);
      return;
    }

    setSelectedColor(color);
    // Color selection logic would be handled by the socket store
  };

  const handleReadyToggle = async () => {
    if (!selectedColor) {
      alert('Please select a color first');
      return;
    }

    if (!entryFee.isPaid) {
      // Prepare for game (validates balance and pays entry fee)
      const success = await prepareForGame();
      if (!success) {
        alert('Failed to prepare for game. Please check your balance.');
        return;
      }
    } else {
      // Exit game (process refund if applicable)
      await exitGame();
    }
  };

  const handleGameResult = (result: any) => {
    setWinnerData(result);
    setShowWinnerModal(true);
  };

  return (
    <div className="enhanced-prize-wheel-game">
      {/* Participation Blockers */}
      {participationBlockers.length > 0 && (
        <div className="participation-blockers">
          <h3>Requirements to Participate:</h3>
          <ul>
            {participationBlockers.map((blocker, index) => (
              <li key={index}>{blocker}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Prize Pool Display */}
      <PrizePoolDisplay
        roomId={roomId}
        showPotentialWinnings={true}
        compact={false}
      />

      {/* Balance and Entry Fee Management */}
      <BalanceAndEntryFee
        roomId={roomId}
        betAmount={10.00}
        onBalanceValidated={(isValid) => console.log('Balance valid:', isValid)}
        showEntryFeeStatus={true}
      />

      {/* House Edge Information */}
      {prizePool.prizePool && (
        <HouseEdgeInfo
          houseEdgePercentage={prizePool.houseEdge}
          totalPool={prizePool.totalPool}
          houseEdgeAmount={prizePool.houseEdgeAmount}
          netPrizeAmount={prizePool.netPrize}
          playerCount={prizePool.currentPlayers}
          entryFee={prizePool.entryFee}
        />
      )}

      {/* Color Selection */}
      <div className="color-selection">
        <h3>Select Your Color:</h3>
        <div className="color-grid">
          {['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'pink', 'cyan'].map(color => (
            <button
              key={color}
              className={`color-button ${color} ${selectedColor === color ? 'selected' : ''}`}
              onClick={() => handleColorSelect(color)}
              disabled={!canParticipate || gamePhase !== 'ready'}
            >
              {color}
            </button>
          ))}
        </div>
      </div>

      {/* Ready Button */}
      <div className="game-controls">
        <button
          className={`ready-button ${entryFee.isPaid ? 'ready' : 'not-ready'}`}
          onClick={handleReadyToggle}
          disabled={!selectedColor || entryFee.processing}
        >
          {entryFee.processing ? 'Processing...' :
           entryFee.isPaid ? 'Mark Not Ready' : 'Pay Entry Fee & Ready'}
        </button>
      </div>

      {/* Game Status */}
      <div className="game-status">
        <div>Game Phase: {gamePhase}</div>
        <div>Current Balance: ${balance.formattedBalance}</div>
        <div>Entry Fee Status: {entryFee.statusDisplay}</div>
      </div>

      {/* Winner Announcement Modal */}
      {showWinnerModal && winnerData && (
        <WinnerAnnouncement
          winner={winnerData.winner}
          totalPool={prizePool.totalPool}
          participantCount={prizePool.currentPlayers}
          winningColor={winnerData.winningColor}
          onClose={() => setShowWinnerModal(false)}
        />
      )}
    </div>
  );
};

export default EnhancedPrizeWheelGame;
```

## Custom React Hooks Examples

### 1. Prize Pool Data Hook Usage

```typescript
import { usePrizePoolData } from '@/hooks';

const PrizePoolComponent: React.FC<{ roomId: string }> = ({ roomId }) => {
  const {
    prizePool,
    potentialWinnings,
    loading,
    error,
    totalPool,
    netPrize,
    houseEdge,
    currentPlayers,
    maxPlayers,
    isAccumulating,
    userPotentialWinnings,
    actions: { loadPrizePool, refreshData, clearData }
  } = usePrizePoolData(roomId);

  if (loading) return <div>Loading prize pool...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="prize-pool-info">
      <h3>Prize Pool: ${totalPool.toFixed(2)}</h3>
      <p>Net Prize: ${netPrize.toFixed(2)} (after {houseEdge}% house edge)</p>
      <p>Players: {currentPlayers}/{maxPlayers}</p>
      <p>Status: {isAccumulating ? 'Accumulating' : 'Locked'}</p>

      {userPotentialWinnings && (
        <div className="user-winnings">
          <h4>Your Potential Winnings</h4>
          <p>Amount: ${userPotentialWinnings.potential_winnings.toFixed(2)}</p>
          <p>Probability: {(userPotentialWinnings.win_probability * 100).toFixed(1)}%</p>
        </div>
      )}

      <button onClick={refreshData}>Refresh Data</button>
    </div>
  );
};
```

### 2. Real-time Balance Hook Usage

```typescript
import { useRealTimeBalance } from '@/hooks';

const BalanceComponent: React.FC = () => {
  const {
    currentBalance,
    lastChangeAmount,
    lastChangeType,
    isValidating,
    validationError,
    hasInsufficientBalance,
    formattedBalance,
    formattedLastChange,
    changeDirection,
    actions: { validateBalance, refreshBalance, resetValidation }
  } = useRealTimeBalance();

  const handleValidateBalance = async (amount: number) => {
    const isValid = await validateBalance(amount);
    console.log('Balance validation result:', isValid);
  };

  return (
    <div className="balance-info">
      <h3>Current Balance: ${formattedBalance}</h3>

      {lastChangeAmount !== 0 && (
        <div className={`balance-change ${changeDirection}`}>
          {changeDirection === 'increase' ? '+' : '-'}${formattedLastChange}
          <span className="change-type">({lastChangeType})</span>
        </div>
      )}

      {isValidating && <div>Validating balance...</div>}
      {validationError && <div className="error">{validationError}</div>}

      <div className="balance-actions">
        <button onClick={() => handleValidateBalance(10.00)}>
          Validate $10.00
        </button>
        <button onClick={refreshBalance}>Refresh Balance</button>
        <button onClick={resetValidation}>Reset Validation</button>
      </div>
    </div>
  );
};
```

### 3. Entry Fee Processing Hook Usage

```typescript
import { useEntryFeeProcessing } from '@/hooks';

const EntryFeeComponent: React.FC<{ roomId: string; amount: number }> = ({
  roomId,
  amount
}) => {
  const {
    status,
    processing,
    error,
    isPaid,
    isRequired,
    canPay,
    canRefund,
    formattedAmount,
    statusDisplay,
    actions: { processEntryFee, processRefund, checkPaymentStatus, resetState }
  } = useEntryFeeProcessing(roomId, amount);

  const handlePayEntryFee = async () => {
    const success = await processEntryFee();
    if (success) {
      console.log('Entry fee paid successfully');
    }
  };

  const handleRefund = async () => {
    const success = await processRefund();
    if (success) {
      console.log('Refund processed successfully');
    }
  };

  return (
    <div className="entry-fee-info">
      <h3>Entry Fee: ${formattedAmount}</h3>
      <p>Status: {statusDisplay}</p>

      {error && <div className="error">{error}</div>}

      <div className="entry-fee-actions">
        {canPay && (
          <button
            onClick={handlePayEntryFee}
            disabled={processing}
          >
            {processing ? 'Processing...' : 'Pay Entry Fee'}
          </button>
        )}

        {canRefund && (
          <button
            onClick={handleRefund}
            disabled={processing}
          >
            {processing ? 'Processing...' : 'Request Refund'}
          </button>
        )}

        <button onClick={checkPaymentStatus}>Check Status</button>
        <button onClick={resetState}>Reset</button>
      </div>
    </div>
  );
};
```

## Prize Pool Visualization Components

### 1. Prize Pool Growth Chart Implementation

```typescript
import React, { useState, useEffect } from 'react';
import { PrizePoolGrowthChart } from '@/components/Game';

const PrizePoolVisualization: React.FC<{ roomId: string }> = ({ roomId }) => {
  const [growthData, setGrowthData] = useState<any[]>([]);
  const [currentPool, setCurrentPool] = useState(0);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      const newDataPoint = {
        timestamp: new Date().toISOString(),
        totalPool: currentPool + Math.random() * 10,
        playerCount: Math.floor(Math.random() * 8) + 1,
        entryFee: 10.00,
      };

      setGrowthData(prev => [...prev.slice(-19), newDataPoint]);
      setCurrentPool(newDataPoint.totalPool);
    }, 5000);

    return () => clearInterval(interval);
  }, [currentPool]);

  return (
    <div className="prize-pool-visualization">
      <PrizePoolGrowthChart
        data={growthData}
        currentPool={currentPool}
        maxPlayers={8}
        className="growth-chart"
      />
    </div>
  );
};
```

### 2. Potential Winnings Calculator Usage

```typescript
import React from 'react';
import { PotentialWinningsCalculator } from '@/components/Game';
import { usePrizePoolData } from '@/hooks';

const WinningsCalculatorExample: React.FC<{ roomId: string }> = ({ roomId }) => {
  const { prizePool, userPotentialWinnings } = usePrizePoolData(roomId);

  if (!prizePool) return <div>Loading...</div>;

  return (
    <div className="winnings-calculator">
      <PotentialWinningsCalculator
        currentPool={prizePool.total_pool}
        netPrizeAmount={prizePool.net_prize_amount}
        playerCount={prizePool.player_count}
        maxPlayers={prizePool.max_players}
        entryFee={prizePool.entry_fee_per_player}
        userPotentialWinnings={userPotentialWinnings}
      />
    </div>
  );
};
```

### 3. Winner Announcement with Animation

```typescript
import React, { useState } from 'react';
import { WinnerAnnouncement } from '@/components/Game';

const WinnerAnnouncementExample: React.FC = () => {
  const [showWinner, setShowWinner] = useState(false);

  const mockWinnerData = {
    userId: 'user123',
    username: 'PlayerOne',
    prizeAmount: 95.00,
    selectedColor: 'red',
  };

  const handleShowWinner = () => {
    setShowWinner(true);
  };

  return (
    <div className="winner-example">
      <button onClick={handleShowWinner}>
        Show Winner Announcement
      </button>

      {showWinner && (
        <WinnerAnnouncement
          winner={mockWinnerData}
          totalPool={100.00}
          participantCount={8}
          winningColor="red"
          onClose={() => setShowWinner(false)}
          autoCloseDelay={8000}
        />
      )}
    </div>
  );
};
```

  const toggleReady = async () => {
    if (!socket || loading) return;

    setLoading(true);
    setError(null);

    const event = gameState.isReady ? 'player_unready' : 'player_ready';
    
    socket.emit(event, { roomId }, (response) => {
      setLoading(false);
      if (!response.success) {
        setError(response.error || 'Failed to update ready state');
      }
    });
  };

  const startWheelAnimation = (data: any) => {
    // Implement wheel spinning animation
    const wheel = document.getElementById('prize-wheel');
    if (wheel) {
      wheel.style.transition = `transform ${data.spinDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`;
      wheel.style.transform = `rotate(${data.animationData.finalAngle || 1800}deg)`;
    }
  };

  const showGameResult = (data: any) => {
    // Show game result with winner information
    const isWinner = data.winner?.userId === userId;
    if (isWinner) {
      alert(`Congratulations! You won $${data.winner.prizeAmount}!`);
    } else {
      alert(`Game ended. Winner: ${data.winner?.username} with color ${data.winningColor}`);
    }
  };

  const showBalanceUpdate = (data: any) => {
    // Show balance update notification
    const message = data.changeType === 'prize_win' 
      ? `You won $${data.changeAmount}! New balance: $${data.newBalance}`
      : `Balance updated: $${data.newBalance}`;
    
    // Show notification (implement your notification system)
    console.log(message);
  };

  return (
    <div className="prize-wheel-game">
      {error && (
        <div className="error-message">
          {error}
          <button onClick={() => setError(null)}>×</button>
        </div>
      )}

      <div className="game-header">
        <h2>Prize Wheel Game</h2>
        <div className="game-info">
          <span>Players: {gameState.players.length}</span>
          <span>Prize Pool: ${gameState.prizePool}</span>
          <span>Status: {gameState.status}</span>
        </div>
      </div>

      <div className="wheel-container">
        <div id="prize-wheel" className="wheel">
          {/* Implement wheel visualization */}
        </div>
      </div>

      <div className="color-selection">
        <h3>Select Your Color:</h3>
        <div className="color-buttons">
          {gameState.availableColors.map(color => (
            <button
              key={color}
              className={`color-button ${color} ${gameState.selectedColor === color ? 'selected' : ''}`}
              onClick={() => selectColor(color)}
              disabled={loading || gameState.status !== 'waiting'}
            >
              {color}
            </button>
          ))}
        </div>
      </div>

      <div className="game-controls">
        <button
          className={`ready-button ${gameState.isReady ? 'ready' : 'not-ready'}`}
          onClick={toggleReady}
          disabled={loading || !gameState.selectedColor}
        >
          {loading ? 'Loading...' : gameState.isReady ? 'Not Ready' : 'Ready'}
        </button>
      </div>

      <div className="players-list">
        <h3>Players:</h3>
        {gameState.players.map(player => (
          <div key={player.userId} className="player-item">
            <span className="username">{player.username}</span>
            <span className={`color-indicator ${player.selectedColor}`}></span>
            <span className={`ready-status ${player.isReady ? 'ready' : 'not-ready'}`}>
              {player.isReady ? '✓' : '○'}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PrizeWheelGame;
```

### 2. Dashboard Integration Example

```typescript
// Dashboard Prize Pool Management Component
import React, { useState, useEffect } from 'react';
import { apiClient } from '@/lib/api';
import { PrizePool, EntryFeeResult } from '@/types';

const PrizePoolDashboard: React.FC = () => {
  const [prizePools, setPrizePools] = useState<PrizePool[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedPool, setSelectedPool] = useState<PrizePool | null>(null);

  useEffect(() => {
    fetchPrizePools();
  }, []);

  const fetchPrizePools = async () => {
    setLoading(true);
    try {
      const response = await apiClient.getPrizePools({
        status: 'accumulating',
        page: 1,
        per_page: 20
      });
      
      if (response.success && response.data) {
        setPrizePools(response.data.prize_pools || []);
      }
    } catch (error) {
      console.error('Failed to fetch prize pools:', error);
    } finally {
      setLoading(false);
    }
  };

  const distributePrizes = async (roomId: string, gameResults: any) => {
    try {
      const result = await apiClient.distributePrizes(roomId, gameResults);
      console.log('Prizes distributed:', result);
      fetchPrizePools(); // Refresh the list
    } catch (error) {
      console.error('Failed to distribute prizes:', error);
    }
  };

  const processEntryFee = async (userId: string, roomId: string, betAmount: number) => {
    try {
      const result: EntryFeeResult = await apiClient.processEntryFee(userId, roomId, betAmount, {
        room_name: 'Test Room',
        game_type: 'prize_wheel'
      });
      
      if (result.success) {
        console.log('Entry fee processed:', result);
        fetchPrizePools(); // Refresh to show updated pool
      } else {
        console.error('Entry fee failed:', result.error_message);
      }
    } catch (error) {
      console.error('Entry fee processing error:', error);
    }
  };

  return (
    <div className="prize-pool-dashboard">
      <h1>Prize Pool Management</h1>
      
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className="prize-pools-grid">
          {prizePools.map(pool => (
            <div key={pool.id} className="prize-pool-card">
              <h3>{pool.room_name}</h3>
              <div className="pool-stats">
                <div>Total Pool: ${pool.total_pool}</div>
                <div>Players: {pool.contributing_players.length}/{pool.max_players}</div>
                <div>Status: {pool.status}</div>
                <div>Net Prize: ${pool.net_prize_amount}</div>
              </div>
              
              <button onClick={() => setSelectedPool(pool)}>
                View Details
              </button>
            </div>
          ))}
        </div>
      )}

      {selectedPool && (
        <div className="pool-details-modal">
          <h2>Prize Pool Details</h2>
          <div className="details-content">
            <p>Room: {selectedPool.room_name}</p>
            <p>Total Pool: ${selectedPool.total_pool}</p>
            <p>Entry Fee: ${selectedPool.entry_fee_per_player}</p>
            <p>House Edge: {selectedPool.house_edge_percentage}%</p>
            <p>Contributing Players: {selectedPool.contributing_players.join(', ')}</p>
          </div>
          
          <div className="actions">
            <button onClick={() => setSelectedPool(null)}>Close</button>
            <button 
              onClick={() => distributePrizes(selectedPool.room_id, {
                winning_color: 'red',
                winner_user_id: selectedPool.contributing_players[0]
              })}
            >
              Distribute Prizes
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PrizePoolDashboard;
```

---

## Backend Service Integration

### 1. Node.js Service Integration

```javascript
// Prize Wheel Service Integration
const axios = require('axios');
const io = require('socket.io-client');

class PrizeWheelService {
  constructor(config) {
    this.managerServiceUrl = config.managerServiceUrl || 'http://localhost:3002';
    this.socketGatewayUrl = config.socketGatewayUrl || 'http://localhost:3001';
    this.authToken = config.authToken;
    
    this.setupSocketConnection();
  }

  setupSocketConnection() {
    this.socket = io(this.socketGatewayUrl, {
      query: { token: this.authToken }
    });

    this.socket.on('connect', () => {
      console.log('Connected to socket gateway');
    });

    this.socket.on('room_info_updated', (data) => {
      this.handleRoomUpdate(data);
    });

    this.socket.on('wheel_result', (data) => {
      this.handleGameResult(data);
    });
  }

  async createPrizePool(roomId, entryFee, maxPlayers = 8) {
    try {
      const response = await axios.post(`${this.managerServiceUrl}/api/v1/entry_fees/process_ready_fee`, {
        user_id: 'system',
        room_id: roomId,
        bet_amount: entryFee,
        metadata: {
          room_name: `Room ${roomId}`,
          game_type: 'prize_wheel',
          max_players: maxPlayers
        }
      }, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data;
    } catch (error) {
      console.error('Failed to create prize pool:', error.response?.data || error.message);
      throw error;
    }
  }

  async validatePlayerBalance(userId, betAmount) {
    try {
      const response = await axios.post(`${this.managerServiceUrl}/api/v1/entry_fees/validate_balance`, {
        user_id: userId,
        bet_amount: betAmount
      }, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.data;
    } catch (error) {
      console.error('Balance validation failed:', error.response?.data || error.message);
      return {
        success: false,
        has_sufficient_balance: false,
        error_message: error.response?.data?.error?.message || 'Validation failed'
      };
    }
  }

  async processPlayerEntry(userId, roomId, betAmount) {
    try {
      // First validate balance
      const validation = await this.validatePlayerBalance(userId, betAmount);
      if (!validation.has_sufficient_balance) {
        throw new Error(`Insufficient balance: ${validation.error_message}`);
      }

      // Process entry fee
      const response = await axios.post(`${this.managerServiceUrl}/api/v1/entry_fees/process_ready_fee`, {
        user_id: userId,
        room_id: roomId,
        bet_amount: betAmount,
        metadata: {
          game_type: 'prize_wheel',
          timestamp: new Date().toISOString()
        }
      }, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.data;
    } catch (error) {
      console.error('Failed to process player entry:', error.response?.data || error.message);
      throw error;
    }
  }

  async distributePrizes(roomId, gameResults) {
    try {
      const response = await axios.post(`${this.managerServiceUrl}/api/rooms/${roomId}/distribute-prizes`, {
        game_results: gameResults
      }, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Content-Type': 'application/json'
        }
      });

      return response.data.data;
    } catch (error) {
      console.error('Failed to distribute prizes:', error.response?.data || error.message);
      throw error;
    }
  }

  async getPrizePoolStatus(roomId) {
    try {
      const response = await axios.get(`${this.managerServiceUrl}/api/rooms/${roomId}/prize-pool`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        }
      });

      return response.data.data.prize_pool;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // Prize pool doesn't exist yet
      }
      console.error('Failed to get prize pool status:', error.response?.data || error.message);
      throw error;
    }
  }

  handleRoomUpdate(data) {
    console.log('Room updated:', data.room.id);
    // Implement your room update logic
    this.onRoomUpdate?.(data);
  }

  handleGameResult(data) {
    console.log('Game result:', data);
    // Automatically distribute prizes when game ends
    this.distributePrizes(data.roomId, {
      game_session_id: data.sessionId,
      winning_color: data.winningColor,
      final_angle: data.finalAngle,
      participants: data.participants,
      timestamp: data.timestamp
    }).then(result => {
      console.log('Prizes distributed automatically:', result);
    }).catch(error => {
      console.error('Auto prize distribution failed:', error);
    });
  }

  // Event handlers that can be overridden
  onRoomUpdate(data) {
    // Override this method to handle room updates
  }

  onGameResult(data) {
    // Override this method to handle game results
  }
}

module.exports = PrizeWheelService;

// Usage Example
const prizeWheelService = new PrizeWheelService({
  managerServiceUrl: 'http://localhost:3002',
  socketGatewayUrl: 'http://localhost:3001',
  authToken: 'your-service-token'
});

// Override event handlers
prizeWheelService.onRoomUpdate = (data) => {
  console.log('Custom room update handler:', data);
};

prizeWheelService.onGameResult = (data) => {
  console.log('Custom game result handler:', data);
};
```

This documentation provides comprehensive implementation examples. The next sections will cover error handling patterns and testing examples.
