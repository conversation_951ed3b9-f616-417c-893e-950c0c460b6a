# Prize Wheel Performance and Optimization Guide

## Overview

This comprehensive guide covers performance optimization strategies, monitoring approaches, and best practices for the Prize Wheel application to ensure optimal user experience and system efficiency.

## Table of Contents

1. [Performance Metrics](#performance-metrics)
2. [Frontend Optimization](#frontend-optimization)
3. [API Performance](#api-performance)
4. [Real-time Communication](#real-time-communication)
5. [Memory Management](#memory-management)
6. [Bundle Optimization](#bundle-optimization)
7. [Caching Strategies](#caching-strategies)
8. [Monitoring and Analytics](#monitoring-and-analytics)
9. [Performance Testing](#performance-testing)
10. [Optimization Checklist](#optimization-checklist)

---

## Performance Metrics

### Key Performance Indicators (KPIs)

#### Frontend Metrics
- **First Contentful Paint (FCP)**: < 1.5s
- **Largest Contentful Paint (LCP)**: < 2.5s
- **First Input Delay (FID)**: < 100ms
- **Cumulative Layout Shift (CLS)**: < 0.1
- **Time to Interactive (TTI)**: < 3.5s

#### API Performance
- **Response Time**: < 200ms (95th percentile)
- **Throughput**: > 1000 requests/second
- **Error Rate**: < 0.1%
- **Availability**: > 99.9%

#### Real-time Metrics
- **Socket Connection Time**: < 500ms
- **Event Latency**: < 50ms
- **Reconnection Time**: < 2s
- **Message Loss Rate**: < 0.01%

### Performance Monitoring Implementation

```typescript
// src/utils/performanceMonitor.ts
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;

  static getInstance(): PerformanceMonitor {
    if (!this.instance) {
      this.instance = new PerformanceMonitor();
    }
    return this.instance;
  }

  // Core Web Vitals monitoring
  measureCoreWebVitals(): void {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.name === 'first-contentful-paint') {
          this.reportMetric('FCP', entry.startTime);
        }
      }
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      this.reportMetric('LCP', lastEntry.startTime);
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.reportMetric('FID', entry.processingStart - entry.startTime);
      }
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          this.reportMetric('CLS', clsValue);
        }
      }
    }).observe({ entryTypes: ['layout-shift'] });
  }

  // API performance monitoring
  measureApiCall(endpoint: string, startTime: number, endTime: number, status: number): void {
    const duration = endTime - startTime;
    this.reportMetric('API_RESPONSE_TIME', duration, {
      endpoint,
      status,
      timestamp: new Date().toISOString(),
    });
  }

  // Socket performance monitoring
  measureSocketEvent(eventName: string, latency: number): void {
    this.reportMetric('SOCKET_LATENCY', latency, {
      event: eventName,
      timestamp: new Date().toISOString(),
    });
  }

  // Memory usage monitoring
  measureMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      this.reportMetric('MEMORY_USAGE', memory.usedJSHeapSize, {
        totalHeapSize: memory.totalJSHeapSize,
        heapSizeLimit: memory.jsHeapSizeLimit,
      });
    }
  }

  private reportMetric(name: string, value: number, metadata?: any): void {
    // Send to analytics service
    if (process.env.NODE_ENV === 'production') {
      // analytics.track('performance_metric', { name, value, metadata });
    }
    
    console.log(`Performance Metric: ${name} = ${value}ms`, metadata);
  }
}
```

---

## Frontend Optimization

### React Component Optimization

#### Memoization Strategies

```typescript
// Optimize expensive calculations
const PrizePoolDisplay = React.memo(({ roomId, prizePool }: PrizePoolDisplayProps) => {
  // Memoize expensive calculations
  const potentialWinnings = useMemo(() => {
    if (!prizePool) return 0;
    return calculatePotentialWinnings(prizePool.net_prize_amount, prizePool.player_count);
  }, [prizePool?.net_prize_amount, prizePool?.player_count]);

  // Memoize formatted values
  const formattedPool = useMemo(() => 
    formatCurrency(prizePool?.total_pool || 0), 
    [prizePool?.total_pool]
  );

  return (
    <div className="prize-pool-display">
      <h3>Prize Pool: {formattedPool}</h3>
      <p>Potential Winnings: {formatCurrency(potentialWinnings)}</p>
    </div>
  );
});

// Optimize callback functions
const PrizeWheelGame: React.FC<{ roomId: string }> = ({ roomId }) => {
  const [selectedColor, setSelectedColor] = useState('');

  // Memoize callback to prevent unnecessary re-renders
  const handleColorSelect = useCallback((color: string) => {
    setSelectedColor(color);
    // Additional logic...
  }, []);

  // Memoize expensive operations
  const colorOptions = useMemo(() => 
    generateColorOptions(selectedColor), 
    [selectedColor]
  );

  return (
    <ColorSelector 
      colors={colorOptions}
      onColorSelect={handleColorSelect}
    />
  );
};
```

#### Virtual Scrolling for Large Lists

```typescript
// Optimize large transaction history lists
import { FixedSizeList as List } from 'react-window';

const TransactionHistory: React.FC<{ transactions: Transaction[] }> = ({ transactions }) => {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <TransactionItem transaction={transactions[index]} />
    </div>
  );

  return (
    <List
      height={400}
      itemCount={transactions.length}
      itemSize={60}
      width="100%"
    >
      {Row}
    </List>
  );
};
```

### State Management Optimization

```typescript
// Optimize Zustand store with selectors
export const useGameStore = create<GameStore>((set, get) => ({
  // ... store implementation
}));

// Use specific selectors to prevent unnecessary re-renders
const usePrizePoolTotal = () => useGameStore(state => state.prizePoolData.currentPrizePool?.total_pool);
const usePrizePoolStatus = () => useGameStore(state => state.prizePoolData.currentPrizePool?.status);

// Optimize with shallow comparison for objects
const usePrizePoolData = () => useGameStore(
  state => state.prizePoolData,
  shallow
);
```

### Code Splitting and Lazy Loading

```typescript
// Lazy load heavy components
const AdminDashboard = lazy(() => import('../components/Admin/AdminDashboard'));
const PrizePoolVisualization = lazy(() => import('../components/Game/PrizePoolVisualization'));

// Route-based code splitting
const AppRoutes: React.FC = () => (
  <Routes>
    <Route path="/" element={<Home />} />
    <Route 
      path="/admin" 
      element={
        <Suspense fallback={<LoadingSpinner />}>
          <AdminDashboard />
        </Suspense>
      } 
    />
    <Route 
      path="/game/:roomId" 
      element={
        <Suspense fallback={<GameLoadingSkeleton />}>
          <PrizeWheelGame />
        </Suspense>
      } 
    />
  </Routes>
);

// Component-based lazy loading
const PrizeWheelReadySection: React.FC = () => {
  const [showVisualization, setShowVisualization] = useState(false);

  return (
    <div>
      {/* Core game components */}
      <PrizePoolDisplay />
      <ColorSelector />
      
      {/* Lazy load visualization only when needed */}
      {showVisualization && (
        <Suspense fallback={<ChartSkeleton />}>
          <PrizePoolVisualization />
        </Suspense>
      )}
    </div>
  );
};
```

---

## API Performance

### Request Optimization

```typescript
// Implement request batching
class ApiRequestBatcher {
  private batchQueue: Map<string, Array<{ resolve: Function; reject: Function; params: any }>> = new Map();
  private batchTimeout: Map<string, NodeJS.Timeout> = new Map();

  batchRequest<T>(endpoint: string, params: any, batchKey: string): Promise<T> {
    return new Promise((resolve, reject) => {
      if (!this.batchQueue.has(batchKey)) {
        this.batchQueue.set(batchKey, []);
      }

      this.batchQueue.get(batchKey)!.push({ resolve, reject, params });

      // Clear existing timeout
      if (this.batchTimeout.has(batchKey)) {
        clearTimeout(this.batchTimeout.get(batchKey)!);
      }

      // Set new timeout to execute batch
      this.batchTimeout.set(batchKey, setTimeout(() => {
        this.executeBatch(endpoint, batchKey);
      }, 50)); // 50ms batch window
    });
  }

  private async executeBatch(endpoint: string, batchKey: string): Promise<void> {
    const requests = this.batchQueue.get(batchKey) || [];
    this.batchQueue.delete(batchKey);
    this.batchTimeout.delete(batchKey);

    try {
      const batchParams = requests.map(req => req.params);
      const results = await apiClient.batchRequest(endpoint, batchParams);

      requests.forEach((req, index) => {
        req.resolve(results[index]);
      });
    } catch (error) {
      requests.forEach(req => req.reject(error));
    }
  }
}

// Use request deduplication
class ApiClient {
  private requestCache = new Map<string, Promise<any>>();

  async get<T>(endpoint: string, params?: any): Promise<T> {
    const cacheKey = `${endpoint}:${JSON.stringify(params)}`;
    
    if (this.requestCache.has(cacheKey)) {
      return this.requestCache.get(cacheKey);
    }

    const request = this.makeRequest<T>('GET', endpoint, params);
    this.requestCache.set(cacheKey, request);

    // Clear cache after request completes
    request.finally(() => {
      setTimeout(() => {
        this.requestCache.delete(cacheKey);
      }, 5000); // 5 second cache
    });

    return request;
  }
}
```

### Response Caching

```typescript
// Implement intelligent caching
class CacheManager {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl: number = 300000): void { // 5 min default
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  get(key: string): any | null {
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  invalidate(pattern: string): void {
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }
}

// Use with API client
const cacheManager = new CacheManager();

export const apiClient = {
  async getPrizePool(roomId: string): Promise<PrizePool> {
    const cacheKey = `prize-pool:${roomId}`;
    const cached = cacheManager.get(cacheKey);
    
    if (cached) return cached;
    
    const data = await this.makeRequest<PrizePool>('GET', `/api/rooms/${roomId}/prize-pool`);
    cacheManager.set(cacheKey, data, 30000); // 30 second cache
    
    return data;
  },

  async processEntryFee(request: EntryFeeRequest): Promise<EntryFeeProcessResult> {
    const result = await this.makeRequest<EntryFeeProcessResult>('POST', '/api/v1/entry_fees/process_entry_fee', request);
    
    // Invalidate related caches
    cacheManager.invalidate(`prize-pool:${request.room_id}`);
    cacheManager.invalidate(`balance:${request.user_id}`);
    
    return result;
  },
};
```

---

## Real-time Communication Optimization

### Socket.IO Performance

```typescript
// Optimize Socket.IO connection
class OptimizedSocketManager {
  private socket: Socket | null = null;
  private eventQueue: Array<{ event: string; data: any }> = [];
  private isConnected = false;

  connect(): Socket {
    this.socket = io(process.env.REACT_APP_SOCKET_URL!, {
      transports: ['websocket'], // Use only WebSocket for better performance
      upgrade: false,
      rememberUpgrade: false,
      timeout: 5000,
      forceNew: false,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      maxReconnectionAttempts: 5,
      randomizationFactor: 0.5,
    });

    this.setupOptimizedEventHandlers();
    return this.socket;
  }

  private setupOptimizedEventHandlers(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      this.isConnected = true;
      this.flushEventQueue();
    });

    this.socket.on('disconnect', () => {
      this.isConnected = false;
    });

    // Batch similar events to reduce processing overhead
    this.setupEventBatching();
  }

  private setupEventBatching(): void {
    const batchedEvents = new Map<string, any[]>();
    const batchTimeout = new Map<string, NodeJS.Timeout>();

    const processBatch = (eventName: string) => {
      const events = batchedEvents.get(eventName) || [];
      if (events.length === 0) return;

      // Process batched events
      this.processBatchedEvents(eventName, events);

      batchedEvents.delete(eventName);
      batchTimeout.delete(eventName);
    };

    // Events that can be batched
    const batchableEvents = ['user_balance_updated', 'room_info_updated'];

    batchableEvents.forEach(eventName => {
      this.socket!.on(eventName, (data) => {
        if (!batchedEvents.has(eventName)) {
          batchedEvents.set(eventName, []);
        }

        batchedEvents.get(eventName)!.push(data);

        // Clear existing timeout
        if (batchTimeout.has(eventName)) {
          clearTimeout(batchTimeout.get(eventName)!);
        }

        // Set new timeout
        batchTimeout.set(eventName, setTimeout(() => {
          processBatch(eventName);
        }, 16)); // ~60fps batching
      });
    });
  }

  private processBatchedEvents(eventName: string, events: any[]): void {
    switch (eventName) {
      case 'user_balance_updated':
        // Process only the latest balance update
        const latestBalance = events[events.length - 1];
        this.handleBalanceUpdate(latestBalance);
        break;

      case 'room_info_updated':
        // Merge room updates
        const mergedRoomData = this.mergeRoomUpdates(events);
        this.handleRoomUpdate(mergedRoomData);
        break;
    }
  }

  // Throttle outgoing events
  private throttledEmit = throttle((event: string, data: any) => {
    if (this.isConnected && this.socket) {
      this.socket.emit(event, data);
    } else {
      this.eventQueue.push({ event, data });
    }
  }, 100); // Max 10 events per second

  emit(event: string, data: any): void {
    this.throttledEmit(event, data);
  }
}
```

### Event Throttling and Debouncing

```typescript
// Throttle and debounce utilities
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;

  return (...args: Parameters<T>) => {
    const currentTime = Date.now();

    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
        lastExecTime = Date.now();
      }, delay - (currentTime - lastExecTime));
    }
  };
};

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout | null = null;

  return (...args: Parameters<T>) => {
    if (timeoutId) clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Usage in components
const PrizePoolDisplay: React.FC = () => {
  const [prizePool, setPrizePool] = useState<PrizePool | null>(null);

  // Throttle prize pool updates to prevent excessive re-renders
  const throttledUpdatePrizePool = useCallback(
    throttle((newPrizePool: PrizePool) => {
      setPrizePool(newPrizePool);
    }, 100),
    []
  );

  useEffect(() => {
    socket.on('prize_pool_updated', throttledUpdatePrizePool);
    return () => {
      socket.off('prize_pool_updated', throttledUpdatePrizePool);
    };
  }, [throttledUpdatePrizePool]);
};
```

---

## Memory Management

### Memory Leak Prevention

```typescript
// Proper cleanup in React components
const PrizeWheelGame: React.FC<{ roomId: string }> = ({ roomId }) => {
  const [gameData, setGameData] = useState(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    // Setup socket connection
    socketRef.current = io(process.env.REACT_APP_SOCKET_URL!);

    // Setup polling interval
    intervalRef.current = setInterval(() => {
      // Polling logic
    }, 5000);

    // Cleanup function
    return () => {
      // Clear interval
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }

      // Disconnect socket
      if (socketRef.current) {
        socketRef.current.disconnect();
      }

      // Clear state
      setGameData(null);
    };
  }, [roomId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Additional cleanup if needed
    };
  }, []);
};

// Memory-efficient data structures
class CircularBuffer<T> {
  private buffer: T[];
  private size: number;
  private index: number = 0;
  private count: number = 0;

  constructor(size: number) {
    this.size = size;
    this.buffer = new Array(size);
  }

  push(item: T): void {
    this.buffer[this.index] = item;
    this.index = (this.index + 1) % this.size;
    this.count = Math.min(this.count + 1, this.size);
  }

  getAll(): T[] {
    if (this.count < this.size) {
      return this.buffer.slice(0, this.count);
    }
    return [...this.buffer.slice(this.index), ...this.buffer.slice(0, this.index)];
  }
}

// Use for transaction history
const useTransactionHistory = () => {
  const [transactions] = useState(() => new CircularBuffer<Transaction>(100));

  const addTransaction = useCallback((transaction: Transaction) => {
    transactions.push(transaction);
  }, [transactions]);

  return { transactions: transactions.getAll(), addTransaction };
};
```

### Garbage Collection Optimization

```typescript
// Object pooling for frequently created objects
class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;

  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize: number = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;

    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(this.createFn());
    }
  }

  acquire(): T {
    if (this.pool.length > 0) {
      return this.pool.pop()!;
    }
    return this.createFn();
  }

  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }
}

// Example usage for animation objects
const animationObjectPool = new ObjectPool(
  () => ({ x: 0, y: 0, rotation: 0, scale: 1 }),
  (obj) => { obj.x = 0; obj.y = 0; obj.rotation = 0; obj.scale = 1; },
  20
);
```

---

## Bundle Optimization

### Webpack/Vite Configuration

```typescript
// vite.config.ts optimizations
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  plugins: [
    react(),
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
    }),
  ],
  build: {
    target: 'es2020',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          socket: ['socket.io-client'],
          ui: ['lucide-react', 'react-hot-toast'],
          charts: ['recharts'],
        },
      },
    },
    chunkSizeWarningLimit: 1000,
  },
  optimizeDeps: {
    include: ['react', 'react-dom', 'socket.io-client'],
  },
});
```

### Tree Shaking Optimization

```typescript
// Optimize imports to enable tree shaking
// Bad - imports entire library
import * as _ from 'lodash';

// Good - imports only needed functions
import { debounce, throttle } from 'lodash-es';

// Even better - use specific imports
import debounce from 'lodash-es/debounce';
import throttle from 'lodash-es/throttle';

// For utility libraries, create barrel exports
// utils/index.ts
export { formatCurrency } from './currency';
export { calculatePotentialWinnings } from './prizePool';
export { validateBalance } from './validation';

// Use dynamic imports for large dependencies
const loadChartLibrary = async () => {
  const { Chart } = await import('chart.js');
  return Chart;
};
```

---

## Caching Strategies

### Browser Caching

```typescript
// Service Worker for advanced caching
// public/sw.js
const CACHE_NAME = 'prize-wheel-v1';
const urlsToCache = [
  '/',
  '/static/js/bundle.js',
  '/static/css/main.css',
  '/manifest.json',
];

self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      })
  );
});

// Register service worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}
```

### Application-Level Caching

```typescript
// React Query for server state caching
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

const usePrizePoolData = (roomId: string) => {
  return useQuery({
    queryKey: ['prizePool', roomId],
    queryFn: () => apiClient.getRoomPrizePool(roomId),
    staleTime: 30000, // 30 seconds
    cacheTime: 300000, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

const useEntryFeeProcessing = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: apiClient.processEntryFee,
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries(['prizePool', variables.room_id]);
      queryClient.invalidateQueries(['balance', variables.user_id]);
    },
  });
};
```

This comprehensive performance optimization guide provides strategies for optimizing every aspect of the Prize Wheel application, ensuring optimal user experience and system efficiency.
