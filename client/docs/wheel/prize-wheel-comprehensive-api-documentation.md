# Prize Wheel Game - Comprehensive API Documentation

## Table of Contents
1. [API Documentation](#api-documentation)
2. [Event Documentation](#event-documentation)
3. [Implementation Examples](#implementation-examples)
4. [Response Schemas](#response-schemas)
5. [Integration Guide](#integration-guide)

---

## API Documentation

### Base URLs
- **Manager Service**: `http://localhost:3002`
- **Socket Gateway**: `http://localhost:3001`
- **Dashboard Service**: `http://localhost:3004`

### Authentication
All API requests require authentication via Bearer token:
```http
Authorization: Bearer <your-jwt-token>
```

---

## Prize Pool Management API

### 1. Get Prize Pools
**Endpoint**: `GET /api/prize-pools`

**Query Parameters**:
```typescript
{
  page?: number;           // Default: 1
  per_page?: number;       // Default: 20, Max: 100
  status?: string;         // 'accumulating' | 'locked' | 'distributed' | 'cancelled'
  game_type?: string;      // 'prize_wheel'
  room_id?: string;        // Filter by specific room
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "prize_pools": [
      {
        "id": "64f8a1b2c3d4e5f6a7b8c9d0",
        "room_id": "room_123",
        "total_pool": 800.00,
        "entry_fee_per_player": 100.00,
        "contributing_players": ["user1", "user2", "user3", "user4"],
        "house_edge_percentage": 5.0,
        "house_edge_amount": 40.00,
        "net_prize_amount": 760.00,
        "status": "accumulating",
        "game_type": "prize_wheel",
        "room_name": "High Stakes Wheel",
        "max_players": 8,
        "player_count": 4,
        "is_full": false,
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T11:45:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 20,
      "total_count": 45,
      "total_pages": 3
    }
  }
}
```

### 2. Get Prize Pool by ID
**Endpoint**: `GET /api/prize-pools/:id`

**Response**:
```json
{
  "success": true,
  "data": {
    "prize_pool": {
      "id": "64f8a1b2c3d4e5f6a7b8c9d0",
      "room_id": "room_123",
      "total_pool": 800.00,
      "entry_fee_per_player": 100.00,
      "contributing_players": ["user1", "user2", "user3", "user4"],
      "house_edge_percentage": 5.0,
      "house_edge_amount": 40.00,
      "net_prize_amount": 760.00,
      "status": "accumulating",
      "game_type": "prize_wheel",
      "room_name": "High Stakes Wheel",
      "max_players": 8,
      "locked_at": null,
      "distributed_at": null,
      "metadata": {
        "last_player_joined": "2024-01-15T11:45:00Z",
        "game_settings": {
          "spin_duration": 5000,
          "result_display_duration": 3000
        }
      },
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T11:45:00Z"
    }
  }
}
```

### 3. Get Room Prize Pool
**Endpoint**: `GET /api/rooms/:room_id/prize-pool`

**Response**: Same as Get Prize Pool by ID

### 4. Distribute Prizes
**Endpoint**: `POST /api/rooms/:room_id/distribute-prizes`

**Request Body**:
```json
{
  "game_results": {
    "game_session_id": "session_456",
    "winning_color": "red",
    "final_angle": 45.5,
    "spin_duration": 5000,
    "participants": [
      {
        "user_id": "user1",
        "username": "player1",
        "selected_color": "red",
        "bet_amount": 100.00,
        "is_winner": true
      },
      {
        "user_id": "user2",
        "username": "player2",
        "selected_color": "blue",
        "bet_amount": 100.00,
        "is_winner": false
      }
    ],
    "timestamp": "2024-01-15T12:00:00Z"
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "distribution_id": "dist_789",
    "prize_pool_id": "64f8a1b2c3d4e5f6a7b8c9d0",
    "total_distributed": 760.00,
    "house_edge_collected": 40.00,
    "winners": [
      {
        "user_id": "user1",
        "username": "player1",
        "prize_amount": 760.00,
        "new_balance": 1260.00,
        "transaction_id": "txn_abc123"
      }
    ],
    "distribution_summary": {
      "total_pool": 800.00,
      "net_prize_amount": 760.00,
      "house_edge_amount": 40.00,
      "winner_count": 1,
      "participant_count": 4
    },
    "distributed_at": "2024-01-15T12:00:00Z"
  }
}
```

### 5. Get Potential Winnings
**Endpoint**: `GET /api/rooms/:room_id/potential-winnings`

**Response**:
```json
{
  "success": true,
  "data": {
    "potential_winnings": [
      {
        "user_id": "user1",
        "potential_winnings": 760.00,
        "win_probability": 0.125,
        "current_pool_share": 1.0
      },
      {
        "user_id": "user2",
        "potential_winnings": 760.00,
        "win_probability": 0.125,
        "current_pool_share": 1.0
      }
    ]
  }
}
```

---

## Entry Fee Management API

### 1. Validate Balance
**Endpoint**: `POST /api/v1/entry_fees/validate_balance`

**Request Body**:
```json
{
  "user_id": "user123",
  "bet_amount": 100.00
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "has_sufficient_balance": true,
    "current_balance": 500.00,
    "required_amount": 100.00,
    "shortfall": null,
    "error_code": null,
    "error_message": null
  }
}
```

### 2. Process Entry Fee
**Endpoint**: `POST /api/v1/entry_fees/process_ready_fee`

**Request Body**:
```json
{
  "user_id": "user123",
  "room_id": "room_456",
  "bet_amount": 100.00,
  "metadata": {
    "room_name": "High Stakes Wheel",
    "game_type": "prize_wheel",
    "max_players": 8,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "transaction": {
      "id": "txn_def456",
      "user_id": "user123",
      "amount": 100.00,
      "type": "ready_fee",
      "status": "completed",
      "description": "Entry fee for prize_wheel game",
      "metadata": {
        "room_id": "room_456",
        "service": "entry_fee_service",
        "processed_at": "2024-01-15T10:30:00Z"
      },
      "created_at": "2024-01-15T10:30:00Z"
    },
    "prize_pool": {
      "room_id": "room_456",
      "total_pool": 400.00,
      "contributing_players": ["user1", "user2", "user3", "user123"],
      "player_count": 4
    },
    "current_balance": 400.00,
    "error_code": null,
    "error_message": null
  }
}
```

### 3. Process Refund
**Endpoint**: `POST /api/v1/entry_fees/process_refund`

**Request Body**:
```json
{
  "user_id": "user123",
  "room_id": "room_456",
  "bet_amount": 100.00,
  "metadata": {
    "reason": "player_unready",
    "timestamp": "2024-01-15T10:35:00Z"
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "transaction": {
      "id": "txn_ghi789",
      "user_id": "user123",
      "amount": 100.00,
      "type": "refund",
      "status": "completed",
      "description": "Entry fee refund for prize_wheel game",
      "created_at": "2024-01-15T10:35:00Z"
    },
    "refunded_amount": 100.00,
    "current_balance": 500.00,
    "error_code": null,
    "error_message": null
  }
}
```

### 4. Check Payment Status
**Endpoint**: `GET /api/v1/entry_fees/check_payment/:user_id/:room_id`

**Response**:
```json
{
  "success": true,
  "data": {
    "success": true,
    "user_id": "user123",
    "room_id": "room_456",
    "has_paid_entry_fee": true,
    "checked_at": "2024-01-15T10:40:00Z"
  }
}
```

---

## Room Management API

### 1. Get Enhanced Room Details
**Endpoint**: `GET /admin/rooms/:id/details`

**Response**:
```json
{
  "success": true,
  "data": {
    "room": {
      "id": "room_123",
      "name": "High Stakes Wheel",
      "game_type": "prize_wheel",
      "status": "waiting",
      "current_players": 4,
      "max_players": 8,
      "min_players": 2,
      "bet_amount": 100.00,
      "prize_pool": 400.00,
      "currency": "USD",
      "is_private": false,
      "creator": {
        "id": "admin1",
        "username": "admin"
      },
      "configuration": {
        "game_duration": 300,
        "spin_duration": 5000,
        "result_display_duration": 3000,
        "wheel_sections": 8,
        "colors": ["red", "blue", "green", "yellow", "purple", "orange", "pink", "cyan"]
      },
      "players": [
        {
          "user_id": "user1",
          "username": "player1",
          "position": 1,
          "is_ready": true,
          "bet_amount": 100.00,
          "selected_color": "red",
          "session_count": 1,
          "status": "active",
          "joined_at": "2024-01-15T10:30:00Z"
        }
      ],
      "game_sessions": [
        {
          "id": "session_456",
          "session_id": "session_456",
          "status": "completed",
          "bet_amount": 100.00,
          "win_amount": 760.00,
          "started_at": "2024-01-15T11:00:00Z",
          "ended_at": "2024-01-15T11:05:00Z"
        }
      ],
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T11:45:00Z"
    }
  }
}
```

### 2. Get Room Players
**Endpoint**: `GET /admin/rooms/:id/players`

**Response**:
```json
{
  "success": true,
  "data": {
    "players": [
      {
        "user_id": "user1",
        "username": "player1",
        "position": 1,
        "is_ready": true,
        "bet_amount": 100.00,
        "selected_color": "red",
        "session_count": 1,
        "status": "active",
        "joined_at": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### 3. Kick Player
**Endpoint**: `POST /admin/rooms/:id/kick-player`

**Request Body**:
```json
{
  "user_id": "user123",
  "reason": "Inappropriate behavior",
  "notify_player": true
}
```

**Response**:
```json
{
  "success": true,
  "message": "Player kicked successfully",
  "data": {
    "kicked_user_id": "user123",
    "reason": "Inappropriate behavior",
    "kicked_at": "2024-01-15T12:00:00Z",
    "kicked_by": "admin1"
  }
}
```

---

## Error Response Format

All API endpoints follow a consistent error response format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "Additional error details",
      "validation_errors": ["Array of validation errors"]
    }
  },
  "timestamp": "2024-01-15T12:00:00Z"
}
```

### Common Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_REQUEST` | 400 | Invalid request parameters |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `NOT_FOUND` | 404 | Resource not found |
| `ROOM_FULL` | 422 | Room has reached maximum capacity |
| `INSUFFICIENT_BALANCE` | 422 | User has insufficient balance |
| `PRIZE_POOL_NOT_FOUND` | 404 | Prize pool not found for room |
| `INVALID_GAME_STATE` | 422 | Invalid game state for operation |
| `DISTRIBUTION_FAILED` | 500 | Prize distribution failed |
| `TRANSACTION_FAILED` | 500 | Transaction processing failed |

---

## Event Documentation

### Socket.IO Connection
**URL**: `ws://localhost:3001`

**Authentication**: Include JWT token in connection query:
```javascript
const socket = io('ws://localhost:3001', {
  query: { token: 'your-jwt-token' }
});
```

---

## Client-to-Server Events

### 1. Room Management Events

#### `join_room`
Join a game room.

**Request**:
```javascript
socket.emit('join_room', {
  roomId: "room_123"
}, (response) => {
  console.log(response);
});
```

**Response**:
```javascript
{
  success: true,
  message: "Successfully joined room",
  data: {
    roomId: "room_123",
    position: 1,
    playerCount: 2
  }
}
```

#### `leave_room`
Leave a game room.

**Request**:
```javascript
socket.emit('leave_room', {
  roomId: "room_123"
}, (response) => {
  console.log(response);
});
```

#### `subscribe_room`
Subscribe to room updates without joining.

**Request**:
```javascript
socket.emit('subscribe_room', {
  roomId: "room_123"
}, (response) => {
  console.log(response);
});
```

### 2. Game State Events

#### `player_ready`
Set player ready state.

**Request**:
```javascript
socket.emit('player_ready', {
  roomId: "room_123"
}, (response) => {
  console.log(response);
});
```

**Response**:
```javascript
{
  success: true,
  message: "Player ready state updated",
  data: {
    isReady: true,
    readyPlayers: 3,
    canStartGame: false
  }
}
```

#### `player_unready`
Set player unready state.

**Request**:
```javascript
socket.emit('player_unready', {
  roomId: "room_123"
}, (response) => {
  console.log(response);
});
```

### 3. Prize Wheel Specific Events

#### `select_wheel_color`
Select a color on the prize wheel.

**Request**:
```javascript
socket.emit('select_wheel_color', {
  roomId: "room_123",
  color: "red" // "red" | "blue" | "green" | "yellow" | "purple" | "orange" | "pink" | "cyan"
}, (response) => {
  console.log(response);
});
```

**Response**:
```javascript
{
  success: true,
  message: "Color selected successfully",
  data: {
    selectedColor: "red",
    availableColors: ["blue", "green", "yellow", "purple", "orange", "pink", "cyan"]
  }
}
```

---

## Server-to-Client Events

### 1. Room Information Events

#### `room_info_updated`
Broadcast when room information changes.

**Event Data**:
```javascript
{
  room: {
    id: "room_123",
    name: "High Stakes Wheel",
    gameType: "prize_wheel",
    status: "waiting", // "waiting" | "countdown" | "spinning" | "result"
    playerCount: 4,
    maxPlayers: 8,
    minPlayers: 2,
    readyCount: 3,
    betAmount: 100.00,
    prizePool: 400.00,
    canJoin: true,
    canStart: false
  },
  players: [
    {
      userId: "user1",
      username: "player1",
      position: 1,
      isReady: true,
      selectedColor: "red",
      colorName: "Red",
      colorHex: "#FF0000",
      betAmount: 100.00,
      joinedAt: "2024-01-15T10:30:00Z"
    }
  ],
  gameSpecificData: {
    colorState: {
      selectedColors: {
        "user1": "red",
        "user2": "blue"
      },
      availableColors: ["green", "yellow", "purple", "orange", "pink", "cyan"],
      colorAssignments: {
        "red": "user1",
        "blue": "user2"
      }
    },
    wheelConfig: {
      sections: 8,
      colors: ["red", "blue", "green", "yellow", "purple", "orange", "pink", "cyan"],
      spinDuration: 5000,
      resultDisplayDuration: 3000
    }
  },
  reason: "color_selection", // "player_joined" | "player_left" | "color_selection" | "ready_state_change"
  timestamp: "2024-01-15T10:35:00Z"
}
```

### 2. Game State Events

#### `game_state_changed`
Broadcast when game state transitions.

**Event Data**:
```javascript
{
  roomId: "room_123",
  sessionId: "session_456",
  previousState: "waiting",
  newState: "countdown", // "waiting" | "countdown" | "spinning" | "result"
  participantCount: 4,
  timestamp: "2024-01-15T11:00:00Z",
  stateData: {
    countdown: {
      duration: 10000,
      remaining: 8500,
      canCancel: true
    }
  },
  triggerReason: "minimum_players_ready" // "minimum_players_ready" | "countdown_expired" | "game_completed"
}
```

#### `wheel_spinning`
Broadcast when wheel starts spinning.

**Event Data**:
```javascript
{
  roomId: "room_123",
  sessionId: "session_456",
  spinId: "spin_789",
  spinDuration: 5000,
  participants: [
    {
      userId: "user1",
      username: "player1",
      selectedColor: "red",
      betAmount: 100.00
    }
  ],
  animationData: {
    startAngle: 0,
    rotationSpeed: 1800, // degrees per second
    deceleration: 0.95
  },
  timestamp: "2024-01-15T11:00:10Z"
}
```

#### `wheel_result`
Broadcast when wheel stops and result is determined.

**Event Data**:
```javascript
{
  roomId: "room_123",
  sessionId: "session_456",
  spinId: "spin_789",
  winningColor: "red",
  finalAngle: 45.5,
  winner: {
    userId: "user1",
    username: "player1",
    selectedColor: "red",
    prizeAmount: 760.00
  },
  participants: [
    {
      userId: "user1",
      username: "player1",
      selectedColor: "red",
      betAmount: 100.00,
      isWinner: true,
      prizeAmount: 760.00
    },
    {
      userId: "user2",
      username: "player2",
      selectedColor: "blue",
      betAmount: 100.00,
      isWinner: false,
      prizeAmount: 0.00
    }
  ],
  prizeDistribution: {
    totalPool: 800.00,
    houseEdge: 40.00,
    netPrize: 760.00
  },
  displayDuration: 3000,
  timestamp: "2024-01-15T11:00:15Z"
}
```

### 3. Balance and Transaction Events

#### `user_balance_updated`
Sent to specific user when balance changes.

**Event Data**:
```javascript
{
  userId: "user1",
  previousBalance: 500.00,
  newBalance: 1260.00,
  changeAmount: 760.00,
  changeType: "prize_win", // "entry_fee" | "refund" | "prize_win" | "deposit" | "withdrawal"
  transactionId: "txn_abc123",
  roomId: "room_123", // if related to game
  description: "Prize wheel win",
  timestamp: "2024-01-15T11:00:15Z"
}
```

#### `user_transaction_completed`
Sent to user when transaction is processed.

**Event Data**:
```javascript
{
  userId: "user1",
  transaction: {
    id: "txn_abc123",
    type: "prize_win",
    amount: 760.00,
    status: "completed",
    description: "Prize wheel win",
    metadata: {
      roomId: "room_123",
      sessionId: "session_456",
      gameType: "prize_wheel"
    },
    createdAt: "2024-01-15T11:00:15Z"
  },
  newBalance: 1260.00,
  timestamp: "2024-01-15T11:00:15Z"
}
```

### 4. Error and Notification Events

#### `error`
Sent when an error occurs.

**Event Data**:
```javascript
{
  code: "INSUFFICIENT_BALANCE",
  message: "Insufficient balance for entry fee",
  details: {
    required: 100.00,
    available: 50.00,
    shortfall: 50.00
  },
  timestamp: "2024-01-15T10:30:00Z"
}
```

#### `notification`
General notification to user.

**Event Data**:
```javascript
{
  type: "info", // "info" | "warning" | "error" | "success"
  title: "Game Starting",
  message: "The prize wheel game is about to begin!",
  roomId: "room_123",
  autoHide: true,
  duration: 5000,
  timestamp: "2024-01-15T11:00:00Z"
}
```

---

## Event Sequence Flows

### Complete Game Cycle Flow

1. **Player Joins Room**
   ```
   Client → join_room → Server
   Server → room_info_updated → All Room Subscribers
   ```

2. **Player Selects Color**
   ```
   Client → select_wheel_color → Server
   Server → room_info_updated (with color state) → All Room Subscribers
   ```

3. **Player Becomes Ready**
   ```
   Client → player_ready → Server
   Server → Entry Fee Processing → Manager Service
   Manager Service → user_balance_updated → Player
   Server → room_info_updated → All Room Subscribers
   ```

4. **Game Starts (Minimum Players Ready)**
   ```
   Game Service → game_state_changed (countdown) → Socket Gateway
   Socket Gateway → game_state_changed → All Room Subscribers
   ```

5. **Wheel Spins**
   ```
   Game Service → game_state_changed (spinning) → Socket Gateway
   Socket Gateway → wheel_spinning → All Room Subscribers
   ```

6. **Game Ends with Result**
   ```
   Game Service → wheel_result → Socket Gateway
   Socket Gateway → wheel_result → All Room Subscribers
   Game Service → Prize Distribution → Manager Service
   Manager Service → user_balance_updated → Winner
   Manager Service → user_transaction_completed → Winner
   ```

7. **Room Reset**
   ```
   Server → room_info_updated (reset state) → All Room Subscribers
   ```

---

This documentation provides comprehensive coverage of all Socket.IO events. The next sections will cover implementation examples and integration guides.
