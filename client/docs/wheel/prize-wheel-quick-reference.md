# Prize Wheel Quick Reference Guide

## 🚀 **Getting Started**

### Installation & Setup
```bash
npm install socket.io-client react-hot-toast
npm install @types/socket.io-client
```

### Environment Variables
```bash
REACT_APP_API_BASE_URL=http://localhost:3002
REACT_APP_SOCKET_URL=http://localhost:3001
REACT_APP_ENABLE_PRIZE_POOL_VISUALIZATION=true
REACT_APP_ENABLE_REAL_TIME_BALANCE=true
```

### Basic Integration
```typescript
import { usePrizeWheelIntegration } from './hooks';

const PrizeWheelGame: React.FC<{ roomId: string }> = ({ roomId }) => {
  const { canParticipate, actions } = usePrizeWheelIntegration(roomId, 10.00);
  
  return (
    <button 
      onClick={actions.prepareForGame}
      disabled={!canParticipate}
    >
      Play Now
    </button>
  );
};
```

---

## 📡 **API Endpoints**

### Prize Pool Management
```typescript
// Get room prize pool
GET /api/rooms/{roomId}/prize-pool
Response: PrizePool

// Get potential winnings
GET /api/rooms/{roomId}/potential-winnings
Response: PotentialWinningsResponse

// Distribute prizes (admin)
POST /admin/rooms/{roomId}/distribute-prizes
```

### Entry Fee Processing
```typescript
// Validate balance
POST /api/v1/entry_fees/validate_balance
Body: { user_id: string, bet_amount: number }
Response: BalanceValidationResponse

// Process entry fee
POST /api/v1/entry_fees/process_entry_fee
Body: EntryFeeRequest
Response: EntryFeeProcessResult

// Process refund
POST /api/v1/entry_fees/process_refund
Body: RefundRequest
Response: EntryFeeRefundResult
```

### Enhanced Room Management
```typescript
// Get enhanced room details
GET /admin/rooms/{roomId}
Response: EnhancedRoomDetailsResponse

// Kick player (admin)
POST /admin/rooms/{roomId}/kick-player
Body: KickPlayerRequest
Response: KickPlayerResponse
```

---

## 🔌 **Socket Events**

### Incoming Events
```typescript
// Balance updates
socket.on('user_balance_updated', (data: UserBalanceUpdatedData) => {
  // Handle balance change
});

// Transaction completion
socket.on('user_transaction_completed', (data: UserTransactionCompletedData) => {
  // Handle transaction
});

// Wheel spinning
socket.on('wheel_spinning', (data: WheelSpinningData) => {
  // Start wheel animation
});

// Game result
socket.on('wheel_result', (data: WheelResultData) => {
  // Show winner
});

// Enhanced room updates
socket.on('enhanced_room_info_updated', (data: EnhancedRoomInfoUpdatedData) => {
  // Update room and prize pool
});
```

### Outgoing Events
```typescript
// Join room
socket.emit('join_room', { roomId }, callback);

// Select color
socket.emit('select_wheel_color', { roomId, color }, callback);

// Set ready status
socket.emit('player_ready', { roomId, isReady }, callback);

// Leave room
socket.emit('leave_room', { roomId }, callback);
```

---

## 🎣 **Custom Hooks**

### Core Integration Hook
```typescript
const prizeWheelIntegration = usePrizeWheelIntegration(roomId, betAmount);

// Available properties
prizeWheelIntegration.prizePool.data
prizeWheelIntegration.balance.current
prizeWheelIntegration.entryFee.status
prizeWheelIntegration.canParticipate
prizeWheelIntegration.participationBlockers

// Available actions
prizeWheelIntegration.actions.prepareForGame()
prizeWheelIntegration.actions.validateParticipation()
```

### Individual Hooks
```typescript
// Prize pool data
const prizePool = usePrizePoolData(roomId);
// Properties: data, loading, error, actions.refreshData()

// Real-time balance
const balance = useRealTimeBalance();
// Properties: current, loading, hasInsufficientBalance(), getShortfall()

// Entry fee processing
const entryFee = useEntryFeeProcessing(roomId, betAmount);
// Properties: status, processing, isPaid, processEntryFee(), processRefund()

// Error handling
const errorHandler = usePrizeWheelErrorHandler();
// Methods: handleError(), handleApiErrorWithRetry(), isErrorType()
```

---

## 🧩 **Components**

### Prize Pool Components
```typescript
// Prize pool display
<PrizePoolDisplay 
  roomId={roomId}
  showPotentialWinnings={true}
/>

// Balance and entry fee
<BalanceAndEntryFee
  roomId={roomId}
  betAmount={10.00}
  showEntryFeeStatus={true}
/>

// Transaction history
<TransactionHistory 
  userId={userId}
  roomId={roomId}
  maxItems={10}
/>
```

### Visualization Components
```typescript
// Prize pool growth chart
<PrizePoolGrowthChart 
  data={growthData}
  currentPool={currentPool}
  maxPlayers={8}
/>

// Winner announcement
<WinnerAnnouncement 
  winner={winnerData}
  totalPool={totalPool}
  participantCount={participantCount}
  onClose={handleClose}
/>

// House edge info
<HouseEdgeInfo 
  houseEdgePercentage={5}
  totalPool={100.00}
  showCalculation={true}
/>
```

### Admin Components
```typescript
// Admin dashboard
<AdminDashboard />

// Prize pool dashboard
<PrizePoolDashboard />

// Room management
<RoomManagement roomId={roomId} />
```

---

## 📊 **TypeScript Interfaces**

### Core Types
```typescript
interface PrizePool {
  id: string;
  room_id: string;
  total_pool: number;
  entry_fee_per_player: number;
  contributing_players: string[];
  house_edge_percentage: number;
  house_edge_amount: number;
  net_prize_amount: number;
  status: 'accumulating' | 'locked' | 'distributed' | 'cancelled';
  game_type: 'prize_wheel';
  // ... other properties
}

interface EntryFeeRequest {
  user_id: string;
  room_id: string;
  bet_amount: number;
  metadata: {
    game_type: 'prize_wheel';
    timestamp: string;
    [key: string]: any;
  };
}

interface Transaction {
  id: string;
  user_id: string;
  type: 'entry_fee' | 'prize_payout' | 'refund';
  amount: number;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  description: string;
  metadata: Record<string, any>;
  created_at: string;
}
```

### Socket Event Types
```typescript
interface UserBalanceUpdatedData {
  userId: string;
  newBalance: number;
  previousBalance: number;
  changeAmount: number;
  changeReason: string;
  transaction?: Transaction;
  timestamp: string;
}

interface WheelResultData {
  roomId: string;
  winner: {
    userId: string;
    username: string;
    selectedColor: string;
    prizeAmount: number;
  };
  winningColor: string;
  prizePool: PrizePool;
  participantCount: number;
  gameId: string;
  timestamp: string;
}
```

---

## ⚠️ **Error Handling**

### Common Error Codes
```typescript
enum PrizeWheelErrorCode {
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  ENTRY_FEE_PROCESSING_FAILED = 'ENTRY_FEE_PROCESSING_FAILED',
  PRIZE_POOL_NOT_FOUND = 'PRIZE_POOL_NOT_FOUND',
  ROOM_NOT_FOUND = 'ROOM_NOT_FOUND',
  ROOM_FULL = 'ROOM_FULL',
  COLOR_ALREADY_SELECTED = 'COLOR_ALREADY_SELECTED',
  UNAUTHORIZED = 'UNAUTHORIZED',
  NETWORK_ERROR = 'NETWORK_ERROR',
}
```

### Error Handling Examples
```typescript
// Basic error handling
try {
  await apiClient.processEntryFee(request);
} catch (error) {
  const prizeWheelError = errorHandler.handleError(error);
  console.log(prizeWheelError.message);
}

// With retry logic
const result = await errorHandler.handleApiErrorWithRetry(
  () => apiClient.processEntryFee(request),
  3, // max retries
  1000, // delay
  { context: 'entry_fee_processing' }
);

// Check specific error type
if (errorHandler.isErrorType(error, 'INSUFFICIENT_BALANCE')) {
  // Handle insufficient balance
}
```

---

## 🎨 **Styling Classes**

### Component Classes
```css
/* Prize pool display */
.prize-pool-display
.prize-pool-amount
.prize-pool-participants
.prize-pool-loading
.prize-pool-error

/* Balance and entry fee */
.balance-and-entry-fee
.balance-section
.balance-amount
.insufficient-balance-warning
.entry-fee-section
.entry-fee-status

/* Winner announcement */
.winner-modal
.winner-content
.trophy-icon
.winner-info
.prize-amount
.continue-button

/* Error states */
.error-toast
.error-boundary
.participation-blockers
```

### Status Classes
```css
/* Entry fee status */
.entry-fee-not-required
.entry-fee-required
.entry-fee-paid
.entry-fee-failed
.entry-fee-refunded

/* Game status */
.game-waiting
.game-in-progress
.game-completed
.game-cancelled

/* Error severity */
.severity-low
.severity-medium
.severity-high
.severity-critical
```

---

## 🔧 **Configuration**

### Feature Flags
```typescript
const config = {
  enablePrizePoolVisualization: process.env.REACT_APP_ENABLE_PRIZE_POOL_VISUALIZATION === 'true',
  enableRealTimeBalance: process.env.REACT_APP_ENABLE_REAL_TIME_BALANCE === 'true',
  enableEnhancedErrorHandling: process.env.REACT_APP_ENABLE_ENHANCED_ERROR_HANDLING === 'true',
  enableAdminDashboard: process.env.REACT_APP_ENABLE_ADMIN_DASHBOARD === 'true',
};
```

### Performance Settings
```typescript
const performanceConfig = {
  apiTimeout: parseInt(process.env.REACT_APP_API_TIMEOUT || '10000'),
  socketTimeout: parseInt(process.env.REACT_APP_SOCKET_TIMEOUT || '5000'),
  retryAttempts: parseInt(process.env.REACT_APP_RETRY_ATTEMPTS || '3'),
  cacheTimeout: 30000, // 30 seconds
  batchingDelay: 16, // ~60fps
};
```

This quick reference guide provides essential information for developers working with the Prize Wheel system, including API endpoints, socket events, hooks, components, and common patterns.
