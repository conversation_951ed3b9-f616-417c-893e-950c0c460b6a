# Prize Wheel Enhanced Features Documentation

## Overview

This document covers the enhanced features implemented in the Prize Wheel client that provide advanced functionality beyond the basic game mechanics. These features include comprehensive error handling, real-time data management, prize pool visualization, and enhanced user experience components.

## Table of Contents

1. [Enhanced Color Selection System](#enhanced-color-selection-system)
2. [Real-time Balance Management](#real-time-balance-management)
3. [Comprehensive Error Handling](#comprehensive-error-handling)
4. [Prize Pool Visualization](#prize-pool-visualization)
5. [Entry Fee Processing](#entry-fee-processing)
6. [Admin Dashboard Features](#admin-dashboard-features)
7. [Custom React Hooks](#custom-react-hooks)
8. [Performance Optimizations](#performance-optimizations)

---

## Enhanced Color Selection System

### Features
- **Conflict Resolution**: Automatic handling of color selection conflicts
- **Real-time Availability**: Live updates of available colors
- **Visual Feedback**: Enhanced UI indicators for color states
- **Validation**: Pre-selection validation of user eligibility

### Implementation

```typescript
// Enhanced Color Selection Hook
export const useEnhancedColorSelection = (roomId: string) => {
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [availableColors, setAvailableColors] = useState<string[]>([]);
  const [colorStatistics, setColorStatistics] = useState({
    takenCount: 0,
    availableCount: 8,
    popularColors: [],
  });

  const enhancedSelectColor = async (color: string) => {
    // Validate color availability
    if (!availableColors.includes(color)) {
      throw new Error('Color no longer available');
    }

    // Process selection with conflict resolution
    const result = await apiClient.selectColor(roomId, color);
    
    if (result.success) {
      setSelectedColor(color);
      updateColorStatistics(result.colorState);
    }

    return result;
  };

  return {
    selectedColor,
    availableColors,
    colorStatistics,
    enhancedSelectColor,
  };
};
```

### Color State Management

```typescript
interface ColorState {
  availableColors: string[];
  takenColors: Record<string, string>; // color -> userId
  colorStatistics: {
    totalSelections: number;
    popularityRanking: string[];
    selectionHistory: ColorSelection[];
  };
}

interface ColorSelection {
  userId: string;
  color: string;
  timestamp: string;
  conflictResolved?: boolean;
}
```

---

## Real-time Balance Management

### Features
- **Live Balance Updates**: Real-time balance tracking via WebSocket
- **Transaction Notifications**: Instant notifications for balance changes
- **Balance Validation**: Pre-action balance validation
- **Historical Tracking**: Complete transaction history

### Implementation

```typescript
// Real-time Balance Hook
export const useRealTimeBalance = () => {
  const [balance, setBalance] = useState(0);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [balanceHistory, setBalanceHistory] = useState<BalanceChange[]>([]);

  // Socket event handlers
  useEffect(() => {
    socket.on('user_balance_updated', (data: UserBalanceUpdatedData) => {
      setBalance(data.newBalance);
      addBalanceChange({
        previousBalance: data.previousBalance,
        newBalance: data.newBalance,
        changeAmount: data.changeAmount,
        changeType: data.changeType,
        timestamp: data.timestamp,
      });
    });

    socket.on('user_transaction_completed', (data: UserTransactionCompletedData) => {
      setTransactions(prev => [data.transaction, ...prev]);
      setBalance(data.newBalance);
    });

    return () => {
      socket.off('user_balance_updated');
      socket.off('user_transaction_completed');
    };
  }, []);

  const validateBalance = async (requiredAmount: number) => {
    const result = await apiClient.validateBalance({
      user_id: userId,
      bet_amount: requiredAmount,
    });

    return result.has_sufficient_balance;
  };

  return {
    balance,
    transactions,
    balanceHistory,
    validateBalance,
  };
};
```

### Balance Change Notifications

```typescript
interface BalanceChange {
  previousBalance: number;
  newBalance: number;
  changeAmount: number;
  changeType: 'entry_fee' | 'refund' | 'prize_win' | 'deposit' | 'withdrawal';
  timestamp: string;
  transactionId?: string;
}

// Notification Component
const BalanceNotification: React.FC<{ change: BalanceChange }> = ({ change }) => {
  const isPositive = change.changeAmount > 0;
  
  return (
    <div className={`balance-notification ${isPositive ? 'positive' : 'negative'}`}>
      <div className="notification-icon">
        {isPositive ? '💰' : '💸'}
      </div>
      <div className="notification-content">
        <div className="change-amount">
          {isPositive ? '+' : '-'}${Math.abs(change.changeAmount).toFixed(2)}
        </div>
        <div className="change-type">{change.changeType.replace('_', ' ')}</div>
        <div className="new-balance">New balance: ${change.newBalance.toFixed(2)}</div>
      </div>
    </div>
  );
};
```

---

## Comprehensive Error Handling

### Features
- **Specific Error Codes**: 20+ specific error codes for different scenarios
- **User-friendly Messages**: Context-aware error messages
- **Automatic Retry Logic**: Intelligent retry with exponential backoff
- **Error Recovery**: Graceful degradation and recovery options

### Error Code System

```typescript
enum PrizeWheelErrorCode {
  // Balance & Payment Errors
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  BALANCE_VALIDATION_FAILED = 'BALANCE_VALIDATION_FAILED',
  ENTRY_FEE_PROCESSING_FAILED = 'ENTRY_FEE_PROCESSING_FAILED',
  REFUND_PROCESSING_FAILED = 'REFUND_PROCESSING_FAILED',
  
  // Prize Pool Errors
  PRIZE_POOL_NOT_FOUND = 'PRIZE_POOL_NOT_FOUND',
  PRIZE_POOL_LOCKED = 'PRIZE_POOL_LOCKED',
  PRIZE_DISTRIBUTION_FAILED = 'PRIZE_DISTRIBUTION_FAILED',
  
  // Room & Game Errors
  ROOM_NOT_FOUND = 'ROOM_NOT_FOUND',
  ROOM_FULL = 'ROOM_FULL',
  GAME_IN_PROGRESS = 'GAME_IN_PROGRESS',
  
  // Player Errors
  PLAYER_NOT_FOUND = 'PLAYER_NOT_FOUND',
  COLOR_ALREADY_SELECTED = 'COLOR_ALREADY_SELECTED',
  INVALID_COLOR_SELECTION = 'INVALID_COLOR_SELECTION',
  
  // System Errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_TIMEOUT = 'API_TIMEOUT',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}
```

### Error Handler Implementation

```typescript
export class PrizeWheelErrorHandler {
  private static instance: PrizeWheelErrorHandler;

  static getInstance(): PrizeWheelErrorHandler {
    if (!this.instance) {
      this.instance = new PrizeWheelErrorHandler();
    }
    return this.instance;
  }

  handleApiError(error: any, context?: string): PrizeWheelError {
    const errorCode = this.mapErrorCode(error);
    const userMessage = this.getUserFriendlyMessage(errorCode, error);
    
    const prizeWheelError: PrizeWheelError = {
      code: errorCode,
      message: userMessage,
      details: error.details || {},
      timestamp: new Date().toISOString(),
      context,
    };

    this.logError(prizeWheelError);
    return prizeWheelError;
  }

  getUserFriendlyMessage(errorCode: PrizeWheelErrorCode, error: any): string {
    switch (errorCode) {
      case PrizeWheelErrorCode.INSUFFICIENT_BALANCE:
        const details = error.details || {};
        if (details.currentBalance && details.requiredAmount) {
          const shortfall = details.requiredAmount - details.currentBalance;
          return `Insufficient balance. You need $${details.requiredAmount.toFixed(2)} but only have $${details.currentBalance.toFixed(2)} (short by $${shortfall.toFixed(2)})`;
        }
        return "You don't have enough balance to complete this action";

      case PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED:
        return "Failed to process entry fee. Please check your balance and try again";

      case PrizeWheelErrorCode.ROOM_FULL:
        return "Room is full. Please try another room";

      case PrizeWheelErrorCode.COLOR_ALREADY_SELECTED:
        return "This color has already been selected by another player";

      default:
        return error.message || "An unexpected error occurred";
    }
  }

  async handleApiErrorWithRetry<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
    options: { context?: string; showToast?: boolean } = {}
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxRetries) {
          break;
        }

        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    const prizeWheelError = this.handleApiError(lastError, options.context);
    
    if (options.showToast !== false) {
      this.showErrorToast(prizeWheelError);
    }

    throw prizeWheelError;
  }

  showErrorToast(error: PrizeWheelError): void {
    const duration = this.getToastDuration(error.code);
    const icon = this.getErrorIcon(error.code);

    toast.error(error.message, {
      duration,
      icon,
      style: { maxWidth: '500px' },
    });
  }

  private getToastDuration(errorCode: PrizeWheelErrorCode): number {
    switch (errorCode) {
      case PrizeWheelErrorCode.INSUFFICIENT_BALANCE:
      case PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED:
        return 8000; // Longer for important errors
      
      case PrizeWheelErrorCode.COLOR_ALREADY_SELECTED:
      case PrizeWheelErrorCode.ROOM_FULL:
        return 3000; // Shorter for less critical errors
      
      default:
        return 5000;
    }
  }

  private getErrorIcon(errorCode: PrizeWheelErrorCode): string {
    switch (errorCode) {
      case PrizeWheelErrorCode.INSUFFICIENT_BALANCE:
        return '💰';
      case PrizeWheelErrorCode.ROOM_FULL:
        return '🚫';
      case PrizeWheelErrorCode.COLOR_ALREADY_SELECTED:
        return '🎨';
      default:
        return '⚠️';
    }
  }
}
```

This enhanced error handling system provides comprehensive coverage for all Prize Wheel scenarios with user-friendly messaging and automatic recovery options.

---

## Prize Pool Visualization

### Features
- **Growth Charts**: Real-time prize pool growth visualization
- **Potential Winnings Calculator**: Dynamic calculation with scenario simulation
- **House Edge Display**: Transparent house edge breakdown
- **Winner Announcements**: Animated winner celebrations

### Prize Pool Growth Chart

```typescript
interface PrizePoolDataPoint {
  timestamp: string;
  totalPool: number;
  playerCount: number;
  entryFee: number;
}

const PrizePoolGrowthChart: React.FC<{
  data: PrizePoolDataPoint[];
  currentPool: number;
  maxPlayers: number;
}> = ({ data, currentPool, maxPlayers }) => {
  const [animatedPool, setAnimatedPool] = useState(0);

  // Animate pool value changes
  useEffect(() => {
    if (currentPool !== animatedPool) {
      const duration = 1000;
      const steps = 30;
      const stepValue = (currentPool - animatedPool) / steps;

      let currentStep = 0;
      const interval = setInterval(() => {
        currentStep++;
        setAnimatedPool(prev => prev + stepValue);

        if (currentStep >= steps) {
          setAnimatedPool(currentPool);
          clearInterval(interval);
        }
      }, duration / steps);

      return () => clearInterval(interval);
    }
  }, [currentPool, animatedPool]);

  return (
    <div className="prize-pool-chart">
      <div className="current-pool">
        <h3>${animatedPool.toFixed(2)}</h3>
        <p>Current Prize Pool</p>
      </div>

      <svg className="growth-chart" viewBox="0 0 300 150">
        {/* Chart implementation */}
      </svg>

      <div className="pool-statistics">
        <div>Players: {data[data.length - 1]?.playerCount || 0}/{maxPlayers}</div>
        <div>Entry Fee: ${data[data.length - 1]?.entryFee.toFixed(2) || '0.00'}</div>
      </div>
    </div>
  );
};
```

### Potential Winnings Calculator

```typescript
const PotentialWinningsCalculator: React.FC<{
  currentPool: number;
  playerCount: number;
  maxPlayers: number;
  entryFee: number;
}> = ({ currentPool, playerCount, maxPlayers, entryFee }) => {
  const [simulatedPlayers, setSimulatedPlayers] = useState(playerCount);

  const calculateScenario = (players: number) => {
    const totalPool = players * entryFee;
    const houseEdge = 0.05; // 5%
    const netPrize = totalPool * (1 - houseEdge);
    const probability = 1 / players;
    const multiplier = netPrize / entryFee;

    return { totalPool, netPrize, probability, multiplier };
  };

  const currentScenario = calculateScenario(playerCount);
  const simulatedScenario = calculateScenario(simulatedPlayers);

  return (
    <div className="winnings-calculator">
      <h3>Potential Winnings Calculator</h3>

      <div className="current-winnings">
        <h4>Your Potential Prize: ${currentScenario.netPrize.toFixed(2)}</h4>
        <p>Win Probability: {(currentScenario.probability * 100).toFixed(1)}%</p>
        <p>Return Multiplier: {currentScenario.multiplier.toFixed(2)}x</p>
      </div>

      <div className="scenario-simulator">
        <label>Simulate with different player counts:</label>
        <input
          type="range"
          min="1"
          max={maxPlayers}
          value={simulatedPlayers}
          onChange={(e) => setSimulatedPlayers(parseInt(e.target.value))}
        />
        <div>With {simulatedPlayers} players: ${simulatedScenario.netPrize.toFixed(2)}</div>
      </div>
    </div>
  );
};
```

### Winner Announcement Component

```typescript
const WinnerAnnouncement: React.FC<{
  winner: {
    userId: string;
    username: string;
    prizeAmount: number;
    selectedColor: string;
  };
  totalPool: number;
  participantCount: number;
  onClose: () => void;
}> = ({ winner, totalPool, participantCount, onClose }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    // Animation sequence
    setTimeout(() => setIsVisible(true), 100);
    setTimeout(() => setShowConfetti(true), 500);

    // Auto close
    setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 300);
    }, 8000);
  }, [onClose]);

  return (
    <div className={`winner-modal ${isVisible ? 'visible' : ''}`}>
      {showConfetti && <ConfettiAnimation />}

      <div className="winner-content">
        <div className="trophy-icon">🏆</div>
        <h1>🎉 WINNER! 🎉</h1>

        <div className="winner-info">
          <h2>{winner.username}</h2>
          <div className="winning-color" style={{ backgroundColor: winner.selectedColor }}>
            {winner.selectedColor}
          </div>
        </div>

        <div className="prize-amount">
          <h3>${winner.prizeAmount.toFixed(2)}</h3>
          <p>From a total pool of ${totalPool.toFixed(2)}</p>
        </div>

        <div className="game-stats">
          <div>Players: {participantCount}</div>
          <div>Winning Color: {winner.selectedColor}</div>
        </div>

        <button onClick={onClose} className="continue-button">
          Continue Playing
        </button>
      </div>
    </div>
  );
};
```

---

## Entry Fee Processing

### Features
- **Pre-validation**: Balance checking before actions
- **Automatic Processing**: Seamless entry fee handling
- **Refund Management**: Automatic refunds on player exit
- **Transaction Tracking**: Complete audit trail

### Entry Fee Hook

```typescript
export const useEntryFeeProcessing = (roomId: string, entryFeeAmount: number) => {
  const [status, setStatus] = useState<EntryFeeStatus>('not_required');
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const processEntryFee = async (): Promise<boolean> => {
    if (processing) return false;

    setProcessing(true);
    setError(null);

    try {
      const result = await apiClient.processEntryFee({
        user_id: userId,
        room_id: roomId,
        bet_amount: entryFeeAmount,
        metadata: {
          game_type: 'prize_wheel',
          timestamp: new Date().toISOString(),
        },
      });

      if (result.success) {
        setStatus('paid');
        toast.success(`Entry fee paid: $${entryFeeAmount.toFixed(2)}`);
        return true;
      } else {
        setStatus('failed');
        setError(result.error_message || 'Entry fee processing failed');
        return false;
      }
    } catch (error) {
      setStatus('failed');
      setError(error instanceof Error ? error.message : 'Processing failed');
      return false;
    } finally {
      setProcessing(false);
    }
  };

  const processRefund = async (): Promise<boolean> => {
    if (processing || status !== 'paid') return false;

    setProcessing(true);

    try {
      const result = await apiClient.processRefund({
        user_id: userId,
        room_id: roomId,
        bet_amount: entryFeeAmount,
        metadata: {
          reason: 'player_unready',
          timestamp: new Date().toISOString(),
        },
      });

      if (result.success) {
        setStatus('refunded');
        toast.success(`Entry fee refunded: $${result.refunded_amount.toFixed(2)}`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Refund processing failed:', error);
      return false;
    } finally {
      setProcessing(false);
    }
  };

  return {
    status,
    processing,
    error,
    processEntryFee,
    processRefund,
    isPaid: status === 'paid',
    isRequired: entryFeeAmount > 0,
    canPay: status === 'required' || status === 'failed',
    canRefund: status === 'paid' && !processing,
  };
};
```

---

## Custom React Hooks

### Comprehensive Integration Hook

```typescript
export const usePrizeWheelIntegration = (roomId: string, betAmount: number) => {
  const prizePool = usePrizePoolData(roomId);
  const balance = useRealTimeBalance();
  const entryFee = useEntryFeeProcessing(roomId, betAmount);
  const errorHandler = usePrizeWheelErrorHandler();

  const validateParticipation = useCallback(() => {
    const blockers: string[] = [];

    if (!user) blockers.push('Must be logged in');
    if (balance.hasInsufficientBalance(betAmount)) {
      blockers.push(`Insufficient balance (need $${balance.getShortfall(betAmount).toFixed(2)} more)`);
    }
    if (!entryFee.isPaid && entryFee.isRequired) {
      blockers.push('Entry fee must be paid');
    }
    if (prizePool.currentPlayers >= prizePool.maxPlayers) {
      blockers.push('Room is full');
    }

    return {
      canParticipate: blockers.length === 0,
      blockers,
    };
  }, [user, balance, entryFee, prizePool, betAmount]);

  const prepareForGame = useCallback(async (): Promise<boolean> => {
    try {
      const validation = validateParticipation();
      if (!validation.canParticipate) return false;

      if (entryFee.isRequired && !entryFee.isPaid) {
        const feeProcessed = await entryFee.processEntryFee();
        if (!feeProcessed) return false;
      }

      const balanceValid = await balance.validateBalance(betAmount);
      return balanceValid;
    } catch (error) {
      errorHandler.handleError(error, { context: 'prepare_for_game' });
      return false;
    }
  }, [validateParticipation, entryFee, balance, betAmount, errorHandler]);

  return {
    prizePool,
    balance,
    entryFee,
    canParticipate: validateParticipation().canParticipate,
    participationBlockers: validateParticipation().blockers,
    actions: {
      prepareForGame,
      validateParticipation,
    },
  };
};
```

This comprehensive hook provides a single interface for all Prize Wheel functionality, making integration simple and consistent across components.
