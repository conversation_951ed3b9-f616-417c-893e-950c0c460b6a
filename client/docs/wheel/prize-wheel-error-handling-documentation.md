# Prize Wheel Error Handling Documentation

## Overview

This comprehensive documentation covers the error handling system implemented in the Prize Wheel application, including error codes, handling patterns, recovery strategies, and troubleshooting guides.

## Table of Contents

1. [Error Code System](#error-code-system)
2. [Error Handling Architecture](#error-handling-architecture)
3. [Client-Side Error Handling](#client-side-error-handling)
4. [API Error Responses](#api-error-responses)
5. [Socket Error Handling](#socket-error-handling)
6. [Recovery Strategies](#recovery-strategies)
7. [Troubleshooting Guide](#troubleshooting-guide)
8. [Monitoring and Logging](#monitoring-and-logging)

---

## Error Code System

### Prize Wheel Error Codes

```typescript
enum PrizeWheelErrorCode {
  // Balance & Payment Errors
  INSUFFICIENT_BALANCE = 'INSUFFICIENT_BALANCE',
  BALANCE_VALIDATION_FAILED = 'BALANCE_VALIDATION_FAILED',
  ENTRY_FEE_PROCESSING_FAILED = 'ENTRY_FEE_PROCESSING_FAILED',
  REFUND_PROCESSING_FAILED = 'REFUND_PROCESSING_FAILED',
  PAYMENT_GATEWAY_ERROR = 'PAYMENT_GATEWAY_ERROR',

  // Prize Pool Errors
  PRIZE_POOL_NOT_FOUND = 'PRIZE_POOL_NOT_FOUND',
  PRIZE_POOL_LOCKED = 'PRIZE_POOL_LOCKED',
  PRIZE_POOL_ALREADY_DISTRIBUTED = 'PRIZE_POOL_ALREADY_DISTRIBUTED',
  PRIZE_DISTRIBUTION_FAILED = 'PRIZE_DISTRIBUTION_FAILED',
  INVALID_PRIZE_POOL_STATE = 'INVALID_PRIZE_POOL_STATE',

  // Room & Game Errors
  ROOM_NOT_FOUND = 'ROOM_NOT_FOUND',
  ROOM_FULL = 'ROOM_FULL',
  ROOM_CLOSED = 'ROOM_CLOSED',
  GAME_IN_PROGRESS = 'GAME_IN_PROGRESS',
  GAME_NOT_STARTED = 'GAME_NOT_STARTED',
  INVALID_GAME_STATE = 'INVALID_GAME_STATE',

  // Player Errors
  PLAYER_NOT_FOUND = 'PLAYER_NOT_FOUND',
  PLAYER_ALREADY_READY = 'PLAYER_ALREADY_READY',
  PLAYER_NOT_READY = 'PLAYER_NOT_READY',
  COLOR_ALREADY_SELECTED = 'COLOR_ALREADY_SELECTED',
  INVALID_COLOR_SELECTION = 'INVALID_COLOR_SELECTION',
  PLAYER_NOT_IN_ROOM = 'PLAYER_NOT_IN_ROOM',

  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  SESSION_EXPIRED = 'SESSION_EXPIRED',

  // System Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_TIMEOUT = 'API_TIMEOUT',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  DATABASE_ERROR = 'DATABASE_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}
```

### Error Severity Levels

```typescript
enum ErrorSeverity {
  LOW = 'low',           // Minor issues, user can continue
  MEDIUM = 'medium',     // Moderate issues, some functionality affected
  HIGH = 'high',         // Major issues, significant functionality lost
  CRITICAL = 'critical', // Critical issues, system unusable
}

interface ErrorSeverityMapping {
  [key: string]: ErrorSeverity;
}

const ERROR_SEVERITY_MAP: ErrorSeverityMapping = {
  [PrizeWheelErrorCode.COLOR_ALREADY_SELECTED]: ErrorSeverity.LOW,
  [PrizeWheelErrorCode.ROOM_FULL]: ErrorSeverity.LOW,
  [PrizeWheelErrorCode.INSUFFICIENT_BALANCE]: ErrorSeverity.MEDIUM,
  [PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED]: ErrorSeverity.MEDIUM,
  [PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND]: ErrorSeverity.HIGH,
  [PrizeWheelErrorCode.UNAUTHORIZED]: ErrorSeverity.HIGH,
  [PrizeWheelErrorCode.SERVICE_UNAVAILABLE]: ErrorSeverity.CRITICAL,
  [PrizeWheelErrorCode.DATABASE_ERROR]: ErrorSeverity.CRITICAL,
};
```

---

## Error Handling Architecture

### Error Interface Structure

```typescript
interface PrizeWheelError {
  code: PrizeWheelErrorCode;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
  context?: string;
  severity?: ErrorSeverity;
  recoverable?: boolean;
  retryable?: boolean;
}

interface ErrorContext {
  userId?: string;
  roomId?: string;
  action?: string;
  component?: string;
  apiEndpoint?: string;
  socketEvent?: string;
}
```

### Error Handler Class

```typescript
export class PrizeWheelErrorHandler {
  private static instance: PrizeWheelErrorHandler;

  static getInstance(): PrizeWheelErrorHandler {
    if (!this.instance) {
      this.instance = new PrizeWheelErrorHandler();
    }
    return this.instance;
  }

  handleApiError(error: any, context?: string): PrizeWheelError {
    const errorCode = this.mapErrorCode(error);
    const severity = this.getErrorSeverity(errorCode);
    const userMessage = this.getUserFriendlyMessage(errorCode, error);
    
    const prizeWheelError: PrizeWheelError = {
      code: errorCode,
      message: userMessage,
      details: this.extractErrorDetails(error),
      timestamp: new Date().toISOString(),
      context,
      severity,
      recoverable: this.isRecoverable(errorCode),
      retryable: this.isRetryable(errorCode),
    };

    this.logError(prizeWheelError);
    this.reportError(prizeWheelError);
    
    return prizeWheelError;
  }

  private mapErrorCode(error: any): PrizeWheelErrorCode {
    // Handle axios error format
    if (error.response?.data?.error_code) {
      return this.normalizeErrorCode(error.response.data.error_code);
    }

    // Handle direct error object
    if (error.code) {
      return this.normalizeErrorCode(error.code);
    }

    // Infer from error message
    return this.inferErrorCodeFromMessage(error.message || '');
  }

  private normalizeErrorCode(code: string): PrizeWheelErrorCode {
    // Map backend error codes to client error codes
    const codeMap: Record<string, PrizeWheelErrorCode> = {
      'INSUFFICIENT_BALANCE': PrizeWheelErrorCode.INSUFFICIENT_BALANCE,
      'PRIZE_POOL_NOT_FOUND': PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND,
      'ENTRY_FEE_PROCESSING_FAILED': PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED,
      'ROOM_NOT_FOUND': PrizeWheelErrorCode.ROOM_NOT_FOUND,
      'UNAUTHORIZED': PrizeWheelErrorCode.UNAUTHORIZED,
      'FORBIDDEN': PrizeWheelErrorCode.FORBIDDEN,
      'VALIDATION_ERROR': PrizeWheelErrorCode.VALIDATION_ERROR,
      // Add more mappings as needed
    };

    return codeMap[code] || PrizeWheelErrorCode.UNKNOWN_ERROR;
  }

  private inferErrorCodeFromMessage(message: string): PrizeWheelErrorCode {
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('insufficient balance')) {
      return PrizeWheelErrorCode.INSUFFICIENT_BALANCE;
    }
    if (lowerMessage.includes('timeout') || lowerMessage.includes('timed out')) {
      return PrizeWheelErrorCode.API_TIMEOUT;
    }
    if (lowerMessage.includes('network') || lowerMessage.includes('connection')) {
      return PrizeWheelErrorCode.NETWORK_ERROR;
    }
    if (lowerMessage.includes('unauthorized') || lowerMessage.includes('not authorized')) {
      return PrizeWheelErrorCode.UNAUTHORIZED;
    }
    if (lowerMessage.includes('forbidden') || lowerMessage.includes('access denied')) {
      return PrizeWheelErrorCode.FORBIDDEN;
    }
    if (lowerMessage.includes('validation') || lowerMessage.includes('invalid')) {
      return PrizeWheelErrorCode.VALIDATION_ERROR;
    }
    if (lowerMessage.includes('room not found')) {
      return PrizeWheelErrorCode.ROOM_NOT_FOUND;
    }
    if (lowerMessage.includes('prize pool not found')) {
      return PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND;
    }

    return PrizeWheelErrorCode.UNKNOWN_ERROR;
  }

  getUserFriendlyMessage(errorCode: PrizeWheelErrorCode, error: any): string {
    const details = error.details || {};

    switch (errorCode) {
      case PrizeWheelErrorCode.INSUFFICIENT_BALANCE:
        if (details.currentBalance && details.requiredAmount) {
          const shortfall = details.requiredAmount - details.currentBalance;
          return `Insufficient balance. You need $${details.requiredAmount.toFixed(2)} but only have $${details.currentBalance.toFixed(2)} (short by $${shortfall.toFixed(2)})`;
        }
        return "You don't have enough balance to complete this action";

      case PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED:
        return "Failed to process entry fee. Please check your balance and try again";

      case PrizeWheelErrorCode.REFUND_PROCESSING_FAILED:
        return "Failed to process refund. Please contact support if this persists";

      case PrizeWheelErrorCode.PRIZE_POOL_NOT_FOUND:
        return "Prize pool not found. The room may have been closed or deleted";

      case PrizeWheelErrorCode.ROOM_NOT_FOUND:
        return "Room not found. It may have been closed or deleted";

      case PrizeWheelErrorCode.ROOM_FULL:
        return "Room is full. Please try another room";

      case PrizeWheelErrorCode.COLOR_ALREADY_SELECTED:
        return "This color has already been selected by another player";

      case PrizeWheelErrorCode.GAME_IN_PROGRESS:
        return "Game is already in progress. Please wait for the next round";

      case PrizeWheelErrorCode.UNAUTHORIZED:
        return "You are not authorized to perform this action. Please log in again";

      case PrizeWheelErrorCode.TOKEN_EXPIRED:
        return "Your session has expired. Please log in again";

      case PrizeWheelErrorCode.NETWORK_ERROR:
        return "Network connection error. Please check your internet connection";

      case PrizeWheelErrorCode.API_TIMEOUT:
        return "Request timed out. Please try again";

      case PrizeWheelErrorCode.SERVICE_UNAVAILABLE:
        return "Service is temporarily unavailable. Please try again later";

      case PrizeWheelErrorCode.VALIDATION_ERROR:
        return error.message || "Invalid input data";

      default:
        return error.message || "An unexpected error occurred";
    }
  }

  private getErrorSeverity(errorCode: PrizeWheelErrorCode): ErrorSeverity {
    return ERROR_SEVERITY_MAP[errorCode] || ErrorSeverity.MEDIUM;
  }

  private isRecoverable(errorCode: PrizeWheelErrorCode): boolean {
    const nonRecoverableErrors = [
      PrizeWheelErrorCode.UNAUTHORIZED,
      PrizeWheelErrorCode.FORBIDDEN,
      PrizeWheelErrorCode.TOKEN_EXPIRED,
      PrizeWheelErrorCode.DATABASE_ERROR,
    ];

    return !nonRecoverableErrors.includes(errorCode);
  }

  private isRetryable(errorCode: PrizeWheelErrorCode): boolean {
    const retryableErrors = [
      PrizeWheelErrorCode.NETWORK_ERROR,
      PrizeWheelErrorCode.API_TIMEOUT,
      PrizeWheelErrorCode.SERVICE_UNAVAILABLE,
      PrizeWheelErrorCode.ENTRY_FEE_PROCESSING_FAILED,
      PrizeWheelErrorCode.BALANCE_VALIDATION_FAILED,
    ];

    return retryableErrors.includes(errorCode);
  }

  async handleApiErrorWithRetry<T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000,
    options: {
      context?: string;
      showToast?: boolean;
      exponentialBackoff?: boolean;
    } = {}
  ): Promise<T> {
    let lastError: any;
    const { exponentialBackoff = true } = options;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await apiCall();
      } catch (error) {
        lastError = error;
        
        const prizeWheelError = this.handleApiError(error, options.context);
        
        // Don't retry if error is not retryable
        if (!prizeWheelError.retryable || attempt === maxRetries) {
          break;
        }

        // Calculate delay with exponential backoff
        const delay = exponentialBackoff 
          ? baseDelay * Math.pow(2, attempt)
          : baseDelay;

        console.log(`Attempt ${attempt + 1} failed, retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    const finalError = this.handleApiError(lastError, options.context);
    
    if (options.showToast !== false) {
      this.showErrorToast(finalError);
    }

    throw finalError;
  }

  showErrorToast(error: PrizeWheelError): void {
    const duration = this.getToastDuration(error.code);
    const icon = this.getErrorIcon(error.code);

    toast.error(error.message, {
      duration,
      icon,
      style: { maxWidth: '500px' },
      className: `error-toast severity-${error.severity}`,
    });
  }

  private getToastDuration(errorCode: PrizeWheelErrorCode): number {
    const severity = this.getErrorSeverity(errorCode);
    
    switch (severity) {
      case ErrorSeverity.LOW:
        return 3000;
      case ErrorSeverity.MEDIUM:
        return 5000;
      case ErrorSeverity.HIGH:
        return 8000;
      case ErrorSeverity.CRITICAL:
        return 10000;
      default:
        return 5000;
    }
  }

  private getErrorIcon(errorCode: PrizeWheelErrorCode): string {
    switch (errorCode) {
      case PrizeWheelErrorCode.INSUFFICIENT_BALANCE:
        return '💰';
      case PrizeWheelErrorCode.ROOM_FULL:
        return '🚫';
      case PrizeWheelErrorCode.COLOR_ALREADY_SELECTED:
        return '🎨';
      case PrizeWheelErrorCode.NETWORK_ERROR:
        return '🌐';
      case PrizeWheelErrorCode.UNAUTHORIZED:
        return '🔒';
      case PrizeWheelErrorCode.SERVICE_UNAVAILABLE:
        return '⚠️';
      default:
        return '❌';
    }
  }

  private extractErrorDetails(error: any): Record<string, any> {
    return {
      originalMessage: error.message,
      statusCode: error.response?.status,
      responseData: error.response?.data,
      stack: error.stack,
      ...error.details,
    };
  }

  private logError(error: PrizeWheelError): void {
    const logLevel = this.getLogLevel(error.severity!);
    
    console[logLevel]('Prize Wheel Error:', {
      code: error.code,
      message: error.message,
      details: error.details,
      timestamp: error.timestamp,
      context: error.context,
      severity: error.severity,
    });
  }

  private getLogLevel(severity: ErrorSeverity): 'log' | 'warn' | 'error' {
    switch (severity) {
      case ErrorSeverity.LOW:
        return 'log';
      case ErrorSeverity.MEDIUM:
        return 'warn';
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return 'error';
      default:
        return 'warn';
    }
  }

  private reportError(error: PrizeWheelError): void {
    // Report to error monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      // Send to error reporting service (e.g., Sentry, Bugsnag)
      // errorReportingService.captureException(error);
    }
  }
}
```

---

## Client-Side Error Handling

### React Hook for Error Handling

```typescript
// src/hooks/usePrizeWheelErrorHandler.ts
import { useMemo } from 'react';
import { PrizeWheelErrorHandler } from '../utils/prizeWheelErrorHandler';

export const usePrizeWheelErrorHandler = () => {
  const errorHandler = useMemo(() => PrizeWheelErrorHandler.getInstance(), []);

  const handleBalanceError = (error: any, currentBalance: number, requiredAmount: number) => {
    return errorHandler.handleApiError(error, 'balance_validation');
  };

  const handleEntryFeeError = (error: any, userId: string, roomId: string, amount: number) => {
    return errorHandler.handleApiError(error, 'entry_fee_processing');
  };

  const handlePrizePoolError = (error: any, userId?: string, roomId?: string) => {
    return errorHandler.handleApiError(error, 'prize_pool_management');
  };

  const handleSocketError = (error: any, eventName: string, roomId?: string) => {
    return errorHandler.handleApiError(error, `socket_event_${eventName}`);
  };

  const handleApiErrorWithRetry = async <T>(
    apiCall: () => Promise<T>,
    maxRetries: number = 3,
    retryDelay: number = 1000,
    options: { context?: string; showToast?: boolean } = {}
  ): Promise<T> => {
    return errorHandler.handleApiErrorWithRetry(apiCall, maxRetries, retryDelay, options);
  };

  const isErrorType = (error: any, errorCode: string): boolean => {
    return error?.code === errorCode;
  };

  return {
    handleBalanceError,
    handleEntryFeeError,
    handlePrizePoolError,
    handleSocketError,
    handleApiErrorWithRetry,
    isErrorType,
    handleError: errorHandler.handleApiError.bind(errorHandler),
  };
};
```

### Error Boundary Component

```typescript
// src/components/ErrorBoundary.tsx
import React, { Component, ErrorInfo, ReactNode } from 'react';
import { PrizeWheelErrorHandler } from '../utils/prizeWheelErrorHandler';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class PrizeWheelErrorBoundary extends Component<Props, State> {
  private errorHandler: PrizeWheelErrorHandler;

  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
    this.errorHandler = PrizeWheelErrorHandler.getInstance();
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error, errorInfo: null };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });

    // Handle error with our error handler
    const prizeWheelError = this.errorHandler.handleApiError(error, 'react_error_boundary');

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    console.error('Error caught by Prize Wheel Error Boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="error-boundary-container">
          <div className="error-boundary-content">
            <h2>🎰 Something went wrong with the Prize Wheel</h2>
            <p>We're sorry, but something unexpected happened. Please try refreshing the page.</p>

            <div className="error-actions">
              <button
                onClick={() => window.location.reload()}
                className="error-button primary"
              >
                Refresh Page
              </button>

              <button
                onClick={() => this.setState({ hasError: false, error: null, errorInfo: null })}
                className="error-button secondary"
              >
                Try Again
              </button>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="error-details">
                <summary>Error Details (Development)</summary>
                <pre>{this.state.error.toString()}</pre>
                <pre>{this.state.errorInfo?.componentStack}</pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### Component-Level Error Handling

```typescript
// Example: PrizePoolDisplay with error handling
import React, { useState, useEffect } from 'react';
import { usePrizeWheelErrorHandler } from '../../hooks/usePrizeWheelErrorHandler';

export const PrizePoolDisplay: React.FC<{ roomId: string }> = ({ roomId }) => {
  const [prizePool, setPrizePool] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { handlePrizePoolError, handleApiErrorWithRetry } = usePrizeWheelErrorHandler();

  useEffect(() => {
    const loadPrizePool = async () => {
      try {
        setLoading(true);
        setError(null);

        const data = await handleApiErrorWithRetry(
          () => apiClient.getRoomPrizePool(roomId),
          3,
          1000,
          { context: 'prize_pool_display_load' }
        );

        setPrizePool(data);
      } catch (err) {
        const prizeWheelError = handlePrizePoolError(err, undefined, roomId);
        setError(prizeWheelError.message);
      } finally {
        setLoading(false);
      }
    };

    if (roomId) {
      loadPrizePool();
    }
  }, [roomId, handlePrizePoolError, handleApiErrorWithRetry]);

  if (loading) {
    return <div className="prize-pool-loading">Loading prize pool...</div>;
  }

  if (error) {
    return (
      <div className="prize-pool-error">
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="prize-pool-display">
      {/* Prize pool content */}
    </div>
  );
};
```

---

## API Error Responses

### Standard Error Response Format

```typescript
interface ApiErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: Record<string, any>;
    timestamp: string;
    request_id?: string;
  };
}
```

### Common API Error Examples

#### Insufficient Balance Error
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_BALANCE",
    "message": "Insufficient balance for entry fee",
    "details": {
      "user_id": "user123",
      "current_balance": 5.00,
      "required_amount": 10.00,
      "shortfall": 5.00
    },
    "timestamp": "2024-12-18T10:30:00Z",
    "request_id": "req_abc123"
  }
}
```

#### Prize Pool Not Found Error
```json
{
  "success": false,
  "error": {
    "code": "PRIZE_POOL_NOT_FOUND",
    "message": "Prize pool not found for room",
    "details": {
      "room_id": "room123",
      "requested_at": "2024-12-18T10:30:00Z"
    },
    "timestamp": "2024-12-18T10:30:00Z",
    "request_id": "req_def456"
  }
}
```

#### Entry Fee Processing Failed Error
```json
{
  "success": false,
  "error": {
    "code": "ENTRY_FEE_PROCESSING_FAILED",
    "message": "Failed to process entry fee payment",
    "details": {
      "user_id": "user123",
      "room_id": "room123",
      "bet_amount": 10.00,
      "failure_reason": "Payment gateway timeout",
      "retry_possible": true
    },
    "timestamp": "2024-12-18T10:30:00Z",
    "request_id": "req_ghi789"
  }
}
```

#### Validation Error
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": {
      "validation_errors": [
        {
          "field": "bet_amount",
          "message": "Must be between 0.01 and 1000.00"
        },
        {
          "field": "room_id",
          "message": "Room ID is required"
        }
      ]
    },
    "timestamp": "2024-12-18T10:30:00Z",
    "request_id": "req_jkl012"
  }
}
```

---

## Socket Error Handling

### Socket Error Events

```typescript
// Socket error event handlers
socket.on('error', (error) => {
  const prizeWheelError = errorHandler.handleApiError(error, 'socket_general_error');
  console.error('Socket error:', prizeWheelError);
});

socket.on('connect_error', (error) => {
  const prizeWheelError = errorHandler.handleApiError(error, 'socket_connection_error');

  if (prizeWheelError.code === PrizeWheelErrorCode.UNAUTHORIZED) {
    // Redirect to login
    window.location.href = '/login';
  } else {
    // Show connection error message
    toast.error('Connection failed. Retrying...');
  }
});

socket.on('disconnect', (reason) => {
  console.log('Socket disconnected:', reason);

  if (reason === 'io server disconnect') {
    // Server disconnected, try to reconnect
    setTimeout(() => {
      socket.connect();
    }, 1000);
  }
});

// Prize Wheel specific error events
socket.on('prize_wheel_error', (data) => {
  const prizeWheelError = errorHandler.handleApiError(data, 'prize_wheel_socket_error');
  errorHandler.showErrorToast(prizeWheelError);
});

socket.on('entry_fee_error', (data) => {
  const prizeWheelError = errorHandler.handleApiError(data, 'entry_fee_socket_error');
  errorHandler.showErrorToast(prizeWheelError);
});
```

### Socket Reconnection Logic

```typescript
class SocketManager {
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  private handleReconnection(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);

      setTimeout(() => {
        this.socket?.connect();
      }, delay);
    } else {
      console.error('Max reconnection attempts reached');
      toast.error('Unable to connect to game server. Please refresh the page.');
    }
  }

  private resetReconnectionState(): void {
    this.reconnectAttempts = 0;
  }
}
```

---

## Recovery Strategies

### Automatic Recovery Patterns

```typescript
// Recovery strategy interface
interface RecoveryStrategy {
  canRecover(error: PrizeWheelError): boolean;
  recover(error: PrizeWheelError, context?: any): Promise<boolean>;
}

// Balance recovery strategy
class BalanceRecoveryStrategy implements RecoveryStrategy {
  canRecover(error: PrizeWheelError): boolean {
    return error.code === PrizeWheelErrorCode.INSUFFICIENT_BALANCE;
  }

  async recover(error: PrizeWheelError, context?: any): Promise<boolean> {
    // Refresh balance and retry
    try {
      await apiClient.refreshUserBalance();
      return true;
    } catch {
      return false;
    }
  }
}

// Token refresh recovery strategy
class TokenRefreshRecoveryStrategy implements RecoveryStrategy {
  canRecover(error: PrizeWheelError): boolean {
    return error.code === PrizeWheelErrorCode.TOKEN_EXPIRED;
  }

  async recover(error: PrizeWheelError): Promise<boolean> {
    try {
      const newToken = await AuthManager.refreshToken();
      return !!newToken;
    } catch {
      // Redirect to login
      window.location.href = '/login';
      return false;
    }
  }
}

// Network recovery strategy
class NetworkRecoveryStrategy implements RecoveryStrategy {
  canRecover(error: PrizeWheelError): boolean {
    return error.code === PrizeWheelErrorCode.NETWORK_ERROR;
  }

  async recover(error: PrizeWheelError): Promise<boolean> {
    // Wait and check network connectivity
    await new Promise(resolve => setTimeout(resolve, 2000));

    try {
      await fetch('/api/health');
      return true;
    } catch {
      return false;
    }
  }
}
```

### Recovery Manager

```typescript
class RecoveryManager {
  private strategies: RecoveryStrategy[] = [
    new BalanceRecoveryStrategy(),
    new TokenRefreshRecoveryStrategy(),
    new NetworkRecoveryStrategy(),
  ];

  async attemptRecovery(error: PrizeWheelError, context?: any): Promise<boolean> {
    for (const strategy of this.strategies) {
      if (strategy.canRecover(error)) {
        try {
          const recovered = await strategy.recover(error, context);
          if (recovered) {
            console.log(`Successfully recovered from error: ${error.code}`);
            return true;
          }
        } catch (recoveryError) {
          console.error(`Recovery failed for ${error.code}:`, recoveryError);
        }
      }
    }

    return false;
  }
}
```

---

## Troubleshooting Guide

### Common Issues and Solutions

#### 1. "INSUFFICIENT_BALANCE" Error
**Symptoms:**
- User cannot pay entry fee
- Balance validation fails
- Error message shows shortfall amount

**Diagnosis:**
```bash
# Check user balance
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:3002/api/users/USER_ID/balance

# Validate balance for specific amount
curl -X POST -H "Authorization: Bearer TOKEN" \
  -d '{"user_id":"USER_ID","bet_amount":10.00}' \
  http://localhost:3002/api/v1/entry_fees/validate_balance
```

**Solutions:**
1. Refresh user balance from database
2. Check for pending transactions
3. Verify balance calculation logic
4. Clear cached balance data

#### 2. "ENTRY_FEE_PROCESSING_FAILED" Error
**Symptoms:**
- Entry fee payment fails
- Transaction remains in pending state
- User balance not deducted

**Diagnosis:**
```bash
# Check transaction status
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:3002/api/transactions/TRANSACTION_ID

# Check service health
curl http://localhost:3002/health
```

**Solutions:**
1. Retry payment processing
2. Check payment gateway connectivity
3. Verify transaction service status
4. Manual transaction reconciliation

#### 3. "PRIZE_POOL_NOT_FOUND" Error
**Symptoms:**
- Prize pool data not loading
- Room shows no prize pool information
- API returns 404 for prize pool

**Diagnosis:**
```bash
# Check if room exists
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:3002/api/rooms/ROOM_ID

# Check prize pool creation
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:3002/api/rooms/ROOM_ID/prize-pool
```

**Solutions:**
1. Initialize prize pool for room
2. Check room status and configuration
3. Verify prize pool service connectivity
4. Clear prize pool cache

#### 4. Socket Connection Issues
**Symptoms:**
- Real-time updates not working
- Connection timeouts
- Frequent disconnections

**Diagnosis:**
```bash
# Test socket connectivity
wscat -c ws://localhost:3001 -H "Authorization: Bearer TOKEN"

# Check socket gateway health
curl http://localhost:3001/health
```

**Solutions:**
1. Check network connectivity
2. Verify authentication token
3. Restart socket gateway service
4. Clear browser cache and cookies

### Debug Commands

```bash
# Service health checks
curl http://localhost:3002/health  # Manager Service
curl http://localhost:3001/health  # Socket Gateway
curl http://localhost:3003/health  # Game Service

# Database connectivity
mongo --eval "db.stats()"

# Redis connectivity
redis-cli ping

# View service logs
docker logs xzgame-manager-service --tail 100
docker logs xzgame-socket-gateway --tail 100

# Monitor real-time events
wscat -c ws://localhost:3001 -H "Authorization: Bearer TOKEN"

# Test API endpoints
curl -X POST -H "Authorization: Bearer TOKEN" \
  -d '{"user_id":"test","bet_amount":10.00}' \
  http://localhost:3002/api/v1/entry_fees/validate_balance
```

### Error Monitoring Dashboard

```typescript
// Error monitoring component
const ErrorMonitoringDashboard: React.FC = () => {
  const [errorStats, setErrorStats] = useState({
    totalErrors: 0,
    errorsByCode: {},
    errorsByTime: [],
    criticalErrors: 0,
  });

  useEffect(() => {
    // Subscribe to error events
    const errorHandler = PrizeWheelErrorHandler.getInstance();

    // Override logError to capture statistics
    const originalLogError = errorHandler.logError;
    errorHandler.logError = (error) => {
      originalLogError.call(errorHandler, error);

      // Update error statistics
      setErrorStats(prev => ({
        ...prev,
        totalErrors: prev.totalErrors + 1,
        errorsByCode: {
          ...prev.errorsByCode,
          [error.code]: (prev.errorsByCode[error.code] || 0) + 1,
        },
        criticalErrors: error.severity === 'critical'
          ? prev.criticalErrors + 1
          : prev.criticalErrors,
      }));
    };
  }, []);

  return (
    <div className="error-monitoring-dashboard">
      <h2>Error Monitoring</h2>

      <div className="error-stats">
        <div className="stat-card">
          <h3>Total Errors</h3>
          <div className="stat-value">{errorStats.totalErrors}</div>
        </div>

        <div className="stat-card critical">
          <h3>Critical Errors</h3>
          <div className="stat-value">{errorStats.criticalErrors}</div>
        </div>
      </div>

      <div className="error-breakdown">
        <h3>Errors by Type</h3>
        {Object.entries(errorStats.errorsByCode).map(([code, count]) => (
          <div key={code} className="error-type">
            <span>{code}</span>
            <span>{count}</span>
          </div>
        ))}
      </div>
    </div>
  );
};
```

---

## Monitoring and Logging

### Error Metrics Collection

```typescript
interface ErrorMetrics {
  errorCount: number;
  errorRate: number;
  errorsByCode: Record<string, number>;
  errorsBySeverity: Record<string, number>;
  averageResolutionTime: number;
  retrySuccessRate: number;
}

class ErrorMetricsCollector {
  private metrics: ErrorMetrics = {
    errorCount: 0,
    errorRate: 0,
    errorsByCode: {},
    errorsBySeverity: {},
    averageResolutionTime: 0,
    retrySuccessRate: 0,
  };

  recordError(error: PrizeWheelError): void {
    this.metrics.errorCount++;
    this.metrics.errorsByCode[error.code] = (this.metrics.errorsByCode[error.code] || 0) + 1;
    this.metrics.errorsBySeverity[error.severity!] = (this.metrics.errorsBySeverity[error.severity!] || 0) + 1;
  }

  recordRetrySuccess(): void {
    // Update retry success rate
  }

  getMetrics(): ErrorMetrics {
    return { ...this.metrics };
  }
}
```

### Production Error Reporting

```typescript
// Error reporting service integration
class ErrorReportingService {
  static captureException(error: PrizeWheelError, context?: any): void {
    if (process.env.NODE_ENV === 'production') {
      // Send to Sentry, Bugsnag, or other error reporting service
      // Sentry.captureException(error, {
      //   tags: {
      //     errorCode: error.code,
      //     severity: error.severity,
      //   },
      //   extra: {
      //     context: error.context,
      //     details: error.details,
      //   },
      // });
    }
  }

  static captureMessage(message: string, level: 'info' | 'warning' | 'error'): void {
    if (process.env.NODE_ENV === 'production') {
      // Sentry.captureMessage(message, level);
    }
  }
}
```

This comprehensive error handling documentation provides complete coverage of error management, recovery strategies, troubleshooting guides, and monitoring for the Prize Wheel application.
