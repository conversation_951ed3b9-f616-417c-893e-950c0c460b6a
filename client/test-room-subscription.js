/**
 * Test script to verify room subscription functionality
 * This script tests that after joining a room successfully, 
 * the subscribe_room event is sent with the room ID
 */

const io = require('socket.io-client');

// Configuration
const SERVER_URL = 'http://localhost:3001'; // Adjust if your server runs on a different port
const TEST_ROOM_ID = 'test-room-123';
const TEST_USER = {
  id: 'test-user-123',
  username: 'TestUser',
  email: '<EMAIL>'
};

let socket;
let testResults = {
  connected: false,
  authenticated: false,
  roomJoined: false,
  roomSubscribed: false,
  subscribeRoomEventSent: false
};

function log(message, data = null) {
  const timestamp = new Date().toISOString();
  console.log(`[${timestamp}] ${message}`);
  if (data) {
    console.log(JSON.stringify(data, null, 2));
  }
}

function runTest() {
  return new Promise((resolve, reject) => {
    log('🚀 Starting room subscription test...');
    
    // Connect to server
    socket = io(SERVER_URL, {
      transports: ['websocket'],
      timeout: 5000
    });

    // Connection events
    socket.on('connect', () => {
      log('✅ Connected to server');
      testResults.connected = true;
      
      // Authenticate (mock authentication)
      socket.emit('authenticate', {
        token: 'mock-jwt-token',
        user: TEST_USER
      });
    });

    socket.on('connect_error', (error) => {
      log('❌ Connection failed:', error.message);
      reject(error);
    });

    // Authentication events
    socket.on('authenticated', (data) => {
      log('✅ Authenticated successfully', data);
      testResults.authenticated = true;
      
      // Now try to join a room
      joinTestRoom();
    });

    socket.on('authentication_failed', (error) => {
      log('❌ Authentication failed:', error);
      reject(new Error('Authentication failed'));
    });

    // Room events
    socket.on('room_joined', (data) => {
      log('✅ Room joined successfully', data);
      testResults.roomJoined = true;
      
      // The key test: Check if subscribe_room event is sent automatically
      // We'll monitor for this by intercepting the emit method
    });

    socket.on('room_join_failed', (error) => {
      log('❌ Room join failed:', error);
      reject(new Error('Room join failed'));
    });

    // Subscription events
    socket.on('room_subscribed', (data) => {
      log('✅ Room subscription confirmed', data);
      testResults.roomSubscribed = true;
      
      // Test completed successfully
      setTimeout(() => {
        printTestResults();
        resolve(testResults);
      }, 1000);
    });

    socket.on('room_subscription_failed', (error) => {
      log('❌ Room subscription failed:', error);
      // Don't reject here as subscription might be optional
      setTimeout(() => {
        printTestResults();
        resolve(testResults);
      }, 1000);
    });

    // Monitor socket emissions to detect subscribe_room event
    const originalEmit = socket.emit.bind(socket);
    socket.emit = function(event, ...args) {
      if (event === 'subscribe_room') {
        log('🔔 subscribe_room event detected!', { event, args });
        testResults.subscribeRoomEventSent = true;
        
        // Verify the room ID is included
        if (args[0] && args[0].roomId === TEST_ROOM_ID) {
          log('✅ Correct room ID included in subscribe_room event');
        } else {
          log('⚠️ Room ID missing or incorrect in subscribe_room event');
        }
      }
      
      return originalEmit(event, ...args);
    };

    // Timeout for the test
    setTimeout(() => {
      log('⏰ Test timeout reached');
      printTestResults();
      resolve(testResults);
    }, 15000);
  });
}

function joinTestRoom() {
  log('🚪 Attempting to join test room:', TEST_ROOM_ID);
  
  socket.emit('join_room', {
    roomId: TEST_ROOM_ID,
    password: null, // No password for test room
    betAmount: 100
  });
}

function printTestResults() {
  log('\n📊 Test Results Summary:');
  log('========================');
  
  Object.entries(testResults).forEach(([key, value]) => {
    const status = value ? '✅' : '❌';
    log(`${status} ${key}: ${value}`);
  });
  
  const passedTests = Object.values(testResults).filter(Boolean).length;
  const totalTests = Object.keys(testResults).length;
  
  log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  
  if (testResults.subscribeRoomEventSent) {
    log('🎉 SUCCESS: subscribe_room event is being sent after room join!');
  } else {
    log('⚠️ WARNING: subscribe_room event was not detected');
  }
}

function cleanup() {
  if (socket) {
    socket.disconnect();
    log('🧹 Disconnected from server');
  }
}

// Run the test
runTest()
  .then((results) => {
    cleanup();
    process.exit(results.subscribeRoomEventSent ? 0 : 1);
  })
  .catch((error) => {
    log('❌ Test failed with error:', error.message);
    cleanup();
    process.exit(1);
  });

// Handle process termination
process.on('SIGINT', () => {
  log('🛑 Test interrupted');
  cleanup();
  process.exit(1);
});
