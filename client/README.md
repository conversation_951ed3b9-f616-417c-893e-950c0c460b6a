# XZ Game Client

A modern React client application for the XZ Game platform, featuring real-time gaming with WebSocket integration.

## 🚀 Features

### Core Functionality
- **Authentication System**: JWT-based login/register with automatic token refresh
- **Real-time Communication**: Socket.io integration for live game events
- **Game Room Management**: Create, join, and manage game rooms
- **Live Game Play**: Real-time game state updates and player interactions
- **User Profile Management**: View and edit user profiles and statistics

### Technical Features
- **Modern React**: Built with React 18, TypeScript, and Vite
- **State Management**: Zustand for efficient state management
- **Real-time Updates**: Socket.io client for WebSocket communication
- **HTTP Client**: Axios with automatic token refresh and error handling
- **Form Handling**: React Hook Form with Zod validation
- **Responsive Design**: Tailwind CSS with mobile-first approach
- **Notifications**: Toast notifications and real-time alerts

## 🏗️ Architecture

### Service Integration
- **API Gateway**: HTTP REST API calls for standard operations
- **Socket Gateway**: WebSocket connections for real-time features
- **Authentication**: JWT tokens shared between HTTP and WebSocket

### Key Components
- **Auth Store**: Manages user authentication state
- **Socket Store**: Handles WebSocket connection and real-time events
- **Game Store**: Manages game rooms and game state
- **API Client**: Centralized HTTP client with token management
- **Socket Service**: WebSocket client with reconnection logic

## 🛠️ Setup

### Prerequisites
- Node.js 18+
- npm 8+
- Running API Gateway service (port 3000)
- Running Socket Gateway service (port 3001)

### Installation

1. **Clone and navigate to client directory**
   ```bash
   cd client
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.development
   ```

4. **Start development server**
   ```bash
   npm run dev
   ```

The client will be available at `http://localhost:3000`

## 🔧 Configuration

### Environment Variables
```env
# API Configuration
VITE_API_BASE_URL=http://localhost:3000
VITE_SOCKET_URL=http://localhost:3001

# Feature Flags
VITE_ENABLE_CHAT=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_DEBUG=true
```

### Service URLs
- **API Gateway**: `http://localhost:3000/api/v1`
- **Socket Gateway**: `http://localhost:3001`

## 📱 Usage Examples

### Authentication Flow
```typescript
// Login
const { login } = useAuthStore();
await login({ username: 'player1', password: 'password' });

// Auto-connect to socket after login
const { connect } = useSocketStore();
await connect();
```

### Room Management
```typescript
// Create room
const { createRoom } = useGameStore();
const room = await createRoom({
  name: 'My Game Room',
  gameType: 'PRIZEWHEEL',
  betAmount: 100,
  maxPlayers: 8,
});

// Join room via socket
const { joinRoom } = useSocketStore();
await joinRoom(room.id);
```

### Real-time Events
```typescript
// Listen for game events
socketService.on('game_started', (data) => {
  console.log('Game started:', data);
});

// Set player ready status
const { setPlayerReady } = useSocketStore();
await setPlayerReady(roomId, true);
```

## 🎮 Game Features

### Supported Game Types
- **PRIZEWHEEL**: Spin-the-wheel style games
- **AMIDAKUJI**: Japanese ladder lottery games

### Real-time Features
- **Player Join/Leave**: Live updates when players join or leave rooms
- **Ready Status**: Real-time ready/not ready status updates
- **Game State**: Live game progress and state changes
- **Notifications**: System and game notifications
- **Connection Status**: Real-time connection state monitoring

## 🧪 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
npm test             # Run tests
npm run format       # Format code with Prettier
```

### Code Structure
```
src/
├── components/          # Reusable UI components
│   ├── Auth/           # Authentication components
│   ├── Game/           # Game-related components
│   ├── Layout/         # Layout components
│   └── UI/             # Generic UI components
├── pages/              # Page components
├── services/           # API and Socket services
├── store/              # State management
├── types/              # TypeScript type definitions
├── config/             # Configuration files
└── utils/              # Utility functions
```

## 🔌 Integration Points

### API Gateway Integration
- **Authentication**: `/api/v1/auth/*`
- **User Management**: `/api/v1/users/*`
- **Room Management**: `/api/v1/rooms/*`
- **Game Data**: `/api/v1/games/*`

### Socket Gateway Integration
- **Connection**: WebSocket connection with JWT auth
- **Room Events**: Join, leave, ready status
- **Game Events**: Start, update, finish
- **System Events**: Notifications, errors

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Environment Configuration
Set production environment variables:
```env
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_SOCKET_URL=https://socket.yourdomain.com
VITE_DEBUG=false
```

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Use TypeScript for type safety
3. Write tests for new features
4. Update documentation as needed

## 📄 License

This project is part of the XZ Game platform.
